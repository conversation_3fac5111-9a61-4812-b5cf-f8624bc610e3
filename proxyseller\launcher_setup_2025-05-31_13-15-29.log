2025-05-31 13:15:29 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-31 13:15:29 [DEBUG] Checking for Python installation...
2025-05-31 13:15:30 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-31 13:15:30 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-31 13:15:30 [DEBUG] Checking for package: selenium
2025-05-31 13:15:31 [DEBUG] Package 'selenium' found.
2025-05-31 13:15:31 [DEBUG] Checking for package: webdriver_manager
2025-05-31 13:15:32 [DEBUG] Package 'webdriver_manager' found.
2025-05-31 13:15:32 [DEBUG] Checking for package: requests
2025-05-31 13:15:33 [DEBUG] Package 'requests' found.
2025-05-31 13:15:33 [DEBUG] Checking for package: configparser
2025-05-31 13:15:34 [DEBUG] Package 'configparser' found.
2025-05-31 13:15:34 [INFO] All required Python packages seem to be installed.
2025-05-31 13:15:34 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-31 13:15:34 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-31 13:15:34 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-31 13:15:48 [INFO] Num Containers override: 3
2025-05-31 13:15:58 [INFO] Launching main Python script...
2025-05-31 13:15:58 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py" --num-containers 3
