2025-05-13 20:29:01 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.0 ---
2025-05-13 20:29:01 [WARNING] Not running as Administrator. Python package installation (pip) might require it if not in user context or if system-wide install is attempted.
2025-05-13 20:29:01 [DEBUG] Checking for Python installation...
2025-05-13 20:29:01 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-13 20:29:01 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 20:29:01 [DEBUG] Checking for package: selenium
2025-05-13 20:29:01 [FATAL] A critical error occurred in the PowerShell launcher: Cannot validate argument on parameter 'RedirectStandardError'. The argument is null or empty. Provide an argument that is not null or empty, and then try the command again.
2025-05-13 20:29:01 [DEBUG] Stack Trace: at Check-And-Install-PythonPackages, C:\main\proxyseller\start_proxy_setup.ps1: line 160
at <ScriptBlock>, C:\main\proxyseller\start_proxy_setup.ps1: line 463
