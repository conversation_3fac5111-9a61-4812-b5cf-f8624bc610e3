2025-05-13 21:00:39 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2 ---
2025-05-13 21:00:39 [WARNING] Not running as Administrator. Python package installation (pip) might require it if not in user context or if system-wide install is attempted.
2025-05-13 21:00:39 [DEBUG] Checking for Python installation...
2025-05-13 21:00:39 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-13 21:00:39 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:00:39 [DEBUG] Checking for package: selenium
2025-05-13 21:00:40 [DEBUG] Package 'selenium' found.
2025-05-13 21:00:40 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:00:41 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:00:41 [DEBUG] Checking for package: requests
2025-05-13 21:00:42 [DEBUG] Package 'requests' found.
2025-05-13 21:00:42 [DEBUG] Checking for package: configparser
2025-05-13 21:00:43 [DEBUG] Package 'configparser' found.
2025-05-13 21:00:43 [INFO] All required Python packages seem to be installed.
2025-05-13 21:00:43 [DEBUG] Initializing default configuration parameters for launcher...
2025-05-13 21:00:43 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read its contents for base settings.
2025-05-13 21:00:43 [DEBUG] Prompting user for parameters to pass to Python script.
2025-05-13 21:01:09 [INFO] Number of Containers override: 3
2025-05-13 21:01:16 [INFO] Launching main Python script...
2025-05-13 21:01:16 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py" --num-containers 3
2025-05-13 21:01:17 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-00-39.log
2025-05-13 21:01:17 [ERROR] Python script exited with code 5.
2025-05-13 21:01:17 [INFO] Launcher script execution finished. Final Exit Code: 5
