{"version": 2, "mode": "accessible-containers", "accessibleContainersDefaultProxyId": "direct-default", "containersDefaultProxyId": "direct-container-default", "containers": {"firefox-container-6": {"proxyId": "proxy-for-container-6", "active": true}, "firefox-container-7": {"proxyId": "proxy-for-container-7", "active": true}, "firefox-container-8": {"proxyId": "proxy-for-container-8", "active": true}, "firefox-container-9": {"proxyId": "proxy-for-container-9", "active": true}, "firefox-container-10": {"proxyId": "proxy-for-container-10", "active": true}}, "proxies": [{"id": "direct-default", "title": "Direct (<PERSON><PERSON><PERSON>)", "type": "direct", "color": "#666666", "active": true, "notes": ""}, {"id": "direct-container-default", "title": "Direct (Container De<PERSON>ult)", "type": "direct", "color": "#777777", "active": true, "notes": ""}, {"id": "proxy-for-container-6", "title": "ProxySeller #01 (US Idaho Boise CenturyLink)", "notes": "Managed by Script. Proxy: res.proxy-seller.com:10000 (HTTP) from list 'US Idaho Boise CenturyLink' Geo: Unknown, Unknown (from title), Unknown", "type": "http", "address": "res.proxy-seller.com", "port": 10000, "username": "5a6fd34a2f3fc8a6", "password": "RNW78Fm5", "proxyDNS": false, "active": true, "color": "red"}, {"id": "proxy-for-container-7", "title": "ProxySeller #02 (US Alabama Abbeville Brightspeed Fiber)", "notes": "Managed by Script. Proxy: res.proxy-seller.com:10000 (HTTP) from list 'US Alabama Abbeville Brightspeed Fiber' Geo: Unknown, Unknown (from title), Unknown", "type": "http", "address": "res.proxy-seller.com", "port": 10000, "username": "2fa3b3e6406b7058", "password": "RNW78Fm5", "proxyDNS": false, "active": true, "color": "orange"}, {"id": "proxy-for-container-8", "title": "ProxySeller #03 (US Maine Calais Pioneer Broadband)", "notes": "Managed by Script. Proxy: res.proxy-seller.com:10000 (HTTP) from list 'US Maine Calais Pioneer Broadband' Geo: Unknown, Unknown (from title), Unknown", "type": "http", "address": "res.proxy-seller.com", "port": 10000, "username": "39e07e0ba58ba3e2", "password": "RNW78Fm5", "proxyDNS": false, "active": true, "color": "yellow"}, {"id": "proxy-for-container-9", "title": "ProxySeller #04 (US Arizona)", "notes": "Managed by Script. Proxy: res.proxy-seller.com:10000 (HTTP) from list 'US Arizona' Geo: Unknown, Unknown (from title), Unknown", "type": "http", "address": "res.proxy-seller.com", "port": 10000, "username": "084391593736b8c3", "password": "RNW78Fm5", "proxyDNS": false, "active": true, "color": "blue"}, {"id": "proxy-for-container-10", "title": "ProxySeller #05 (US)", "notes": "Managed by Script. Proxy: res.proxy-seller.com:10000 (HTTP) from list 'US' Geo: Unknown, Unknown (from title), Unknown", "type": "http", "address": "res.proxy-seller.com", "port": 10000, "username": "a3c2f4780330961e", "password": "RNW78Fm5", "proxyDNS": false, "active": true, "color": "purple"}], "logging": {"active": true, "maxSize": 500}, "settingsSignature": "", "settings": {"sync": false, "autoUpdate": false, "container": true, "incognito": true, "quickadd": {"active": true, "color": "#008800", "isSocksDNS": false, "port": "", "scheme": "http", "title": "QuickAdd Proxy", "type": 1, "address": "", "username": "", "password": "", "id": "quickadd-default"}, "showDesktopNotifications": true}, "timestamp": 1747220729074}