"""
main_entry.py - Main orchestration and CLI for ProxySeller Firefox Container Setup
"""
import sys
import argparse
from pathlib import Path
from core_types_and_logging import AppConfig, setup_structlog

def main_entry():
    # Parse CLI arguments
    parser = argparse.ArgumentParser(
        description="Setup Firefox containers with ProxySeller proxies.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("-k", "--api-key", help="ProxySeller API key")
    parser.add_argument("-n", "--num-containers", type=int, help="Number of containers/proxies")
    parser.add_argument("-t", "--proxy-type", choices=["http", "socks5"], help="Proxy type")
    parser.add_argument("-p", "--profile", help="Path to Firefox profile directory")
    parser.add_argument("--skip-import", action="store_true", help="Skip FoxyProxy import")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], help="Console log level")
    parser.add_argument("--diagnostic-api-dump", metavar="DIR_PATH", help="Dir to dump API responses & exit")
    parser.add_argument("--test-api-key", action="store_true", help="Test API key & exit")
    parser.add_argument("--prompt-overrides", action="store_true", help="Interactively prompt for all config overrides.")
    parser.add_argument("--foxyproxy-uuid", help="Manually provide FoxyProxy Internal UUID.")
    cli_args = parser.parse_args()

    app_config = AppConfig(cli_args=cli_args)
    log_file = Path(app_config.LOG_FILE_NAME).resolve()
    slogger = setup_structlog(app_config.log_level, log_file, app_config)

    app_config.prompt_for_global_overrides()

    import logging
    current_stdlib_level = logging.getLogger().getEffectiveLevel()
    new_config_level_val = getattr(logging, app_config.log_level, logging.INFO)
    if current_stdlib_level != new_config_level_val:
        slogger.info("Log level changed interactively, re-adjusting stdlib root logger level.",
                     old_level=logging.getLevelName(current_stdlib_level), new_level=app_config.log_level)
        logging.getLogger().setLevel(new_config_level_val)

    slogger.info("Final effective configuration.",
                 api_key_set=bool(app_config.api_key),
                 num_containers=app_config.num_containers,
                 proxy_type=app_config.proxy_type,
                 log_level=app_config.log_level,
                 profile_path=app_config.firefox_profile_path or "Autodetect",
                 skip_import=app_config.skip_import,
                 foxy_uuid=app_config.foxyproxy_uuid_manual or "Autodetect/Prompt")

    # Diagnostic dump or API key test (exit after)
    if getattr(cli_args, "diagnostic_api_dump", False):
        from main import diagnostic_api_test_and_dump
        diagnostic_api_test_and_dump(app_config.api_key, cli_args.diagnostic_api_dump)
        sys.exit(0)
    if getattr(cli_args, "test_api_key", False):
        from main import test_api_key_and_config
        test_api_key_and_config(app_config.api_key)
        sys.exit(0)

    exit_code = 1
    try:
        from main import ProxyContainerSetup
        slogger.info("Starting ProxySeller Firefox Setup Tool", tool_version="Refactored-2.0")
        setup_manager = ProxyContainerSetup(app_config)
        if setup_manager.run():
            slogger.info("Main setup process completed successfully.")
            exit_code = 0
        else:
            slogger.error("Main setup process reported failure or incomplete execution.")
    except Exception as e_main_fatal:
        slogger.critical("FATAL ERROR in main execution", error=str(e_main_fatal), exc_info=True)
        print(f"\nFATAL ERROR: {e_main_fatal}\nCheck '{log_file}' for details.\n")
        exit_code = 4
    finally:
        slogger.info("Initiating final script shutdown sequence...")
        logging.shutdown()
        print(f"\nPython script execution process finished. Final exit code: {exit_code}")
        sys.exit(exit_code)
