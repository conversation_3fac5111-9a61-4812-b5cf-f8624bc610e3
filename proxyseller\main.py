#!/usr/bin/env python3
# pylint: disable=too-many-lines
"""Automates Firefox container setup with ProxySeller proxies."""

import argparse
import configparser
import json
import logging
import os
import random
import re  # For SensitiveDataFormatter
import subprocess
import sys
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path

import requests  # For public IP check


# --- Sensitive Data Formatter ---
class SensitiveDataFormatter(logging.Formatter):
    """Formatter that masks sensitive data in log records."""

    def __init__(self, fmt=None, datefmt=None, style="%", sensitive_patterns=None):
        super().__init__(fmt, datefmt, style)
        self.sensitive_patterns = sensitive_patterns or [
            (
                re.compile(
                    # Adjusted length for typical API keys, also made it more generic for various key formats
                    r"([\"']?(?:api_)?key[\"']?\s*[:=]\s*[\"']?)([a-zA-Z0-9\-_/.]{20,})([\"']?)"
                ),
                r"\1***MASKED_API_KEY***\3",
            ),
            (
                re.compile(
                    r"([\"']?password[\"']?\s*[:=]\s*[\"']?)([^\"'\s]+)([\"']?)"
                ),
                r"\1***MASKED_PASS***\3",
            ),
            (
                re.compile(r"([a-zA-Z0-9\._%+-]+):([^@\s]+)@([\w\.-]+:\d+)"),
                r"***USER***:***PASS***@\3",
            ),
        ]

    def format(self, record):
        original_message = super().format(record)
        masked_message = original_message
        for pattern, replacement in self.sensitive_patterns:
            masked_message = pattern.sub(replacement, masked_message)
        return masked_message


# --- Setup Logging ---
LOG_FORMAT_STRING = (
    "%(asctime)s - %(levelname)s - [%(name)s:%(funcName)s:%(lineno)d] - %(message)s"
)
sensitive_formatter = SensitiveDataFormatter(LOG_FORMAT_STRING)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # Default to DEBUG, console handler will filter

stream_handler = logging.StreamHandler(sys.stdout)
stream_handler.setLevel(logging.INFO)  # Console level can be overridden by args
stream_handler.setFormatter(sensitive_formatter)
logger.addHandler(stream_handler)

log_file_path = Path("proxy_setup.log").resolve()
try:
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    file_handler = logging.FileHandler(log_file_path, mode="w", encoding="utf-8")
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(sensitive_formatter)
    logger.addHandler(file_handler)
    print(f"INFO: Detailed logs will be written to: {log_file_path}")
except IOError as e_io_log:
    print(f"ERROR: Could not open log file {log_file_path} for writing: {e_io_log}")


# --- Selenium and WebDriver Manager ---
try:
    from selenium import webdriver
    from selenium.common.exceptions import (
        ElementNotInteractableException,
        NoAlertPresentException,
        NoSuchElementException,
        SessionNotCreatedException,
        StaleElementReferenceException,
        TimeoutException,
        WebDriverException,
    )
    from selenium.webdriver.common.by import By
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.webdriver.firefox.service import Service
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.support.ui import WebDriverWait

    os.environ["WDM_LOG"] = str(logging.WARNING)  # Suppress WDM info logs
    from webdriver_manager.firefox import GeckoDriverManager
except ImportError as e_selenium:
    logger.critical(
        "Selenium or Webdriver-Manager not installed. "
        "Please run: pip install selenium webdriver-manager requests configparser"
    )
    sys.exit(f"Missing dependency: {e_selenium}")


# --- Proxy Seller API SDK ---
PROXY_SELLER_API_CLIENT_CLASS = None
try:
    # Attempt to import from installed package first
    from proxy_seller_user_api import Api as ProxySellerAPIClient

    PROXY_SELLER_API_CLIENT_CLASS = ProxySellerAPIClient
    logger.info("Successfully imported ProxySellerAPIClient from installed package.")
except ImportError:
    logger.warning(
        "Could not import ProxySellerAPIClient from installed package. Trying local SDK path..."
    )
    # Fallback to local SDK path (as in original script)
    PATH1_STR = str(Path(__file__).parent / "user-api-python")
    PATH2_STR = str(Path(__file__).parent.parent / "user-api-python")
    sdk_to_try = None
    if Path(PATH1_STR).is_dir():
        sdk_to_try = PATH1_STR
    elif Path(PATH2_STR).is_dir():
        sdk_to_try = PATH2_STR

    if sdk_to_try:
        sys.path.insert(0, str(Path(sdk_to_try).resolve()))
        logger.debug(
            "Adding local SDK path to sys.path: %s", Path(sdk_to_try).resolve()
        )
        try:
            from proxy_seller_user_api import Api as ProxySellerAPIClient

            PROXY_SELLER_API_CLIENT_CLASS = ProxySellerAPIClient
            logger.info(
                "Successfully imported ProxySellerAPIClient from local SDK path: %s",
                sdk_to_try,
            )
        except ImportError as e_sdk_local:
            logger.error(
                "Failed to import ProxySellerAPIClient from local SDK path %s: %s",
                sdk_to_try,
                e_sdk_local,
            )
    else:
        logger.error(
            "Local SDK directory 'user-api-python' not found relative to script or its parent. "
            f"Looked for: {PATH1_STR} and {PATH2_STR}"
        )

if not PROXY_SELLER_API_CLIENT_CLASS:
    logger.critical(
        "Proxy Seller API SDK could not be loaded. "
        "Please install it (`pip install proxy-seller-user-api`) or ensure 'user-api-python' directory is correctly placed."
    )
    # Depending on how strictly other parts need it, you might exit here.
    # For now, we'll let it proceed, and init_api_client will handle its absence.


# --- Custom Exceptions ---
class ProxySellerAPIError(Exception):
    """Exception raised for errors interacting with the ProxySeller API"""

    def __init__(self, message, sdk_error=None):  # Added sdk_error to store original
        self.message = message
        self.sdk_error = sdk_error
        super().__init__(self.message)


class FoxyProxySetupError(Exception):
    pass


class ProfileError(Exception):
    pass


# --- ProxySeller Resident Proxy Manager (using SDK) ---
class ProxySellerResidentManagerSDK:
    """Manages resident proxies from Proxy-Seller using the official SDK."""

    def __init__(self, api_client_instance):
        if not api_client_instance:
            # This should not happen if init_api_client ensures api_client is set
            raise ValueError(
                "ProxySellerAPIClient instance is required for ProxySellerResidentManagerSDK."
            )
        self.api_client = api_client_instance
        self.package_info = None
        self.all_locations = None  # Stores data from api_client.residentGeo()

    def get_package_info_sdk(self):
        logger.info("Fetching resident proxy package information via SDK...")
        try:
            # SDK method: residentPackage()
            self.package_info = self.api_client.residentPackage()
            if self.package_info:
                logger.info(
                    "SDK Package Info: Active: %s, Traffic Limit: %s, Usage: %s, Left: %s",
                    self.package_info.get("is_active"),
                    self.package_info.get("traffic_limit"),
                    self.package_info.get("traffic_usage"),
                    # Use traffic_left_sub if available, otherwise traffic_left (based on API docs)
                    self.package_info.get(
                        "traffic_left_sub", self.package_info.get("traffic_left")
                    ),
                )
            return self.package_info
        except ValueError as e_sdk:  # SDK raises ValueError for API errors
            raise ProxySellerAPIError(
                f"SDK error fetching package info: {e_sdk}", sdk_error=e_sdk
            ) from e_sdk
        except Exception as e:
            # Catch any other unexpected error during the SDK call
            raise ProxySellerAPIError(
                f"Unexpected error fetching package info via SDK: {e}"
            ) from e

    def get_all_locations_sdk(self):
        if not self.all_locations:  # Cache the result
            logger.info(
                "Fetching all available resident proxy geo-locations via SDK..."
            )
            try:
                # SDK method: residentGeo()
                geo_data_raw = (
                    self.api_client.residentGeo()
                )  # This returns the raw response content

                # The SDK's request method might return a string representation of JSON if it's a direct list
                if isinstance(geo_data_raw, (str, bytes)):
                    if isinstance(geo_data_raw, bytes):
                        geo_data_raw = geo_data_raw.decode("utf-8")  # Ensure string
                    try:
                        self.all_locations = json.loads(geo_data_raw)
                        logger.debug(
                            "Parsed geo_data_raw string to list via json.loads()."
                        )
                    except json.JSONDecodeError as e_json_geo:
                        logger.error(
                            "Failed to parse geo_data_raw string from SDK as JSON: %s",
                            e_json_geo,
                        )
                        logger.debug(
                            "Raw geo_data_raw string from SDK: %s", geo_data_raw[:500]
                        )  # Log snippet
                        self.all_locations = []  # Prevent further errors if parsing fails
                        raise ProxySellerAPIError(
                            f"SDK returned unparsable string for geo data: {e_json_geo}"
                        )

                elif isinstance(geo_data_raw, list):
                    self.all_locations = geo_data_raw  # Assume SDK returned parsed list
                else:
                    logger.error(
                        "Unexpected data type for geo_data from SDK: %s",
                        type(geo_data_raw),
                    )
                    self.all_locations = []
                    raise ProxySellerAPIError(
                        f"SDK returned unexpected type for geo data: {type(geo_data_raw)}"
                    )

                if self.all_locations:
                    logger.info(
                        f"SDK: Successfully fetched {len(self.all_locations)} countries for resident proxies."
                    )
            except ValueError as e_sdk:
                raise ProxySellerAPIError(
                    f"SDK error fetching geo locations: {e_sdk}", sdk_error=e_sdk
                ) from e_sdk
            except Exception as e:
                raise ProxySellerAPIError(
                    f"Unexpected error fetching geo locations via SDK: {e}"
                ) from e
        return self.all_locations

    def get_existing_lists_sdk(self):
        logger.info("Fetching existing resident proxy lists via SDK...")
        try:
            # SDK method: residentList()
            # Based on SDK __init__.py, residentList() calls self.request('GET', 'resident/lists')
            # The self.request method returns data['data'] if status is 'success'.
            # The API doc for /resident/lists shows data: { items: [...] }
            # So, list_response should be {"items": [...] }
            list_response = self.api_client.residentList()

            if isinstance(list_response, dict) and "items" in list_response:
                existing_lists = list_response["items"]
                logger.info(
                    f"SDK: Found {len(existing_lists)} existing resident proxy lists."
                )
                return existing_lists
            elif isinstance(
                list_response, list
            ):  # Fallback if SDK was changed to return list directly
                logger.info(
                    f"SDK: Found {len(list_response)} existing resident proxy lists (direct list)."
                )
                return list_response
            else:
                # This case indicates an unexpected response structure from the SDK/API.
                logger.error(
                    f"SDK: Unexpected response format for residentList: {type(list_response)}. Expected dict with 'items' key or a direct list."
                )
                logger.debug("Full residentList response from SDK: %s", list_response)
                return []  # Return empty list to prevent crashes downstream
        except ValueError as e_sdk:  # SDK's way of signaling API error
            raise ProxySellerAPIError(
                f"SDK error fetching existing lists: {e_sdk}", sdk_error=e_sdk
            ) from e_sdk
        except Exception as e:  # Other unexpected errors
            raise ProxySellerAPIError(
                f"Unexpected error fetching existing lists via SDK: {e}"
            ) from e

    def download_proxy_for_list_sdk(self, list_id, proxy_protocol="http"):
        logger.info(
            f"Downloading proxy string for list ID: {list_id}, protocol: {proxy_protocol} via SDK..."
        )
        api_proto = "https" if proxy_protocol == "http" else "socks5"
        try:
            # SDK method: proxyDownload(type, ext=None, proto=None, listId=None)
            # Returns raw text content for downloads.
            proxy_data_text = self.api_client.proxyDownload(
                type="resident",
                listId=list_id,
                proto=api_proto,
                ext="txt",  # Ensure we get text format
            )
            if isinstance(proxy_data_text, bytes):  # SDK might return bytes
                proxy_data_text = proxy_data_text.decode("utf-8")

            if isinstance(proxy_data_text, str) and proxy_data_text.strip():
                # API returns format like: login:password@127.0.0.1:80;
                # We want the part before the semicolon if it exists
                proxy_string = proxy_data_text.split(";")[0].strip()
                if proxy_string:
                    logger.info(
                        f"SDK: Downloaded proxy string for list {list_id}: {proxy_string[:20]}..."
                    )  # Log partial
                    return proxy_string
                else:
                    logger.error(
                        f"SDK: Empty proxy string received for list {list_id} after splitting/stripping."
                    )
            else:
                logger.error(
                    f"SDK: Failed to download proxy string for list {list_id}. Response: {proxy_data_text}"
                )
            return None
        except ValueError as e_sdk:
            raise ProxySellerAPIError(
                f"SDK error downloading proxy for list {list_id}: {e_sdk}",
                sdk_error=e_sdk,
            ) from e_sdk
        except Exception as e:
            raise ProxySellerAPIError(
                f"Unexpected error downloading proxy for list {list_id} via SDK: {e}"
            ) from e


# --- Main Class ---
class ProxyContainerSetup:  # pylint: disable=too-many-instance-attributes
    """Handles setup of Firefox containers and FoxyProxy using ProxySeller."""

    def __init__(
        self, api_key, num_containers=5, proxy_type="http", firefox_profile_path=None
    ):
        logger.info("Initializing ProxyContainerSetup...")
        self.api_key = api_key
        self.num_containers = num_containers
        self.proxy_type = proxy_type.lower()  # 'http' or 'socks5'
        self.api_client = None  # This will be the SDK instance
        self.resident_manager_sdk = (
            None  # This will be an instance of ProxySellerResidentManagerSDK
        )

        self.containers = []
        self.proxies = []  # This will store the formatted resident proxies
        self.firefox_profile_path = None
        self.foxyproxy_internal_id = None
        self.public_ip = None

        logger.info("Attempting to determine Firefox profile path...")
        if firefox_profile_path and Path(firefox_profile_path).is_dir():
            self.firefox_profile_path = Path(firefox_profile_path).resolve()
            logger.info(
                "Using user-specified profile path: %s", self.firefox_profile_path
            )
        else:
            if firefox_profile_path:
                logger.warning(
                    "User-specified profile path '%s' not found or not a directory. "
                    "Attempting autodetection.",
                    firefox_profile_path,
                )
            self._find_firefox_profile()

        if not self.firefox_profile_path or not self.firefox_profile_path.is_dir():
            profile_path_str = (
                str(self.firefox_profile_path) if self.firefox_profile_path else "None"
            )
            msg = (
                f"Firefox profile path could not be determined or is invalid: '{profile_path_str}'. "
                "Please ensure Firefox is installed or specify a valid path using the "
                "-p option."  # Removed 'in the launcher' for generality
            )
            raise ProfileError(msg)
        logger.info(
            "Using Firefox profile directory: %s", str(self.firefox_profile_path)
        )

        self.container_colors = [
            "blue",
            "turquoise",
            "green",
            "yellow",
            "orange",
            "red",
            "pink",
            "purple",
        ]
        self.container_icons = [
            "fingerprint",
            "briefcase",
            "dollar",
            "cart",
            "gift",
            "food",
            "vacation",
            "pet",
            "tree",
            "chill",
            "circle",
        ]

    def _find_firefox_profile(self):
        logger.debug("Starting automatic Firefox profile detection.")
        found_profile = False
        home_dir = Path.home()
        possible_locations = [
            home_dir / "AppData" / "Roaming" / "Mozilla" / "Firefox",  # Windows
            home_dir / ".mozilla" / "firefox",  # Linux
            home_dir / "Library" / "Application Support" / "Firefox",  # macOS
            home_dir
            / "AppData"
            / "Local"
            / "Mozilla"
            / "Firefox",  # Windows alternative
        ]
        for firefox_dir in possible_locations:
            if not firefox_dir.is_dir():
                continue
            profiles_file = firefox_dir / "profiles.ini"
            if profiles_file.is_file():
                config = configparser.ConfigParser(interpolation=None)
                try:
                    with open(profiles_file, "r", encoding="utf-8") as f:
                        config.read_file(f)
                except (configparser.Error, IOError) as e_cfg:
                    logger.warning(
                        "Could not parse profiles.ini in %s: %s", firefox_dir, e_cfg
                    )
                    continue

                profile_to_use = None
                # Prioritize Default=1 profile
                for section in config.sections():
                    if section.startswith("Profile") and config.getboolean(
                        section, "Default", fallback=False
                    ):
                        profile_to_use = section
                        break
                # Fallback to first profile with a Path if no Default=1 found
                if not profile_to_use:
                    for section in config.sections():
                        if (
                            section.startswith("Profile")
                            or section.startswith(
                                "Install"
                            )  # Installs can also have Default
                        ) and config.has_option(section, "Path"):
                            profile_to_use = section
                            break

                if profile_to_use:
                    path_str = config.get(profile_to_use, "Path", fallback=None)
                    is_relative = config.getboolean(
                        profile_to_use, "IsRelative", fallback=True
                    )

                    if path_str:
                        potential_path = (
                            (firefox_dir / path_str).resolve()
                            if is_relative
                            else Path(path_str).resolve()
                        )
                        if potential_path.is_dir():
                            self.firefox_profile_path = potential_path
                            logger.info(
                                "Profile path determined from profiles.ini ('%s' in %s): %s",
                                profile_to_use,
                                profiles_file,
                                self.firefox_profile_path,
                            )
                            found_profile = True
                            break  # Found profile, exit outer loop
                        else:
                            logger.warning(
                                "Path '%s' from profiles.ini (section '%s') is not a "
                                "valid directory.",
                                potential_path,
                                profile_to_use,
                            )
                    else:
                        logger.warning(
                            "Section '%s' in profiles.ini is missing 'Path' attribute.",
                            profile_to_use,
                        )
            else:
                logger.debug("profiles.ini not found in %s", firefox_dir)

            # If profiles.ini method failed for this firefox_dir, try common patterns
            if not found_profile:
                logger.debug(
                    "profiles.ini method failed for %s, trying glob for '*.default*' dirs.",
                    firefox_dir,
                )
                profiles_glob = sorted(
                    (p for p in firefox_dir.glob("*.default*") if p.is_dir()),
                    key=lambda p_item: p_item.stat().st_mtime,
                    reverse=True,
                )
                release_profile = next(
                    (
                        p_item
                        for p_item in profiles_glob
                        if "release" in p_item.name.lower()
                    ),
                    None,
                )
                target_profile_dir = (
                    release_profile
                    if release_profile
                    else (profiles_glob[0] if profiles_glob else None)
                )

                if target_profile_dir:
                    self.firefox_profile_path = target_profile_dir.resolve()
                    logger.info(
                        "Found profile directory by pattern ('%s' in %s): %s",
                        target_profile_dir.name,
                        firefox_dir,
                        self.firefox_profile_path,
                    )
                    found_profile = True
                    break  # Found profile, exit outer loop
        if not found_profile:
            logger.warning(
                "Could not automatically determine Firefox profile path after checking all "
                "locations. Please ensure Firefox is installed and has a profile, "
                "or specify the path manually."
            )

    def init_api_client(self):
        """Initialize the ProxySeller API SDK client and the resident manager."""
        logger.info("Initializing ProxySeller API SDK client...")
        if not PROXY_SELLER_API_CLIENT_CLASS:
            logger.error(
                "ProxySellerAPIClient class (SDK) was not loaded. "
                "Cannot initialize API client."
            )
            return False
        try:
            self.api_client = PROXY_SELLER_API_CLIENT_CLASS({"key": self.api_key})
            # The SDK's ping() method returns a timestamp (float or int) on success.
            ping_response = self.api_client.ping()
            logger.debug("SDK API ping response: %s", ping_response)
            if not isinstance(ping_response, (int, float)):
                logger.warning(
                    "SDK API ping did not return a numeric timestamp: %s. "
                    "API key might be invalid or API unresponsive. Attempting to proceed.",
                    ping_response,
                )
                # Allow to continue, other calls will fail if key is bad
            else:
                logger.info(
                    "Successfully pinged Proxy-Seller API via SDK (timestamp: %s).",
                    ping_response,
                )

            # Initialize Resident Manager with the SDK client instance
            self.resident_manager_sdk = ProxySellerResidentManagerSDK(self.api_client)
            # Test connectivity for resident proxies by fetching package info
            if not self.resident_manager_sdk.get_package_info_sdk():
                logger.error(
                    "Failed to get resident proxy package info using SDK. "
                    "Check API key and ensure you have an active resident proxy plan."
                )
                return False
            logger.info(
                "Successfully initialized resident proxy manager with SDK and fetched package info."
            )
            return True
        except (
            ValueError
        ) as e_sdk_init:  # SDK raises ValueError for config errors or API errors
            logger.error(
                "Failed to initialize Proxy-Seller SDK client or resident manager: %s",
                e_sdk_init,
            )
            return False
        except Exception as e_generic_init:  # Catch any other unexpected error
            logger.error(
                "Unexpected error initializing API client/manager: %s",
                e_generic_init,
                exc_info=True,
            )
            return False

    def fetch_proxies(self):
        """Fetch resident proxies using the ProxySellerResidentManagerSDK."""
        logger.info("Fetching resident proxies for %d containers using SDK...", self.num_containers)
        if not self.resident_manager_sdk:
            logger.error("Resident manager (SDK) not initialized. Cannot fetch proxies.")
            return False

        # ************************************************************************************
        # IMPORTANT: This script assumes you have ALREADY CREATED resident proxy lists
        # in your Proxy-Seller dashboard and configured them with US geo-targeting.
        # The SDK (v1.0.4) does not support creating new geo-targeted resident lists.
        # ************************************************************************************
        try:
            all_existing_lists = self.resident_manager_sdk.get_existing_lists_sdk()
            if not all_existing_lists:
                logger.error(
                    "No existing resident proxy lists found via SDK, or an error occurred fetching them. "
                    "Please ensure US-targeted lists are pre-configured in your Proxy-Seller dashboard."
                )
                return False

            # Debug log for the raw structure of all_existing_lists
            if logger.isEnabledFor(logging.DEBUG):
                import json
                logger.debug("All existing resident lists from SDK: %s", json.dumps(all_existing_lists, indent=2))

            us_targeted_lists = []
            for lst_idx, lst_item in enumerate(all_existing_lists):
                list_title = lst_item.get('title', f"ListID-{lst_item.get('id','N/A')}")
                geo_data = lst_item.get("geo", {})  # Default to empty dict if 'geo' is missing

                if logger.isEnabledFor(logging.DEBUG):
                    import json
                    logger.debug(f"List #{lst_idx+1} - Title: '{list_title}', Raw Geo data: {json.dumps(geo_data)}")

                is_us_list = False
                if isinstance(geo_data, dict):
                    country_code = geo_data.get("country", "").upper()
                    if country_code == "US" or country_code == "USA":
                        is_us_list = True
                        logger.info(f"List '{list_title}' identified as US based on geo dictionary: {geo_data}")
                    else:
                        logger.debug(f"List '{list_title}' geo dictionary country is '{country_code}', not US/USA.")
                elif isinstance(geo_data, list):
                    logger.warning(
                        f"List '{list_title}' (ID: {lst_item.get('id')}) has 'geo' data of unexpected type: list. Content: {json.dumps(geo_data)}. "
                        "Expected a dictionary. Cannot reliably determine US targeting for this list from this field. "
                        "Please check its configuration in the Proxy-Seller dashboard."
                    )
                    # Fallback: Check if the list's *title* contains "US" as a fallback
                    if "US" in list_title.upper() or "USA" in list_title.upper():
                        logger.warning(f"Fallback: List '{list_title}' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.")
                        lst_item['geo_processed_fallback'] = {"country": "US", "region": "Unknown (from title)", "city": "Unknown", "isp": "Unknown"}
                        is_us_list = True
                elif isinstance(geo_data, str):  # If geo is just a string "US" or "USA"
                    if geo_data.upper() == "US" or geo_data.upper() == "USA":
                        logger.info(f"List '{list_title}' identified as US based on geo string value: '{geo_data}'")
                        lst_item['geo_processed_fallback'] = {"country": geo_data.upper(), "region": "Unknown (from string)", "city": "Unknown", "isp": "Unknown"}
                        is_us_list = True
                    else:
                        logger.debug(f"List '{list_title}' geo string is '{geo_data}', not US/USA.")
                else:
                    logger.warning(f"List '{list_title}' has 'geo' data of unhandled type: {type(geo_data)}. Content: {geo_data}")

                if is_us_list:
                    us_targeted_lists.append(lst_item)

            if not us_targeted_lists:
                logger.error(
                    "No US-targeted resident proxy lists found after checking 'geo.country' and list titles. "
                    "Please ensure your lists in the Proxy-Seller dashboard are correctly configured for US geo-targeting "
                    "and that their 'geo' field is a dictionary with a 'country' key set to 'US' or 'USA'."
                )
                return False

            logger.info(
                f"Found {len(us_targeted_lists)} US-targeted resident lists after filtering. "
                f"Will attempt to use up to {self.num_containers} of them."
            )

            import random
            random.shuffle(us_targeted_lists)
            lists_to_use_for_proxies = us_targeted_lists[:self.num_containers]

            generated_proxies_sdk = []
            import re
            import time
            for i, list_data_item in enumerate(lists_to_use_for_proxies):
                list_id = list_data_item.get("id")
                list_title = list_data_item.get("title", f"ListID-{list_id}")
                geo_info_for_proxy = list_data_item.get('geo_processed_fallback', list_data_item.get("geo", {}))
                if not isinstance(geo_info_for_proxy, dict):
                    geo_info_for_proxy = {"country": "US", "region": "Unknown", "city": "Unknown", "isp": "Unknown"}

                logger.info(
                    f"Processing resident proxy for container {i+1}/{len(lists_to_use_for_proxies)} "
                    f"from list: '{list_title}' (ID: {list_id})"
                )

                proxy_string = self.resident_manager_sdk.download_proxy_for_list_sdk(
                    list_id=list_id,
                    proxy_protocol=self.proxy_type
                )

                if not proxy_string:
                    logger.error(f"Failed to download proxy string for list ID {list_id} (Title: {list_title}). Skipping.")
                    continue

                match = re.match(r"([^:]+):([^@]+)@([^:]+):(\d+)", proxy_string)
                if not match:
                    logger.error(f"Could not parse downloaded proxy string: '{proxy_string}' for list '{list_title}'")
                    continue

                username, password, ip, port_str = match.groups()
                try:
                    port = int(port_str)
                except ValueError:
                    logger.error(f"Invalid port in proxy string '{proxy_string}' for list '{list_title}'")
                    continue

                formatted_proxy = {
                    "ip": ip, "port": port, "username": username, "password": password,
                    "type": self.proxy_type, "id": f"resident_list_{list_id}",
                    "country": geo_info_for_proxy.get("country", "US"),
                    "state": geo_info_for_proxy.get("region"),
                    "city": geo_info_for_proxy.get("city"),
                    "isp": geo_info_for_proxy.get("isp"),
                    "status": "active", "title": list_title
                }
                generated_proxies_sdk.append(formatted_proxy)
                logger.info(
                    f"SDK: Configured resident proxy for container {i+1}: {ip}:{port} from list '{list_title}' "
                    f"(Geo: {geo_info_for_proxy.get('city', 'N/A')}, {geo_info_for_proxy.get('region', 'N/A')})"
                )
                time.sleep(0.5)

            self.proxies = generated_proxies_sdk
            actual_proxies_configured = len(self.proxies)
            if actual_proxies_configured < self.num_containers:
                logger.warning(
                    "Could only configure %d proxies out of %d initially requested. Adjusted num_containers.",
                    actual_proxies_configured, self.num_containers
                )
            self.num_containers = actual_proxies_configured

            if self.num_containers == 0:
                logger.error("No resident proxies could be configured. Cannot proceed.")
                return False

            logger.info(f"SDK: Successfully configured {len(self.proxies)} resident proxies.")
            return True

        except ProxySellerAPIError as e_sdk_api:
            logger.error(f"ProxySeller API Error during SDK proxy fetch: {e_sdk_api.message}", exc_info=False)
            if e_sdk_api.sdk_error and logger.isEnabledFor(logging.DEBUG):
                logger.debug("Original SDK error details: %s", e_sdk_api.sdk_error)
            return False
        except Exception as e_general:
            logger.error("Unexpected error fetching proxies via SDK: %s", e_general, exc_info=True)
            return False

    def get_existing_containers(self):
        """Get existing Firefox containers from containers.json in the profile."""
        logger.debug(
            "Looking for containers.json at: %s",
            self.firefox_profile_path / "containers.json",
        )
        containers_path = self.firefox_profile_path / "containers.json"
        default_structure = {"version": 4, "lastUserContextId": 0, "identities": []}

        if containers_path.is_file():
            try:
                with open(containers_path, "r", encoding="utf-8") as f:
                    containers_data = json.load(f)
                if (
                    not isinstance(containers_data, dict)
                    or "identities" not in containers_data
                    or not isinstance(containers_data["identities"], list)
                ):
                    logger.warning(
                        "Invalid format in containers.json (path: %s). Using default structure.",
                        containers_path,
                    )
                    return default_structure, []

                existing_containers_list = containers_data.get("identities", [])
                logger.info(
                    "Found %d existing containers in %s",
                    len(existing_containers_list),
                    containers_path,
                )
                return containers_data, existing_containers_list
            except (IOError, json.JSONDecodeError) as e_json:
                logger.error(
                    "Error reading/parsing %s: %s. Using default structure.",
                    containers_path,
                    e_json,
                )
                return default_structure, []
        else:
            logger.info(
                "No existing containers.json found at %s. Will create new structure.",
                containers_path,
            )
            return default_structure, []

    def create_or_update_containers(self):
        """Create or update Firefox container definitions in containers.json."""
        logger.info("Creating or updating Firefox container definitions...")
        try:
            if not isinstance(self.proxies, list):
                logger.error("Proxy list is not available. Cannot setup containers.")
                return False

            num_available_proxies = len(self.proxies)
            # If num_containers was adjusted by fetch_proxies, it reflects available proxies.
            # If no proxies, no containers needed from this script.
            if num_available_proxies == 0:
                logger.info(
                    "No proxies available. Skipping container JSON update as no containers can be mapped."
                )
                self.containers = []  # Ensure self.containers is empty
                self.num_containers = 0  # Reflect that no containers will be set up
                return True  # Allow process to continue if other actions are needed, but FoxyProxy will be skipped

            containers_data, existing_identities = self.get_existing_containers()
            proxyseller_managed_containers = []
            other_user_containers = []
            all_user_context_ids_in_json = set()
            max_existing_ps_number_suffix = 0

            for container_dict in existing_identities:
                if not isinstance(container_dict, dict):
                    continue  # Skip malformed

                context_id = container_dict.get("userContextId")
                if isinstance(context_id, int):
                    all_user_context_ids_in_json.add(context_id)

                name = container_dict.get("name", "")
                if name.startswith("ProxySeller #"):
                    try:
                        num_suffix_str = name.split("#")[-1]
                        if num_suffix_str.isdigit():  # Ensure it's a number
                            num_suffix = int(num_suffix_str)
                            max_existing_ps_number_suffix = max(
                                max_existing_ps_number_suffix, num_suffix
                            )
                            proxyseller_managed_containers.append(container_dict)
                        else:  # Malformed "ProxySeller #" name (e.g., "ProxySeller #abc")
                            other_user_containers.append(container_dict)
                    except (
                        ValueError,
                        IndexError,
                    ):  # Error splitting or converting to int
                        other_user_containers.append(container_dict)
                else:
                    other_user_containers.append(container_dict)

            proxyseller_managed_containers.sort(
                key=lambda c: int(c.get("name", "PS #9999").split("#")[-1])
                if c.get("name", "").startswith("ProxySeller #")
                and c.get("name", "PS #9999").split("#")[-1].isdigit()
                else float("inf")
            )

            logger.info(
                "Found %d existing 'ProxySeller #' containers. Max number suffix found: %d",
                len(proxyseller_managed_containers),
                max_existing_ps_number_suffix,
            )

            # We need one container definition for each *available* proxy from fetch_proxies
            target_proxyseller_container_definitions_needed = num_available_proxies

            new_container_definitions_to_create_count = max(
                0,
                target_proxyseller_container_definitions_needed
                - len(proxyseller_managed_containers),
            )

            ids_for_max_calc = list(all_user_context_ids_in_json)
            ids_for_max_calc.append(containers_data.get("lastUserContextId", 0))
            current_max_user_context_id = (
                max(ids_for_max_calc) if ids_for_max_calc else 0
            )
            next_available_id_for_new_container = current_max_user_context_id + 1

            newly_created_container_objects = []
            if new_container_definitions_to_create_count > 0:
                logger.info(
                    "Creating %d new container definitions in containers.json structure...",
                    new_container_definitions_to_create_count,
                )
                for i in range(new_container_definitions_to_create_count):
                    while (
                        next_available_id_for_new_container
                        in all_user_context_ids_in_json
                    ):
                        next_available_id_for_new_container += 1

                    new_container_def = {
                        "userContextId": next_available_id_for_new_container,
                        "public": True,
                        "icon": random.choice(self.container_icons),
                        "color": random.choice(self.container_colors),
                        "name": f"ProxySeller #{max_existing_ps_number_suffix + i + 1:02d}",
                        "accessKey": "",
                        "telemetryId": "",
                    }
                    newly_created_container_objects.append(new_container_def)
                    all_user_context_ids_in_json.add(
                        next_available_id_for_new_container
                    )
                    current_max_user_context_id = (
                        next_available_id_for_new_container  # Update for next iteration
                    )
                    # next_available_id_for_new_container will be incremented by the while loop or at start of next iteration

            containers_data["lastUserContextId"] = current_max_user_context_id
            # Combine all identities: user's existing non-PS, then sorted PS, then newly created PS
            containers_data["identities"] = (
                other_user_containers
                + proxyseller_managed_containers
                + newly_created_container_objects
            )

            containers_path = self.firefox_profile_path / "containers.json"
            logger.info("Writing updated container data to: %s", containers_path)
            try:
                containers_path.parent.mkdir(parents=True, exist_ok=True)
                with open(containers_path, "w", encoding="utf-8") as f:
                    json.dump(containers_data, f, indent=2, ensure_ascii=False)
            except IOError as e_io:
                logger.error("Failed to write containers.json: %s", e_io)
                # Potentially critical, but proceed with in-memory data if possible

            # self.containers should be the list of ProxySeller managed containers we will actually use,
            # limited by the number of available proxies.
            self.containers = (
                proxyseller_managed_containers + newly_created_container_objects
            )[:num_available_proxies]

            self.num_containers = len(
                self.containers
            )  # Update effective num_containers again based on what was defined and available for proxies

            logger.info(
                "Final list for setup: Using %d container definitions matched with %d proxies.",
                self.num_containers,
                num_available_proxies,
            )

            if self.num_containers == 0 and num_available_proxies > 0:
                logger.error(
                    "No 'ProxySeller #' container definitions were available or created for the existing proxies. "
                    "Cannot proceed with FoxyProxy setup for these proxies."
                )
                return False
            if self.num_containers == 0:  # Handles if num_available_proxies was also 0
                logger.error(
                    "Zero containers/proxies available after final adjustment. Cannot proceed with FoxyProxy setup."
                )
                return False  # No containers mean no FoxyProxy setup
            return True
        except Exception as e_cont_update:
            logger.exception("Error in create_or_update_containers: %s", e_cont_update)
            return False

    def generate_foxyproxy_config(self):
        """Generate FoxyProxy configuration JSON file for import."""
        logger.info("Generating FoxyProxy configuration...")
        try:
            # Ensure we only configure for pairs we actually have (proxies and container defs)
            num_pairs_to_configure = min(
                len(self.proxies) if self.proxies else 0,
                len(self.containers) if self.containers else 0,
            )

            if num_pairs_to_configure == 0:
                logger.error(
                    "Cannot generate FoxyProxy config: No proxy/container pairs available."
                )
                return None

            foxyproxy_config = {
                "version": 2,
                "mode": "accessible-containers",
                "accessibleContainersDefaultProxyId": "direct-default",
                "containersDefaultProxyId": "direct-container-default",
                "containers": {},  # This will be populated
                "proxies": [  # Default direct proxies
                    {
                        "id": "direct-default",
                        "title": "Direct (Default)",
                        "type": "direct",
                        "color": "#666666",
                        "active": True,
                        "notes": "",
                    },
                    {
                        "id": "direct-container-default",
                        "title": "Direct (Container Default)",
                        "type": "direct",
                        "color": "#777777",
                        "active": True,
                        "notes": "",
                    },
                ],
                "logging": {"active": True, "maxSize": 500},
                "settingsSignature": "",  # FoxyProxy fills this
                "settings": {  # Default FoxyProxy settings
                    "sync": False,
                    "autoUpdate": False,
                    "container": True,
                    "incognito": True,
                    "quickadd": {
                        "active": True,
                        "color": "#008800",
                        "isSocksDNS": False,
                        "port": "",
                        "scheme": "http",
                        "title": "QuickAdd Proxy",
                        "type": 1,
                        "address": "",
                        "username": "",
                        "password": "",
                        "id": "quickadd-default",
                    },
                    "showDesktopNotifications": True,
                },
                "timestamp": int(time.time() * 1000),  # Current time in ms
            }

            # Iterate only up to the number of available pairs
            for i in range(num_pairs_to_configure):
                proxy_info = self.proxies[i]
                container_def = self.containers[i]

                ff_container_user_context_id = container_def.get("userContextId")
                ff_container_name = container_def.get("name")
                if (
                    not isinstance(ff_container_user_context_id, int)
                    or not ff_container_name
                ):
                    logger.warning(
                        "Skipping FoxyProxy entry for invalid container data (missing ID or name): %s",
                        container_def,
                    )
                    continue

                foxyproxy_container_key = (
                    f"firefox-container-{ff_container_user_context_id}"
                )
                foxyproxy_proxy_definition_id = (
                    f"proxy-for-container-{ff_container_user_context_id}"
                )

                proxy_host = proxy_info.get("ip")
                proxy_port_val = proxy_info.get("port")
                internal_proxy_type = proxy_info.get(
                    "type"
                )  # Should be 'http' or 'socks5'

                if not all([proxy_host, proxy_port_val, internal_proxy_type]):
                    logger.warning(
                        "Skipping FoxyProxy entry for proxy with missing host/port/type: %s",
                        proxy_info,
                    )
                    continue

                # FoxyProxy uses 'socks' for SOCKS5
                foxyproxy_type_string = (
                    "socks" if internal_proxy_type == "socks5" else internal_proxy_type
                )

                # Use the original list title from proxy_info if available for a more descriptive proxy title
                proxy_list_title_for_foxyproxy = proxy_info.get(
                    "title", proxy_host
                )  # Fallback to host if title missing

                proxy_definition_entry = {
                    "id": foxyproxy_proxy_definition_id,
                    "title": f"{ff_container_name} ({proxy_list_title_for_foxyproxy})",  # More descriptive title
                    "notes": (
                        f"Managed by Script. Proxy: {proxy_host}:{proxy_port_val} "
                        f"({internal_proxy_type.upper()}) from list '{proxy_info.get('title', 'N/A')}' "
                        f"Geo: {proxy_info.get('city', 'N/A')}, {proxy_info.get('state', 'N/A')}, {proxy_info.get('isp', 'N/A')}"
                    ),
                    "type": foxyproxy_type_string,
                    "address": proxy_host,
                    "port": int(proxy_port_val),  # Ensure port is int
                    "username": proxy_info.get("username", ""),
                    "password": proxy_info.get("password", ""),
                    "proxyDNS": True
                    if internal_proxy_type == "socks5"
                    else False,  # SOCKS proxies handle DNS
                    "active": True,
                    "color": container_def.get(
                        "color", "#0088CC"
                    ),  # Use container color
                }
                foxyproxy_config["proxies"].append(proxy_definition_entry)
                foxyproxy_config["containers"][foxyproxy_container_key] = {
                    "proxyId": foxyproxy_proxy_definition_id,
                    "active": True,
                }
                logger.info(
                    "Added FoxyProxy config mapping for Firefox Container '%s' to use proxy %s:%s (from list '%s')",
                    ff_container_name,
                    proxy_host,
                    proxy_port_val,
                    proxy_info.get("title", "N/A"),
                )

            config_file_path = Path(os.getcwd()) / "foxyproxy_config.json"
            config_file_path.parent.mkdir(
                parents=True, exist_ok=True
            )  # Ensure dir exists
            with open(config_file_path, "w", encoding="utf-8") as f:
                json.dump(foxyproxy_config, f, indent=2, ensure_ascii=False)
            logger.info(
                "FoxyProxy configuration generated and saved to: %s", config_file_path
            )
            return config_file_path
        except Exception as e_foxy_gen:
            logger.exception("Error in generate_foxyproxy_config: %s", e_foxy_gen)
            return None

    def run(self, skip_import=False):
        """Run the complete proxy container setup process."""
        try:
            public_ip_req = requests.get("https://api.ipify.org", timeout=10)
            public_ip_req.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            self.public_ip = public_ip_req.text.strip()
            logger.info("Detected public IP for this script: %s", self.public_ip)
        except requests.RequestException as e_ip:
            logger.warning("Could not determine public IP: %s", e_ip)
        except Exception as e_ip_other:  # Catch any other unexpected error
            logger.warning("Unexpected error determining public IP: %s", e_ip_other)

        logger.info(
            "Starting proxy container setup process (using SDK for resident proxies)..."
        )

        if not self.init_api_client():  # This now also tests resident manager with SDK
            logger.critical(
                "ProxySeller API client/manager initialization failed. Cannot proceed."
            )
            return False

        if (
            not self.fetch_proxies() or not self.proxies
        ):  # This now uses resident proxies via SDK
            logger.error(
                "Failed to fetch or configure required resident proxies using SDK. Cannot proceed."
            )
            return False

        if not self.create_or_update_containers():
            logger.error(
                "Failed to create or update Firefox container definitions in containers.json."
            )
            # If no containers could be made (e.g., num_containers became 0),
            # then FoxyProxy config also cannot be made.
            if self.num_containers == 0:
                logger.info(
                    "No containers available for FoxyProxy setup. Exiting FoxyProxy related steps."
                )
                # Still consider the run "successful" in terms of not crashing,
                # but it didn't do much. Caller might want to check self.num_containers.
                # For now, let it return False as primary goal is proxy setup.
                return False
            return False  # General error in create_or_update_containers

        config_path = self.generate_foxyproxy_config()
        if (
            not config_path
        ):  # generate_foxyproxy_config returns None on failure (e.g. if num_pairs is 0)
            logger.error(
                "Failed to generate FoxyProxy configuration file (likely no proxy/container pairs)."
            )
            # If this failed because num_containers is 0, it's consistent.
            # If num_containers > 0 but this failed, it's an issue.
            return False

        self.create_summary_file()  # Best effort

        if not skip_import:
            logger.info(
                "Attempting automated import of FoxyProxy configuration via Selenium..."
            )
            try:
                if not self.import_foxyproxy_config(config_path):
                    logger.warning(
                        "Automated FoxyProxy import failed or timed out. "
                        "Manual import will be needed: %s",
                        config_path,
                    )
                else:
                    logger.info(
                        "FoxyProxy import process initiated successfully via automation."
                    )
            except (
                Exception
            ) as import_err:  # Catching broad exception from import attempt
                logger.error(
                    "Unexpected error during FoxyProxy import attempt: %s",
                    import_err,
                    exc_info=True,
                )
                logger.info("Manual import will be needed: %s", config_path)
        else:
            logger.info(
                "Skipping automated FoxyProxy import. Manual Import required from: %s",
                config_path,
            )

        logger.info("Launching a dedicated Firefox window for each container setup...")
        self.launch_container_windows()

        logger.info("=" * 40)
        logger.info("Proxy container setup process finished main steps.")
        logger.info(
            "Summary file available at: %s",
            Path(os.getcwd()) / "proxy_container_summary.txt",
        )
        logger.info(
            "FoxyProxy configuration file (for manual import if needed): %s",
            config_path,
        )
        logger.info("=" * 40)
        return True

    def launch_container_windows(self):
        """Launch Firefox windows, one for each container/proxy pair."""
        logger.info("==== LAUNCHING CONTAINER WINDOWS ====")
        # Ensure we only launch for matched pairs
        num_pairs_to_launch = min(
            len(self.containers) if self.containers else 0,
            len(self.proxies) if self.proxies else 0,
        )

        if num_pairs_to_launch == 0:
            logger.error(
                "Cannot launch windows: No container/proxy pairs available. "
                "Containers: %d, Proxies: %d",
                len(self.containers) if self.containers else 0,
                len(self.proxies) if self.proxies else 0,
            )
            return

        logger.info(
            "Attempting to terminate existing Firefox processes to avoid profile lock issues (best effort)..."
        )
        try:
            cmd_args = []
            if sys.platform.startswith("win"):
                cmd_args = ["taskkill", "/F", "/IM", "firefox.exe", "/T"]
                subprocess.run(
                    cmd_args,
                    capture_output=True,
                    text=True,
                    check=False,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                )
            elif sys.platform.startswith("darwin"):  # macOS
                cmd_args = ["pkill", "-f", "Firefox"]  # Case-sensitive on macOS
                subprocess.run(cmd_args, capture_output=True, text=True, check=False)
            else:  # Linux and other Unix-like
                cmd_args = ["pkill", "-f", "firefox"]  # Usually lowercase
                subprocess.run(cmd_args, capture_output=True, text=True, check=False)
            logger.info(
                "Firefox termination command executed. Waiting a few seconds..."
            )
            time.sleep(3)
        except FileNotFoundError:
            logger.warning(
                "Process termination command (taskkill/pkill) not found. Skipping."
            )
        except Exception as e_kill:  # More specific + general
            logger.warning(
                "Failed to terminate existing Firefox processes automatically: %s",
                e_kill,
            )

        def launch_window_for_container_thread_func(container_info, proxy_info_dict):
            container_name = container_info.get("name", "Unknown Container")
            proxy_display = (
                f"{proxy_info_dict.get('ip')}:{proxy_info_dict.get('port')}"
                if proxy_info_dict
                else "N/A"
            )
            local_driver_instance = None  # Ensure defined in this scope
            try:
                logger.info(
                    "Preparing to launch window for %s (Proxy: %s from list '%s')",
                    container_name,
                    proxy_display,
                    proxy_info_dict.get("title", "N/A"),
                )
                ff_options = FirefoxOptions()
                ff_options.profile = str(self.firefox_profile_path)

                try:
                    gecko_service = Service(GeckoDriverManager().install())
                except Exception as wdm_err_thread:
                    logger.error(
                        "Failed to setup GeckoDriverManager for %s: %s.",
                        container_name,
                        wdm_err_thread,
                    )
                    return f"Failed to setup GeckoDriver for {container_name}: {wdm_err_thread}"

                local_driver_instance = webdriver.Firefox(
                    service=gecko_service, options=ff_options
                )
                local_driver_instance.set_page_load_timeout(45)
                local_driver_instance.implicitly_wait(5)

                instruction_html_content = f"""
                <html><head><title>Manual Action: {container_name}</title>
                <style>body {{ font-family: sans-serif; padding: 20px; }} li {{ margin-bottom: 10px; }} b {{ color: #0056b3; }}</style></head>
                <body><h2 style="color: {container_info.get("color", "blue")};">Manual Action Required for: {container_name}</h2>
                <p>This Firefox window is configured to use proxy:
                   <b>{proxy_display} ({proxy_info_dict.get("type") if proxy_info_dict else "N/A"})</b>
                   from list <b>'{proxy_info_dict.get("title", "N/A")}'</b> via FoxyProxy.</p>
                <p>Geo: City: <b>{proxy_info_dict.get("city", "N/A")}</b>, State: <b>{proxy_info_dict.get("state", "N/A")}</b>, ISP: <b>{proxy_info_dict.get("isp", "N/A")}</b></p>
                <p>If using <b>Firefox Multi-Account Containers</b> extension:</p><ol>
                  <li>Right-click on <b>THIS INSTRUCTION TAB</b>.</li>
                  <li>Select "Reopen in Container" / "Move Tab to Container".</li>
                  <li>Choose <b>'{container_name}'</b>.</li>
                  <li>Repeat for other tabs (dasher.doordash.com, ipinfo.io) to assign them.</li></ol><hr>
                <p><i>Verify proxy at ipinfo.io.</i></p></body></html>"""
                local_driver_instance.get(
                    "data:text/html;charset=utf-8," + instruction_html_content
                )
                local_driver_instance.execute_script(
                    "window.open('https://dasher.doordash.com', '_blank');"
                )
                local_driver_instance.execute_script(
                    "window.open('https://ipinfo.io', '_blank');"
                )
                logger.info(
                    "Window for %s launched with instruction, DoorDash, and IPInfo tabs.",
                    container_name,
                )
                return f"Successfully launched window for {container_name}"
            except WebDriverException as e_wd:
                logger.error(
                    "WebDriver error launching window for %s: %s", container_name, e_wd
                )
                if "profile is in use" in str(e_wd).lower():
                    logger.error(
                        "Profile lock error for %s. Ensure Firefox instances closed.",
                        container_name,
                    )
                return f"WebDriver error for {container_name}: {e_wd}"
            except Exception as e_thread:
                logger.error(
                    "Unexpected error launching window for %s: %s",
                    container_name,
                    e_thread,
                    exc_info=True,
                )
                return f"Failed to launch {container_name}: {e_thread}"
            # IMPORTANT: Do NOT call local_driver_instance.quit() here to keep windows open.

        num_workers = min(num_pairs_to_launch, 5)  # Limit concurrent browser launches
        if num_workers <= 0:
            logger.info(
                "No container/proxy pairs to launch windows for after adjustments."
            )
            return

        logger.info("Launching up to %d browser windows concurrently...", num_workers)
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures_list = [
                # Iterate up to num_pairs_to_launch to ensure we don't go out of bounds
                executor.submit(
                    launch_window_for_container_thread_func,
                    self.containers[i],
                    self.proxies[i],
                )
                for i in range(num_pairs_to_launch)
            ]
            for future_item in as_completed(futures_list):
                try:
                    result_message = future_item.result()
                    if "Failed" in result_message or "error" in result_message.lower():
                        logger.warning(result_message)
                    else:
                        logger.info(result_message)
                except Exception as e_future:
                    logger.error(
                        "Error processing result from a launch thread: %s",
                        e_future,
                        exc_info=True,
                    )

        logger.info("All container window launch attempts have been initiated.")
        logger.info("==== IF USING FIREFOX MULTI-ACCOUNT CONTAINERS EXTENSION: ====")
        logger.info(
            "==== MANUAL TAB-TO-CONTAINER ASSIGNMENT IS REQUIRED. SEE INSTRUCTION TAB IN EACH WINDOW. ===="
        )

    def import_foxyproxy_config(self, config_path: Path):
        """Import FoxyProxy configuration using Selenium, with manual fallback for ID."""
        logger.info("Starting FoxyProxy import via Selenium...")
        driver = None  # Ensure driver is defined
        max_retries = 1
        attempt_num = 0
        foxyproxy_id_to_use_for_url = (
            self.foxyproxy_internal_id
        )  # Use cached if available

        while attempt_num <= max_retries:
            attempt_num += 1
            logger.info(
                "FoxyProxy Selenium import attempt %d/%d...",
                attempt_num,
                max_retries + 1,
            )
            try:
                ff_options_import = FirefoxOptions()
                ff_options_import.profile = str(self.firefox_profile_path)

                logger.debug("Initializing GeckoDriverManager for FoxyProxy import...")
                try:
                    import_service = Service(GeckoDriverManager().install())
                except Exception as wdm_err_import:
                    logger.error(
                        "Failed to setup GeckoDriverManager for import: %s",
                        wdm_err_import,
                    )
                    if attempt_num > max_retries:
                        raise FoxyProxySetupError(
                            f"GeckoDriver setup failed: {wdm_err_import}"
                        ) from wdm_err_import
                    time.sleep(5)
                    continue  # Retry WDM init

                logger.info(
                    "Initializing WebDriver for FoxyProxy options page access..."
                )
                driver = webdriver.Firefox(
                    service=import_service, options=ff_options_import
                )
                driver.set_page_load_timeout(60)
                driver.implicitly_wait(10)
                wait = WebDriverWait(driver, 45)
                logger.info(
                    "Browser for FoxyProxy import started (PID: %s).",
                    driver.service.process.pid
                    if driver
                    and driver.service
                    and hasattr(driver.service, "process")
                    and driver.service.process
                    else "N/A",
                )

                if not foxyproxy_id_to_use_for_url:  # If not cached, try to find it
                    logger.info(
                        "Attempting to find FoxyProxy Internal UUID via about:debugging..."
                    )
                    driver.get("about:debugging#/runtime/this-firefox")
                    try:
                        wait.until(
                            EC.presence_of_element_located(
                                (
                                    By.CSS_SELECTOR,
                                    "section.addon-list-section, section.extension-list-section",
                                )
                            )
                        )
                        time.sleep(7)  # Allow JS to render everything
                        extensions_cards = driver.find_elements(
                            By.CSS_SELECTOR, "div.addon-card, div.extension-card"
                        )
                        if not extensions_cards:
                            raise TimeoutException(
                                "No extension cards found on about:debugging."
                            )

                        found_fp_id_auto = False
                        for ext_card_element in extensions_cards:
                            try:
                                ext_name_elements = ext_card_element.find_elements(
                                    By.CSS_SELECTOR, "h3.addon-name, h3.extension-name"
                                )
                                if not ext_name_elements:
                                    continue
                                if (
                                    "FoxyProxy" in ext_name_elements[0].text
                                ):  # Check name
                                    uuid_elements = ext_card_element.find_elements(
                                        By.XPATH,
                                        ".//dt[contains(text(), 'Internal UUID')]/following-sibling::dd",
                                    )
                                    if uuid_elements:
                                        foxyproxy_id_to_use_for_url = uuid_elements[
                                            0
                                        ].text.strip()
                                        logger.info(
                                            "Found FoxyProxy Internal UUID: %s (Name: %s)",
                                            foxyproxy_id_to_use_for_url,
                                            ext_name_elements[0].text,
                                        )
                                        self.foxyproxy_internal_id = (
                                            foxyproxy_id_to_use_for_url  # Cache it
                                        )
                                        found_fp_id_auto = True
                                        break
                            except (
                                NoSuchElementException,
                                StaleElementReferenceException,
                            ):
                                continue  # Element might disappear
                        if not found_fp_id_auto:
                            logger.warning(
                                "Could not automatically find FoxyProxy Internal UUID on about:debugging."
                            )
                    except (TimeoutException, WebDriverException) as e_debug:
                        logger.error(
                            "Error finding FoxyProxy UUID on about:debugging: %s",
                            e_debug,
                        )

                if not foxyproxy_id_to_use_for_url:  # If still not found, prompt user
                    logger.error(
                        "Automated FoxyProxy Internal UUID detection failed. Prompting for manual input."
                    )
                    print(
                        f"\n{'=' * 60}\n ACTION REQUIRED: Manual FoxyProxy UUID Needed\n 1. Open Firefox, go to: about:debugging#/runtime/this-firefox\n 2. Find 'FoxyProxy Standard' or 'Basic'.\n 3. Copy its 'Internal UUID' value.\n{'=' * 60}"
                    )
                    while not foxyproxy_id_to_use_for_url:
                        try:
                            if not sys.stdin.isatty():
                                raise FoxyProxySetupError(
                                    "Non-interactive terminal. Cannot prompt for FoxyProxy UUID."
                                )
                            user_entered_id = input(
                                "  Paste Internal UUID here and press Enter: "
                            ).strip()
                            if (
                                len(user_entered_id) >= 36 and "-" in user_entered_id
                            ):  # Basic validation
                                foxyproxy_id_to_use_for_url = user_entered_id
                                self.foxyproxy_internal_id = (
                                    foxyproxy_id_to_use_for_url  # Cache it
                                )
                                logger.info(
                                    "Using manually entered FoxyProxy Internal UUID: %s",
                                    foxyproxy_id_to_use_for_url,
                                )
                            else:
                                print(
                                    "   [ERROR] Invalid format. UUID should be long with hyphens. Try again."
                                )
                        except EOFError as eof_err:
                            raise FoxyProxySetupError(
                                "FoxyProxy UUID not provided due to input error (EOF)."
                            ) from eof_err
                        except Exception as e_input_uuid:
                            raise FoxyProxySetupError(
                                f"FoxyProxy UUID input error: {e_input_uuid}"
                            ) from e_input_uuid
                    if not foxyproxy_id_to_use_for_url:
                        raise FoxyProxySetupError(
                            "FoxyProxy Internal UUID was not obtained."
                        )

                options_page_url = f"moz-extension://{foxyproxy_id_to_use_for_url.strip('{}')}/options.html"
                logger.info(
                    "Navigating to FoxyProxy options page: %s", options_page_url
                )
                driver.get(options_page_url)
                try:
                    wait.until(
                        EC.presence_of_element_located(
                            (
                                By.CSS_SELECTOR,
                                "header, #foxyproxy-app-header, nav#sidebar",
                            )
                        )
                    )  # Wait for page load
                except TimeoutException as toe:
                    raise FoxyProxySetupError(
                        f"Failed to load FoxyProxy options page: {options_page_url}. Check UUID/extension."
                    ) from toe

                logger.info("Looking for Import/Export navigation link/button...")
                nav_el = self._find_clickable_element(
                    wait,
                    [
                        (By.XPATH, "//button[normalize-space()='Import / Export']"),
                        (
                            By.CSS_SELECTOR,
                            "a[href*='import'], button[data-i18n*='importExport']",
                        ),
                    ],
                    "Import/Export navigation",
                )
                if not nav_el:
                    raise FoxyProxySetupError(
                        "Cannot find 'Import / Export' navigation on FoxyProxy options page."
                    )
                nav_el.click()
                time.sleep(2)  # Allow page to transition

                logger.info("Looking for file input element for FoxyProxy config...")
                file_input_el = self._find_element(
                    wait,
                    [
                        (
                            By.CSS_SELECTOR,
                            "input[type='file'][id*='import'], input[type='file'][name*='import']",
                        ),
                        (
                            By.XPATH,
                            "//input[@type='file' and contains(@class, 'file-input')]",
                        ),
                        (By.CSS_SELECTOR, "input[type=file]"),
                    ],
                    "FoxyProxy file input",
                    presence_only=True,
                )
                if not file_input_el:
                    raise FoxyProxySetupError(
                        "Cannot find file input for FoxyProxy configuration import."
                    )

                resolved_path = str(config_path.resolve())
                logger.info(
                    "Sending FoxyProxy config file path to input: %s", resolved_path
                )
                file_input_el.send_keys(resolved_path)
                time.sleep(2)  # Allow file to register

                logger.info("Looking for final 'Import' or 'Import Settings' button...")
                confirm_btn_el = self._find_clickable_element(
                    wait,
                    [
                        (
                            By.XPATH,
                            "//button[normalize-space()='Import Settings' or normalize-space()='Import']",
                        ),
                        (
                            By.CSS_SELECTOR,
                            "button[type='submit'][id*='import'], button.is-primary[data-i18n='importButton'], button.button.is-primary",
                        ),
                    ],
                    "final FoxyProxy Import button",
                )
                if confirm_btn_el:
                    driver.execute_script(
                        "arguments[0].scrollIntoView(true);", confirm_btn_el
                    )
                    time.sleep(0.5)  # Scroll into view
                    confirm_btn_el.click()
                    time.sleep(4)  # Allow import to process
                else:
                    logger.warning(
                        "Could not find a distinct final 'Import' button. Import might occur on file selection."
                    )

                try:  # Handle alert if present
                    alert = WebDriverWait(driver, 5).until(EC.alert_is_present())
                    alert_text = alert.text
                    logger.info(
                        "FoxyProxy import alert displayed: '%.100s...'", alert_text
                    )
                    alert.accept()
                    if (
                        "success" not in alert_text.lower()
                        and "erfolgreich" not in alert_text.lower()
                    ):
                        logger.warning(
                            "FoxyProxy import alert text does not clearly indicate success."
                        )
                except (TimeoutException, NoAlertPresentException):
                    logger.info(
                        "No alert after FoxyProxy import. Checking for on-page success messages."
                    )
                    try:  # Check for on-page success message
                        success_msg_el = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located(
                                (
                                    By.XPATH,
                                    "//*[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'imported successfully') or contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'import successful')]",
                                )
                            )
                        )
                        logger.info(
                            "Detected FoxyProxy import success message: '%.100s'",
                            success_msg_el.text,
                        )
                    except TimeoutException:
                        logger.warning(
                            "No explicit FoxyProxy success message (alert or text) detected. Assuming import was sent."
                        )
                except Exception as alert_e:
                    logger.error("Error handling FoxyProxy alert: %s", alert_e)

                logger.info(
                    "FoxyProxy import process attempted. Verify in FoxyProxy options."
                )
                return True
            except FoxyProxySetupError as fpe_inner:
                logger.error(
                    "FoxyProxy Setup Error (Attempt %d/%d): %s",
                    attempt_num,
                    max_retries + 1,
                    fpe_inner,
                )
                time.sleep(1)  # Brief pause before retry
            except SessionNotCreatedException as snce_inner:
                logger.error(
                    "WebDriver: Could not create session (Attempt %d/%d): %s",
                    attempt_num,
                    max_retries + 1,
                    snce_inner,
                )
                raise
            except WebDriverException as wde_inner:
                logger.error(
                    "WebDriver error during FoxyProxy import (Attempt %d/%d): %s",
                    attempt_num,
                    max_retries + 1,
                    wde_inner,
                )
                if "Missing 'moz:profile'" in str(
                    wde_inner
                ) or "Failed to read profile" in str(wde_inner):
                    logger.critical(
                        "CRITICAL: Invalid Firefox profile path: %s",
                        self.firefox_profile_path,
                    )
                    raise
                if "unexpected alert open" in str(wde_inner).lower() and driver:
                    try:
                        logger.warning(
                            "Handling unexpected alert during FoxyProxy import."
                        )
                        driver.switch_to.alert.accept()
                    except (NoAlertPresentException, Exception) as alert_e_h:
                        logger.error("Failed to handle unexpected alert: %s", alert_e_h)
            except Exception as e_inner_import:
                logger.exception(
                    "Unexpected error during FoxyProxy import (Attempt %d/%d): %s",
                    attempt_num,
                    max_retries + 1,
                    e_inner_import,
                )
            finally:
                if driver:
                    try:
                        logger.info(
                            "Closing browser window used for FoxyProxy import..."
                        )
                        driver.quit()
                    except WebDriverException as e_q_wd:
                        logger.warning(
                            "WebDriver error closing FoxyProxy import browser: %s",
                            e_q_wd,
                        )
                    except Exception as e_q_g:
                        logger.warning(
                            "Generic error closing FoxyProxy import browser: %s", e_q_g
                        )
                driver = None  # Ensure driver is reset for next attempt or exit
            if attempt_num <= max_retries:
                logger.info("Waiting 5 seconds before retrying FoxyProxy import...")
                time.sleep(5)
        logger.error(
            "Automated FoxyProxy import failed after %d attempt(s).", max_retries + 1
        )
        return False

    def _find_element(
        self, wait_instance, selectors_list, element_description, presence_only=False
    ):
        logger.debug("Searching for element: %s", element_description)
        for by_method, selector_value in selectors_list:
            try:
                logger.debug(
                    "Trying selector: By.%s, Value: '%s'", by_method, selector_value
                )
                condition = (
                    EC.presence_of_element_located((by_method, selector_value))
                    if presence_only
                    else EC.visibility_of_element_located((by_method, selector_value))
                )
                element = wait_instance.until(condition)
                if element:
                    logger.debug(
                        "Found '%s' using selector: By.%s, Value: '%s'",
                        element_description,
                        by_method,
                        selector_value,
                    )
                    return element
            except (TimeoutException, NoSuchElementException):
                logger.debug(
                    "Selector failed for '%s': By.%s, Value: '%s'",
                    element_description,
                    by_method,
                    selector_value,
                )
        logger.warning(
            "Could not find element '%s' using any provided selectors.",
            element_description,
        )
        return None

    def _find_clickable_element(
        self, wait_instance, selectors_list, element_description
    ):
        element = self._find_element(
            wait_instance, selectors_list, element_description, presence_only=False
        )
        if element:
            try:
                clickable_element = wait_instance.until(
                    EC.element_to_be_clickable(element)
                )
                logger.debug("Element '%s' confirmed clickable.", element_description)
                return clickable_element
            except (
                TimeoutException,
                StaleElementReferenceException,
                ElementNotInteractableException,
            ) as e_clickable:
                logger.warning(
                    "Element '%s' found but not currently clickable: %s",
                    element_description,
                    e_clickable,
                )
        return None

    def create_summary_file(self):
        """Create a summary text file mapping containers to proxies."""
        summary_file_path = Path(os.getcwd()) / "proxy_container_summary.txt"
        logger.info("Creating proxy-container summary file at: %s", summary_file_path)
        try:
            with open(summary_file_path, "w", encoding="utf-8") as f:
                f.write(
                    f"{'=' * 50}\n Firefox Container and Proxy Mapping Summary\n{'=' * 50}\n\n"
                )

                num_pairs_for_summary = min(
                    len(self.containers) if self.containers else 0,
                    len(self.proxies) if self.proxies else 0,
                )

                if num_pairs_for_summary == 0:
                    f.write("Error: No container/proxy pairs available to summarize.\n")
                    logger.error(
                        "Cannot create accurate summary: no container/proxy pairs available. "
                        "Containers: %d, Proxies: %d.",
                        len(self.containers) if self.containers else 0,
                        len(self.proxies) if self.proxies else 0,
                    )
                    return False

                for i in range(num_pairs_for_summary):
                    container_def = self.containers[i]
                    proxy_info = self.proxies[i]  # Corresponding proxy
                    f.write(f"--- Mapping {i + 1} ---\n")
                    f.write(
                        f" Firefox Container Name:   {container_def.get('name', 'N/A')}\n"
                    )
                    f.write(
                        f" Firefox Container ID:     userContextId: {container_def.get('userContextId', 'N/A')}\n"
                    )
                    # FoxyProxy key not strictly needed in summary but can be derived
                    # f.write(f" FoxyProxy Map Key:        firefox-container-{container_def.get('userContextId', 'N/A')}\n")
                    f.write(
                        f" Assigned Proxy IP:        {proxy_info.get('ip', 'N/A')}\n"
                    )
                    f.write(
                        f" Assigned Proxy Port:      {proxy_info.get('port', 'N/A')}\n"
                    )
                    f.write(
                        f" Assigned Proxy Type:      {proxy_info.get('type', 'N/A').upper()}\n"
                    )
                    f.write(
                        f" Assigned Proxy Username:  {proxy_info.get('username', '(none)')}\n"
                    )
                    # For resident proxies, 'id' refers to the list_id, 'title' is list title
                    f.write(
                        f" ProxySeller List Title:   {proxy_info.get('title', 'N/A')}\n"
                    )
                    f.write(
                        f" ProxySeller List ID:      {proxy_info.get('id', 'N/A')}\n"
                    )  # This is resident_list_ID
                    f.write(
                        f" Proxy Status (Assumed):   {proxy_info.get('status', 'N/A')}\n"
                    )
                    f.write(
                        f" Proxy Geo (State):        {proxy_info.get('state', 'N/A')}\n"
                    )
                    f.write(
                        f" Proxy Geo (City):         {proxy_info.get('city', 'N/A')}\n"
                    )
                    f.write(
                        f" Proxy Geo (ISP):          {proxy_info.get('isp', 'N/A')}\n"
                    )
                    f.write(f"{'=' * 25}\n\n")
            logger.info("Summary file created successfully.")
            return True
        except IOError as e_summary_io:
            logger.exception(
                "IOError creating summary file %s: %s", summary_file_path, e_summary_io
            )
            return False
        except Exception as e_summary_generic:
            logger.exception(
                "Error creating summary file %s: %s",
                summary_file_path,
                e_summary_generic,
            )
            return False


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Setup Firefox containers with ProxySeller proxies (SDK Resident Mode) and FoxyProxy.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "-k", "--api-key", help="ProxySeller API key (overrides config.ini)"
    )
    parser.add_argument(
        "-n",
        "--num-containers",
        type=int,
        help="Number of containers/proxies to set up (max limited by available US resident lists).",
    )
    parser.add_argument(
        "-t",
        "--proxy-type",
        choices=["http", "socks5"],
        default="http",
        help="Proxy type for resident proxies (default: http, overrides config.ini)",
    )
    parser.add_argument(
        "-p",
        "--profile",
        help="Path to Firefox profile directory (overrides config.ini, auto-detects if blank)",
    )
    parser.add_argument(
        "--skip-import",
        action="store_true",
        help="Skip automated FoxyProxy import (overrides config.ini)",
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Console log level (overrides config.ini)",
    )
    # Updated diagnostic/test flags to reflect SDK usage for resident proxies
    parser.add_argument(
        "--diagnostic-api-dump-sdk",
        metavar="DIR_PATH",
        help="Directory to dump SDK resident API responses and exit",
    )
    parser.add_argument(
        "--test-api-key-sdk",
        action="store_true",
        help="Test API key with SDK for resident proxy access, print results and exit",
    )
    return parser.parse_args()


def load_config_and_args():
    """Load settings from config.ini and merge with command line args (CLI args take precedence)."""
    config_file_path = Path(__file__).parent / "config.ini"
    config = configparser.ConfigParser(interpolation=None, allow_no_value=True)
    config.optionxform = str  # Preserve case for keys

    if config_file_path.is_file():
        try:
            with open(
                config_file_path, "r", encoding="utf-8-sig"
            ) as cfg_file:  # Handles BOM
                config.read_file(cfg_file)
            logger.info("Loaded settings from config.ini at %s", config_file_path)
        except (IOError, configparser.Error) as e_cfg_load:
            logger.error(
                "Error parsing config.ini: %s. Using defaults and CLI args.", e_cfg_load
            )
            config = configparser.ConfigParser(interpolation=None)
            config.optionxform = str  # Reset on error
    else:
        logger.info(
            "config.ini not found at %s. Using defaults and CLI arguments.",
            config_file_path,
        )

    args = parse_arguments()
    cfg_section_name = "DEFAULT"  # Assume settings are in DEFAULT or not in sections

    def get_resolved_val(cli_val, cfg_key, default_val, is_int=False, is_bool=False):
        val = default_val  # Start with internal script default
        # Try to get from config file; if key exists, it overrides internal default
        if (
            cfg_section_name in config
            and config[cfg_section_name].get(cfg_key) is not None
        ):
            # Ensure empty string in config doesn't override if default_val is meaningful
            config_val_str = config[cfg_section_name].get(cfg_key)
            if (
                config_val_str.strip() != "" or default_val is None
            ):  # Allow empty string from config if default is None
                val = config_val_str

        # CLI argument overrides everything if provided
        if cli_val is not None:
            val = cli_val

        try:
            if val is None:
                return default_val  # If still None, return original default
            if is_int:
                return int(val)
            if is_bool:
                if isinstance(val, bool):
                    return val  # Already bool
                return str(val).lower() in [
                    "true",
                    "1",
                    "yes",
                    "on",
                ]  # Convert string to bool
            return str(val)  # Ensure string type for others
        except ValueError as ve:  # Handle type conversion errors
            logger.warning(
                "Invalid value for %s ('%s'): %s. Using internal default: %s",
                cfg_key,
                val,
                ve,
                default_val,
            )
            return default_val

    settings_dict = {
        "api_key": get_resolved_val(args.api_key, "API_KEY", None),
        "num_containers": get_resolved_val(
            args.num_containers, "NUM_CONTAINERS", 3, is_int=True
        ),
        "proxy_type": get_resolved_val(
            args.proxy_type, "PROXY_TYPE", "http"
        ).lower(),  # Default 'http'
        "log_level": get_resolved_val(args.log_level, "LOG_LEVEL", "INFO").upper(),
        "firefox_profile_path": get_resolved_val(args.profile, "PROFILE", None),
        "skip_import": args.skip_import
        or get_resolved_val(None, "SKIP_IMPORT", False, is_bool=True),
        # Removed user_id as it wasn't used and not in original args.
    }

    missing_critical = []
    if not settings_dict["api_key"]:
        missing_critical.append("API_KEY (must be in config.ini or via --api-key)")
    if settings_dict["num_containers"] < 1:
        logger.warning(
            "Num containers resolved to %d, adjusting to 1.",
            settings_dict["num_containers"],
        )
        settings_dict["num_containers"] = 1
    if settings_dict["proxy_type"] not in ("http", "socks5"):
        missing_critical.append(
            f"PROXY_TYPE (must be 'http' or 'socks5', got '{settings_dict['proxy_type']}')"
        )

    if missing_critical:
        err_msg = (
            f"Critical configuration errors: {'; '.join(missing_critical)}. "
            "Update config.ini or use correct CLI arguments."
        )
        logger.critical(err_msg)
        sys.exit(5)

    logger.info(
        "Effective settings: Num Containers: %d, Proxy Type: %s, Log Level: %s, "
        "Profile Path: %s, Skip Import: %s, API Key Set: %s",
        settings_dict["num_containers"],
        settings_dict["proxy_type"],
        settings_dict["log_level"],
        settings_dict["firefox_profile_path"] or "Autodetect",
        settings_dict["skip_import"],
        bool(settings_dict["api_key"]),
    )
    return settings_dict, args


def diagnostic_api_test_and_dump_sdk(current_api_key, out_dir_path_str):
    """Dumps SDK resident API responses for diagnostics."""
    out_dir_path = Path(out_dir_path_str)
    out_dir_path.mkdir(parents=True, exist_ok=True)
    logger.info(
        "Starting ProxySeller SDK RESIDENT API diagnostic dump (Key: %s*****)...",
        current_api_key[:5],
    )

    if not PROXY_SELLER_API_CLIENT_CLASS:
        logger.error("SDK not loaded. Cannot perform diagnostic dump.")
        sys.exit(1)
    try:
        api_sdk_instance = PROXY_SELLER_API_CLIENT_CLASS({"key": current_api_key})
        resident_manager = ProxySellerResidentManagerSDK(api_sdk_instance)

        endpoints_to_test_sdk = {
            "sdk_resident_package": resident_manager.get_package_info_sdk,
            "sdk_resident_geo": resident_manager.get_all_locations_sdk,
            "sdk_resident_lists": resident_manager.get_existing_lists_sdk,
        }
        summary = []

        for name, func_to_call in endpoints_to_test_sdk.items():
            dump_path = out_dir_path / f"{name}_raw.json"
            try:
                raw_data = func_to_call()  # Call the manager's SDK method
                # Ensure data is serializable, handle potential bytes from SDK if not already string/dict/list
                if isinstance(raw_data, bytes):
                    raw_data = raw_data.decode("utf-8", errors="replace")

                data_to_dump_str = (
                    json.dumps(raw_data, indent=2, ensure_ascii=False)
                    if raw_data
                    else "{}"
                )

                with open(dump_path, "w", encoding="utf-8") as f:
                    f.write(data_to_dump_str)
                summary.append(f"{name}: Success, dumped to {dump_path}")
            except ProxySellerAPIError as e_diag_api:
                summary.append(
                    f"{name}: API ERROR {e_diag_api.message}. Dumped error to {dump_path}"
                )
                with open(dump_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {
                            "error": e_diag_api.message,
                            "sdk_error_details": str(e_diag_api.sdk_error),
                        },
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )
            except Exception as e_other_diag:
                summary.append(
                    f"{name}: UNEXPECTED ERROR {e_other_diag}. Attempted dump to {dump_path}"
                )
                with open(dump_path, "w", encoding="utf-8") as f:
                    json.dump(
                        {"unexpected_error": str(e_other_diag)},
                        f,
                        indent=2,
                        ensure_ascii=False,
                    )

        print("\nProxySeller SDK RESIDENT API diagnostic dump complete. Results:")
        for line in summary:
            print(f"  {line}")
        print(f"\nFiles are in directory: {out_dir_path.resolve()}")
        print(
            "Provide these files to Proxy-Seller support if needed (especially if errors occurred)."
        )
    except Exception as e_setup_diag:  # Error setting up SDK client for diagnostics
        logger.error(
            "Error setting up SDK client for diagnostic dump: %s",
            e_setup_diag,
            exc_info=True,
        )
    sys.exit(0)


def test_api_key_sdk(current_api_key):
    """Tests API key with SDK for resident proxy access."""
    logger.info(
        "Testing ProxySeller API key with SDK for RESIDENT access (Key: %s*****)...",
        current_api_key[:5],
    )

    if not PROXY_SELLER_API_CLIENT_CLASS:
        logger.error("SDK not loaded. Cannot perform API key test.")
        sys.exit(1)

    overall_success = True
    try:
        api_sdk_instance = PROXY_SELLER_API_CLIENT_CLASS({"key": current_api_key})
        resident_manager = ProxySellerResidentManagerSDK(api_sdk_instance)

        # Test package info
        package_info = resident_manager.get_package_info_sdk()
        if package_info and package_info.get("is_active"):
            logger.info(
                f"  SDK Package Info: SUCCESS. Active. Traffic Left: {package_info.get('traffic_left_sub', package_info.get('traffic_left', 'N/A'))}"
            )
        else:
            logger.error(
                f"  SDK Package Info: FAILED or Inactive. Response: {package_info}"
            )
            overall_success = False

        # Test geo locations
        geo_info = resident_manager.get_all_locations_sdk()
        if (
            geo_info
            and isinstance(geo_info, list)
            and any(c.get("code") == "US" for c in geo_info if isinstance(c, dict))
        ):
            logger.info(
                f"  SDK Geo Info: SUCCESS. Found {len(geo_info)} countries, including US."
            )
        elif geo_info is None:  # Error occurred within manager method
            logger.error(
                f"  SDK Geo Info: FAILED to fetch (returned None). Check previous logs from manager."
            )
            overall_success = False
        else:  # Got data but not as expected
            logger.error(
                f"  SDK Geo Info: FAILED or US not found or unexpected format. Response type: {type(geo_info)}, Preview: {str(geo_info)[:200]}..."
            )
            overall_success = False

        # Test existing lists
        existing_lists = resident_manager.get_existing_lists_sdk()
        if existing_lists is not None and isinstance(
            existing_lists, list
        ):  # Allows empty list as success
            logger.info(
                f"  SDK Existing Lists: SUCCESS. Found {len(existing_lists)} lists."
            )
            if not any(
                isinstance(lst.get("geo", {}), dict)
                and lst.get("geo", {}).get("country", "").upper() == "US"
                for lst in existing_lists
            ):
                logger.warning(
                    "    Note: No US-targeted lists found among existing lists. Script requires these to be pre-configured."
                )
        else:  # Error or unexpected format
            logger.error(
                f"  SDK Existing Lists: FAILED or unexpected format. Response type: {type(existing_lists)}"
            )
            overall_success = False

        if overall_success:
            logger.info("\nAPI key appears VALID for SDK resident proxy access.")
            sys.exit(0)
        else:
            logger.error(
                "\nAPI key has ISSUES with SDK resident proxy access. Review details above."
            )
            sys.exit(1)

    except ProxySellerAPIError as e_test_api:  # Catch specific API errors from manager
        logger.error(
            f"\nAPI key test with SDK FAILED (ProxySellerAPIError): {e_test_api.message}"
        )
        if e_test_api.sdk_error:
            logger.debug("  Original SDK error: %s", e_test_api.sdk_error)
        sys.exit(1)
    except (
        Exception
    ) as e_test_generic:  # Catch other errors like SDK client init failure
        logger.error(
            f"\nAPI key test with SDK FAILED (Unexpected Error): {e_test_generic}",
            exc_info=True,
        )
        sys.exit(1)


if __name__ == "__main__":
    settings_main, args_main = load_config_and_args()

    # Handle diagnostic/test flags first and exit
    if args_main.diagnostic_api_dump_sdk:
        diagnostic_api_test_and_dump_sdk(
            settings_main["api_key"], args_main.diagnostic_api_dump_sdk
        )
    if args_main.test_api_key_sdk:
        test_api_key_sdk(settings_main["api_key"])

    EXIT_CODE_MAIN = 1  # Default to error
    try:
        CONSOLE_LOG_LEVEL_ACTUAL = getattr(
            logging, settings_main["log_level"].upper(), logging.INFO
        )
        # Find and update the primary console stream_handler's level
        for h_item in logger.handlers:
            if isinstance(h_item, logging.StreamHandler) and h_item.stream in (
                sys.stdout,
                sys.stderr,
            ):
                h_item.setLevel(CONSOLE_LOG_LEVEL_ACTUAL)
                break

        logger.info("Console logging level set to: %s", settings_main["log_level"])
        logger.info("Detailed file logging (DEBUG level) at: %s", log_file_path)
        separator_main = "=" * 40
        logger.info(
            "%s\n Starting ProxySeller Firefox Setup (Resident SDK Mode)\n%s",
            separator_main,
            separator_main,
        )

        setup_manager_instance = ProxyContainerSetup(
            api_key=settings_main["api_key"],
            num_containers=settings_main["num_containers"],
            proxy_type=settings_main["proxy_type"],
            firefox_profile_path=settings_main["firefox_profile_path"],
        )

        if setup_manager_instance.run(skip_import=settings_main["skip_import"]):
            logger.info("Main setup process reported success for core operations.")
            EXIT_CODE_MAIN = 0
        else:
            logger.error(
                "Main setup process reported failure or incomplete execution for core operations."
            )

    except ProfileError as pe_main_exc:
        logger.critical("CRITICAL PROFILE ERROR: %s", pe_main_exc, exc_info=False)
        print(
            f"\nCRITICAL PROFILE ERROR: {pe_main_exc}\nEnsure Firefox is installed and a valid profile exists.\n"
        )
        EXIT_CODE_MAIN = 2
    except ImportError as ie_main_exc:  # Could be SDK or Selenium
        logger.critical("CRITICAL IMPORT ERROR: %s", ie_main_exc, exc_info=True)
        print(
            f"\nCRITICAL IMPORT ERROR: {ie_main_exc}\nEnsure dependencies (selenium, etc.) and ProxySeller SDK are correct.\n"
        )
        EXIT_CODE_MAIN = 3
    except (
        Exception
    ) as main_execution_err:  # Top-level catch for truly unexpected errors
        logger.critical(
            "Unexpected critical error in main execution: %s",
            main_execution_err,
            exc_info=True,
        )
        print(
            f"\nCRITICAL UNHANDLED ERROR: {main_execution_err}\nCheck '{log_file_path}' for details.\n"
        )
        EXIT_CODE_MAIN = 4

    if EXIT_CODE_MAIN == 0:
        success_message_body = (
            "PROXY AND CONTAINER SETUP PROCESS COMPLETED SUCCESSFULLY.\n"
            "Firefox browser windows should remain open.\n"
            "If using Firefox Multi-Account Containers, assign tabs manually (see instruction tab).\n"
            "Verify proxy functionality at ipinfo.io in each window."
        )
        logger.info("%s\n%s\n%s", "=" * 50, success_message_body, "=" * 50)

        logger.info(
            "IMPORTANT: This script will now pause to keep browser windows alive."
        )
        logger.info("           Close browser windows manually when finished.")
        try:
            PROMPT_MESSAGE = (
                "\n--> Press Enter in this console to terminate this script "
                "(this will close Selenium-controlled browser windows)."
            )
            if sys.stdin.isatty():  # Only prompt if interactive
                input(PROMPT_MESSAGE)
            else:
                logger.info(
                    "Non-interactive mode: Script will exit, closing Selenium browsers if any were opened by it."
                )
                # In non-interactive, might want a short delay or other signal
        except KeyboardInterrupt:
            logger.info("\nCtrl+C pressed. Script exiting.")
        except EOFError:  # Happens if input is piped and pipe closes
            logger.info("\nEOF detected on input. Script exiting.")
        # Regardless of input method, log that Selenium browsers will be closed if applicable
        logger.info(
            "Selenium-controlled browsers (if any were launched by this script) will be closed upon script termination."
        )
    else:
        error_message_body = (
            f"SCRIPT FINISHED WITH ERRORS (Exit Code: {EXIT_CODE_MAIN}).\n"
            f"Review logs above and in '{str(log_file_path)}' for details.\n"
            "Any opened browser windows might be in an inconsistent state."
        )
        logger.error("%s\n%s\n%s", "=" * 50, error_message_body, "=" * 50)
        # Optional pause for errors if run in a window that closes immediately
        # if sys.stdin.isatty(): input("--> Press Enter to acknowledge errors and exit. ")

    logger.info("Initiating final script shutdown sequence...")
    logging.shutdown()
    print(
        f"\nPython script execution process finished. Final exit code: {EXIT_CODE_MAIN}"
    )
    sys.exit(EXIT_CODE_MAIN)
