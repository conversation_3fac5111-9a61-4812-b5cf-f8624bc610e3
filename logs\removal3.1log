
================================================================================
SESSION START: 2025-03-30 19:20:19 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-30 19:20:19,629 | INFO     | __main__:add_log_handler:526 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-30 19:20:19,629 | DEBUG    | __main__:add_log_handler:527 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:20:19,629 | DEBUG    | __main__:add_log_handler:528 - Full logging path: C:\main\logs\removal3.1log
2025-03-30 19:20:19,629 | INFO     | __main__:add_log_handler:529 - Console logging disabled - all logs will only go to file
2025-03-30 19:22:33,813 | INFO     | __main__:handle_keypress:2704 - Exit requested by user via 'q' key
2025-03-30 19:22:35,124 | INFO     | __main__:handle_keypress:2704 - Exit requested by user via 'q' key
2025-03-30 19:22:38,752 | INFO     | __main__:handle_keypress:2704 - Exit requested by user via 'q' key
2025-03-30 19:22:39,307 | INFO     | __main__:handle_keypress:2704 - Exit requested by user via 'q' key
2025-03-30 19:22:39,661 | INFO     | __main__:handle_keypress:2704 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-30 19:23:00 - removal3.1.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-30 19:23:00,097 | INFO     | __main__:add_log_handler:526 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-30 19:23:00,097 | DEBUG    | __main__:add_log_handler:527 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:23:00,097 | DEBUG    | __main__:add_log_handler:528 - Full logging path: C:\main\logs\removal3.1log
2025-03-30 19:23:00,098 | INFO     | __main__:add_log_handler:529 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-03-31 00:11:05 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:11:05,956 | INFO     | __main__:add_log_handler:527 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:11:05,956 | DEBUG    | __main__:add_log_handler:528 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:11:05,956 | DEBUG    | __main__:add_log_handler:529 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:11:05,956 | INFO     | __main__:add_log_handler:530 - Console logging disabled - all logs will only go to file
2025-03-31 00:16:12,721 | ERROR    | __main__:main:2858 - Manual login timed out.

================================================================================
SESSION START: 2025-03-31 00:17:39 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:17:39,847 | INFO     | __main__:add_log_handler:527 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:17:39,847 | DEBUG    | __main__:add_log_handler:528 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:17:39,847 | DEBUG    | __main__:add_log_handler:529 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:17:39,847 | INFO     | __main__:add_log_handler:530 - Console logging disabled - all logs will only go to file
2025-03-31 00:18:03,725 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:18:28,625 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:18:49,981 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:19:11,356 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:19:32,683 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:19:54,000 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:20:15,330 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:20:36,701 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:20:58,072 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:21:19,428 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:21:40,783 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:22:02,189 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:22:23,584 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:22:44,937 | DEBUG    | __main__:handle_cloudflare_check:2769 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:22:51,025 | ERROR    | __main__:main:2942 - Manual login timed out.

================================================================================
SESSION START: 2025-03-31 00:23:30 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:23:30,197 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:23:30,197 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:23:30,197 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:23:30,197 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 00:23:49,658 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:23:49,659 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:23:54,775 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:24:10,906 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:24:10,908 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:24:16,046 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:24:32,466 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:24:32,468 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:24:37,543 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:24:37,548 | INFO     | __main__:main:2938 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=3e127c13-40e1-4c53-851a-ce60b85eff24&method=Email&state=none)
2025-03-31 00:26:22,012 | INFO     | __main__:remove_tip:1142 - Opening support chat for order 68a17f
2025-03-31 00:26:37,258 | INFO     | __main__:remove_tip:1153 - Sending initial message for order 68a17f
2025-03-31 00:26:47,548 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 1:
2025-03-31 00:26:47,548 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B630078850>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ "Ima Rowe\<EMAIL>\n\nhey remove dasher's tip and adjust to $0"
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B630078850>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:27:04,926 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 2:
2025-03-31 00:27:04,926 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B630078850>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ "Ima Rowe\<EMAIL>\n\nhey remove dasher's tip and adjust to $0"
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B630078850>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:27:23,660 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 3:
2025-03-31 00:27:23,660 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B630078850>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ "Ima Rowe\<EMAIL>\n\nhey remove dasher's tip and adjust to $0"
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B630078850>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:27:30,664 | ERROR    | __main__:remove_tip:1155 - Failed to send initial message for order 68a17f
2025-03-31 00:27:30,742 | INFO     | __main__:remove_tip:1142 - Opening support chat for order 498a42
2025-03-31 00:27:42,209 | INFO     | __main__:remove_tip:1153 - Sending initial message for order 498a42
2025-03-31 00:27:52,518 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 1:
2025-03-31 00:27:52,518 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 1
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B62FFEC2D0>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B62FFEC2D0>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:28:09,636 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 2:
2025-03-31 00:28:09,636 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 1
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B62FFEC2D0>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B62FFEC2D0>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:28:28,842 | ERROR    | __main__:send_message_to_support:1456 - Timeout exception details for attempt 3:
2025-03-31 00:28:28,843 | ERROR    | __main__:send_message_to_support:1457 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3065, in <module>
    main()
    └ <function main at 0x000002B62FFB7600>

  File "C:\main\removal3.1.py", line 3043, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <function monitor_orders_auto at 0x000002B62FFB6D40>

  File "C:\main\removal3.1.py", line 2395, in monitor_orders_auto
    process_single_order(
    └ <function process_single_order at 0x000002B62FFB7060>

  File "C:\main\removal3.1.py", line 2602, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 1
    │     │          └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    │     └ <function Order.remove_tip at 0x000002B62FFB5940>
    └ <__main__.Order object at 0x000002B62FFEC2D0>

  File "C:\main\removal3.1.py", line 1154, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000002B62FFB5BC0>
           └ <__main__.Order object at 0x000002B62FFEC2D0>

> File "C:\main\removal3.1.py", line 1385, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000002B62FBF56C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="dc91f11a07e748861558da1bc277b38b")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 00:28:35,846 | ERROR    | __main__:remove_tip:1155 - Failed to send initial message for order 498a42

================================================================================
SESSION START: 2025-03-31 00:30:19 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:30:19,344 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:30:19,344 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:30:19,344 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:30:19,344 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 00:30:39,998 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:30:40,001 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:30:45,091 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:31:01,112 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:31:01,117 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:31:06,200 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:31:22,463 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:31:22,493 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:31:27,822 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:31:43,914 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:31:43,915 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:31:50,528 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:32:07,024 | DEBUG    | __main__:handle_cloudflare_check:2775 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:32:07,026 | DEBUG    | __main__:is_intermediary_login_page:2814 - Checking if on intermediary login page...
2025-03-31 00:32:12,110 | DEBUG    | __main__:is_intermediary_login_page:2829 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:32:12,116 | INFO     | __main__:main:2938 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=22ef4b5f-d202-480b-a27b-8d2c7de6b54e&method=Email&state=none)
2025-03-31 00:33:46,645 | INFO     | __main__:remove_tip:1142 - Opening support chat for order 68a17f
2025-03-31 00:34:00,077 | INFO     | __main__:remove_tip:1153 - Sending initial message for order 68a17f
2025-03-31 00:34:05,710 | INFO     | __main__:remove_tip:1164 - Sending agent trigger message for order 68a17f
2025-03-31 00:34:10,895 | INFO     | __main__:remove_tip:1142 - Opening support chat for order 498a42
2025-03-31 00:34:22,896 | INFO     | __main__:remove_tip:1153 - Sending initial message for order 498a42
2025-03-31 00:34:28,715 | INFO     | __main__:remove_tip:1164 - Sending agent trigger message for order 498a42
2025-03-31 00:37:19,265 | INFO     | __main__:handle_keypress:2711 - Exit requested by user via 'q' key
2025-03-31 00:37:25,929 | INFO     | __main__:handle_keypress:2711 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-31 00:38:09 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:38:09,132 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:38:09,132 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:38:09,132 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:38:09,132 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 00:38:49,884 | DEBUG    | __main__:create_driver:906 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 00:39:13,902 | DEBUG    | __main__:handle_cloudflare_check:2780 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:39:13,909 | DEBUG    | __main__:is_intermediary_login_page:2851 - Checking if on intermediary login page...
2025-03-31 00:39:19,115 | DEBUG    | __main__:is_intermediary_login_page:2866 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:39:36,151 | DEBUG    | __main__:handle_cloudflare_check:2780 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 00:39:36,156 | DEBUG    | __main__:is_intermediary_login_page:2851 - Checking if on intermediary login page...
2025-03-31 00:39:41,260 | DEBUG    | __main__:is_intermediary_login_page:2866 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 00:39:41,266 | INFO     | __main__:main:2975 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=22d2c402-cbc2-494b-ba68-0ba39feb5d36&method=Email&state=none)
2025-03-31 00:41:04,829 | INFO     | __main__:remove_tip:1147 - Opening support chat for order 68a17f
2025-03-31 00:41:17,938 | INFO     | __main__:remove_tip:1158 - Sending initial message for order 68a17f
2025-03-31 00:41:23,553 | INFO     | __main__:remove_tip:1169 - Sending agent trigger message for order 68a17f
2025-03-31 00:41:28,830 | INFO     | __main__:remove_tip:1147 - Opening support chat for order 498a42
2025-03-31 00:41:39,777 | INFO     | __main__:remove_tip:1158 - Sending initial message for order 498a42
2025-03-31 00:41:45,546 | INFO     | __main__:remove_tip:1169 - Sending agent trigger message for order 498a42
2025-03-31 00:43:46,456 | INFO     | __main__:handle_keypress:2716 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-31 00:44:25 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 00:44:25,720 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 00:44:25,720 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:44:25,720 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 00:44:25,720 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 00:44:28,070 | DEBUG    | __main__:create_driver:906 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 01:02:54 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:02:54,763 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:02:54,763 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:02:54,763 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:02:54,763 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-03-31 01:04:17 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:04:17,119 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:04:17,119 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:04:17,119 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:04:17,119 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-03-31 01:04:44 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:04:44,575 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:04:44,575 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:04:44,575 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:04:44,575 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 01:04:48,195 | DEBUG    | __main__:create_driver:906 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 01:08:45 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:08:45,019 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:08:45,019 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:08:45,019 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:08:45,019 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-03-31 01:10:06 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:10:06,745 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:10:06,745 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:10:06,746 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:10:06,746 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 01:10:10,049 | DEBUG    | __main__:create_driver:907 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 01:15:24 - removal3.11.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:15:24,876 | INFO     | __main__:add_log_handler:526 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:15:24,876 | DEBUG    | __main__:add_log_handler:527 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:15:24,876 | DEBUG    | __main__:add_log_handler:528 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:15:24,876 | INFO     | __main__:add_log_handler:529 - Console logging disabled - all logs will only go to file
2025-03-31 01:17:06,170 | INFO     | __main__:remove_tip:1135 - Opening support chat for order 68a17f
2025-03-31 01:17:18,716 | INFO     | __main__:remove_tip:1146 - Sending initial message for order 68a17f

================================================================================
SESSION START: 2025-03-31 01:20:00 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:20:00,310 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:20:00,310 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:20:00,310 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:20:00,310 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 01:20:23,709 | DEBUG    | __main__:create_driver:910 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 01:26:36 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:26:36,373 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:26:36,373 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:26:36,373 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:26:36,373 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 01:26:38,579 | DEBUG    | __main__:create_driver:910 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 01:28:22,085 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 68a17f
2025-03-31 01:28:34,598 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 68a17f
2025-03-31 01:28:40,216 | INFO     | __main__:remove_tip:1173 - Sending agent trigger message for order 68a17f
2025-03-31 01:28:45,406 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 498a42
2025-03-31 01:28:57,239 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 498a42
2025-03-31 01:29:02,934 | INFO     | __main__:remove_tip:1173 - Sending agent trigger message for order 498a42
2025-03-31 01:35:10,229 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 68a17f
2025-03-31 01:35:21,431 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 68a17f
2025-03-31 01:35:31,668 | ERROR    | __main__:send_message_to_support:1465 - Timeout exception details for attempt 1:
2025-03-31 01:35:31,668 | ERROR    | __main__:send_message_to_support:1466 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>
    main()
    └ <function main at 0x000001D612F07600>

  File "C:\main\removal3.1.py", line 3078, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function monitor_orders_auto at 0x000001D612F06D40>

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    process_single_order(driver, order, index=i, total=len(eligible_orders))
    │                    │       │            │            └ [<__main__.Order object at 0x000001D612F0F4D0>, <__main__.Order object at 0x000001D612F0F450>]
    │                    │       │            └ 0
    │                    │       └ <__main__.Order object at 0x000001D612F0F4D0>
    │                    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function process_single_order at 0x000001D612F07060>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    │     └ <function Order.remove_tip at 0x000001D612F05940>
    └ <__main__.Order object at 0x000001D612F0F4D0>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F4D0>

> File "C:\main\removal3.1.py", line 1394, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000001D612B456C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 01:35:48,816 | ERROR    | __main__:send_message_to_support:1465 - Timeout exception details for attempt 2:
2025-03-31 01:35:48,816 | ERROR    | __main__:send_message_to_support:1466 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>
    main()
    └ <function main at 0x000001D612F07600>

  File "C:\main\removal3.1.py", line 3078, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function monitor_orders_auto at 0x000001D612F06D40>

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    process_single_order(driver, order, index=i, total=len(eligible_orders))
    │                    │       │            │            └ [<__main__.Order object at 0x000001D612F0F4D0>, <__main__.Order object at 0x000001D612F0F450>]
    │                    │       │            └ 0
    │                    │       └ <__main__.Order object at 0x000001D612F0F4D0>
    │                    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function process_single_order at 0x000001D612F07060>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    │     └ <function Order.remove_tip at 0x000001D612F05940>
    └ <__main__.Order object at 0x000001D612F0F4D0>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F4D0>

> File "C:\main\removal3.1.py", line 1394, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000001D612B456C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 01:36:07,682 | ERROR    | __main__:send_message_to_support:1465 - Timeout exception details for attempt 3:
2025-03-31 01:36:07,682 | ERROR    | __main__:send_message_to_support:1466 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>
    main()
    └ <function main at 0x000001D612F07600>

  File "C:\main\removal3.1.py", line 3078, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function monitor_orders_auto at 0x000001D612F06D40>

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    process_single_order(driver, order, index=i, total=len(eligible_orders))
    │                    │       │            │            └ [<__main__.Order object at 0x000001D612F0F4D0>, <__main__.Order object at 0x000001D612F0F450>]
    │                    │       │            └ 0
    │                    │       └ <__main__.Order object at 0x000001D612F0F4D0>
    │                    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function process_single_order at 0x000001D612F07060>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 0
    │     │          └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    │     └ <function Order.remove_tip at 0x000001D612F05940>
    └ <__main__.Order object at 0x000001D612F0F4D0>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHey, the app glitched - could you zero out the dasher tip for me?'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F4D0>

> File "C:\main\removal3.1.py", line 1394, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000001D612B456C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 01:36:14,685 | ERROR    | __main__:remove_tip:1164 - Failed to send initial message for order 68a17f
2025-03-31 01:36:14,774 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 498a42
2025-03-31 01:36:25,803 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 498a42
2025-03-31 01:36:36,106 | ERROR    | __main__:send_message_to_support:1465 - Timeout exception details for attempt 1:
2025-03-31 01:36:36,106 | ERROR    | __main__:send_message_to_support:1466 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>
    main()
    └ <function main at 0x000001D612F07600>

  File "C:\main\removal3.1.py", line 3078, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function monitor_orders_auto at 0x000001D612F06D40>

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    process_single_order(driver, order, index=i, total=len(eligible_orders))
    │                    │       │            │            └ [<__main__.Order object at 0x000001D612F0F4D0>, <__main__.Order object at 0x000001D612F0F450>]
    │                    │       │            └ 1
    │                    │       └ <__main__.Order object at 0x000001D612F0F450>
    │                    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function process_single_order at 0x000001D612F07060>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 1
    │     │          └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    │     └ <function Order.remove_tip at 0x000001D612F05940>
    └ <__main__.Order object at 0x000001D612F0F450>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHi, i want you to remove whole dasher tip and make it $0'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F450>

> File "C:\main\removal3.1.py", line 1394, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000001D612B456C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 01:36:52,793 | ERROR    | __main__:send_message_to_support:1465 - Timeout exception details for attempt 2:
2025-03-31 01:36:52,793 | ERROR    | __main__:send_message_to_support:1466 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>
    main()
    └ <function main at 0x000001D612F07600>

  File "C:\main\removal3.1.py", line 3078, in main
    monitor_orders_auto(driver)
    │                   └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function monitor_orders_auto at 0x000001D612F06D40>

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    process_single_order(driver, order, index=i, total=len(eligible_orders))
    │                    │       │            │            └ [<__main__.Order object at 0x000001D612F0F4D0>, <__main__.Order object at 0x000001D612F0F450>]
    │                    │       │            └ 1
    │                    │       └ <__main__.Order object at 0x000001D612F0F450>
    │                    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <function process_single_order at 0x000001D612F07060>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    order.remove_tip(driver, index=index, total=total, create_new_tab=False)
    │     │          │             │            └ 2
    │     │          │             └ 1
    │     │          └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    │     └ <function Order.remove_tip at 0x000001D612F05940>
    └ <__main__.Order object at 0x000001D612F0F450>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHi, i want you to remove whole dasher tip and make it $0'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F450>

> File "C:\main\removal3.1.py", line 1394, in send_message_to_support
    WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))
    │             │       │                            │  │                       └ ('tag name', 'textarea')
    │             │       │                            │  └ <function element_to_be_clickable at 0x000001D612B456C0>
    │             │       │                            └ <module 'selenium.webdriver.support.expected_conditions' from 'C:\\main\\.venv\\Lib\\site-packages\\selenium\\webdriver\\supp...
    │             │       └ 5
    │             └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.support.wait.WebDriverWait'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\support\wait.py", line 146, in until
    raise TimeoutException(message, screen, stacktrace)
          │                │        │       └ None
          │                │        └ None
          │                └ ''
          └ <class 'selenium.common.exceptions.TimeoutException'>

selenium.common.exceptions.TimeoutException: Message: 

2025-03-31 01:37:08,482 | ERROR    | __main__:send_message_to_support:1487 - Exception details for attempt 3:
2025-03-31 01:37:08,482 | ERROR    | __main__:send_message_to_support:1488 - Exception details:
Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 1433, in send_message_to_support
    text_input_element.send_keys(Keys.RETURN)
    │                  │         │    └ '\ue006'
    │                  │         └ <class 'selenium.webdriver.common.keys.Keys'>
    │                  └ <function WebElement.send_keys at 0x000001D6112B42C0>
    └ <undetected_chromedriver.webelement.WebElement (session="98f20538e9f64e7815b6272e9d31fd16", element="f.3C643A09403F6E022592D4...

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webelement.py", line 303, in send_keys
    self._execute(
    │    └ <function WebElement._execute at 0x000001D6112B4D60>
    └ <undetected_chromedriver.webelement.WebElement (session="98f20538e9f64e7815b6272e9d31fd16", element="f.3C643A09403F6E022592D4...
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webelement.py", line 572, in _execute
    return self._parent.execute(command, params)
           │    │       │       │        └ {'text': '\ue006', 'value': ['\ue006'], 'id': 'f.3C643A09403F6E022592D4C92C0E7603.d.9D1627A7FFD7E11C0AF79CF85A40A781.e.20'}
           │    │       │       └ 'sendKeysToElement'
           │    │       └ <function WebDriver.execute at 0x000001D61132C860>
           │    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           └ <undetected_chromedriver.webelement.WebElement (session="98f20538e9f64e7815b6272e9d31fd16", element="f.3C643A09403F6E022592D4...
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    │    │             │              └ {'status': 404, 'value': '{"value":{"error":"no such window","message":"no such window: target window already closed\\nfrom u...
    │    │             └ <function ErrorHandler.check_response at 0x000001D611222520>
    │    └ <selenium.webdriver.remote.errorhandler.ErrorHandler object at 0x000001D612E97B60>
    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
          │               │        │       └ ['\tGetHandleVerifier [0x0085C7F3+24435]', '\t(No symbol) [0x007E2074]', '\t(No symbol) [0x006B06E3]', '\t(No symbol) [0x0068...
          │               │        └ None
          │               └ 'no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=134.0.6998.178)'
          └ <class 'selenium.common.exceptions.NoSuchWindowException'>

selenium.common.exceptions.NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=134.0.6998.178)
Stacktrace:
	GetHandleVerifier [0x0085C7F3+24435]
	(No symbol) [0x007E2074]
	(No symbol) [0x006B06E3]
	(No symbol) [0x0068F83E]
	(No symbol) [0x0072455E]
	(No symbol) [0x0073EB19]
	(No symbol) [0x0071D5B6]
	(No symbol) [0x006EC54F]
	(No symbol) [0x006ED894]
	GetHandleVerifier [0x00B670A3+3213347]
	GetHandleVerifier [0x00B7B0C9+3295305]
	GetHandleVerifier [0x00B7558C+3271948]
	GetHandleVerifier [0x008F7360+658144]
	(No symbol) [0x007EB27D]
	(No symbol) [0x007E8208]
	(No symbol) [0x007E83A9]
	(No symbol) [0x007DAAC0]
	BaseThreadInitThunk [0x76355D49+25]
	RtlInitializeExceptionChain [0x7797CF6B+107]
	RtlGetAppContainerNamedObjectPath [0x7797CEF1+561]



During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 1437, in send_message_to_support
    ActionChains(driver).key_down(Keys.RETURN).key_up(Keys.RETURN).perform()
    │            │                │    │              │    └ '\ue006'
    │            │                │    │              └ <class 'selenium.webdriver.common.keys.Keys'>
    │            │                │    └ '\ue006'
    │            │                └ <class 'selenium.webdriver.common.keys.Keys'>
    │            └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <class 'selenium.webdriver.common.action_chains.ActionChains'>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\common\action_chains.py", line 94, in perform
    self.w3c_actions.perform()
    │    │           └ <function ActionBuilder.perform at 0x000001D61134C400>
    │    └ <selenium.webdriver.common.actions.action_builder.ActionBuilder object at 0x000001D612F7D550>
    └ <selenium.webdriver.common.action_chains.ActionChains object at 0x000001D612DA7E50>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\common\actions\action_builder.py", line 170, in perform
    self.driver.execute(Command.W3C_ACTIONS, enc)
    │    │      │       │       │            └ {'actions': [{'type': 'pointer', 'parameters': {'pointerType': 'mouse'}, 'id': 'mouse', 'actions': [{'type': 'pause', 'durati...
    │    │      │       │       └ 'actions'
    │    │      │       └ <class 'selenium.webdriver.remote.command.Command'>
    │    │      └ <function WebDriver.execute at 0x000001D61132C860>
    │    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
    └ <selenium.webdriver.common.actions.action_builder.ActionBuilder object at 0x000001D612F7D550>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    │    │             │              └ {'status': 404, 'value': '{"value":{"error":"no such window","message":"no such window: target window already closed\\nfrom u...
    │    │             └ <function ErrorHandler.check_response at 0x000001D611222520>
    │    └ <selenium.webdriver.remote.errorhandler.ErrorHandler object at 0x000001D612E97B60>
    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
          │               │        │       └ ['\tGetHandleVerifier [0x0085C7F3+24435]', '\t(No symbol) [0x007E2074]', '\t(No symbol) [0x006B06E3]', '\t(No symbol) [0x0068...
          │               │        └ None
          │               └ 'no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=134.0.6998.178)'
          └ <class 'selenium.common.exceptions.NoSuchWindowException'>

selenium.common.exceptions.NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=134.0.6998.178)
Stacktrace:
	GetHandleVerifier [0x0085C7F3+24435]
	(No symbol) [0x007E2074]
	(No symbol) [0x006B06E3]
	(No symbol) [0x0068F83E]
	(No symbol) [0x0072455E]
	(No symbol) [0x0073EB19]
	(No symbol) [0x0071D5B6]
	(No symbol) [0x006EC54F]
	(No symbol) [0x006ED894]
	GetHandleVerifier [0x00B670A3+3213347]
	GetHandleVerifier [0x00B7B0C9+3295305]
	GetHandleVerifier [0x00B7558C+3271948]
	GetHandleVerifier [0x008F7360+658144]
	(No symbol) [0x007EB27D]
	(No symbol) [0x007E8208]
	(No symbol) [0x007E83A9]
	(No symbol) [0x007DAAC0]
	BaseThreadInitThunk [0x76355D49+25]
	RtlInitializeExceptionChain [0x7797CF6B+107]
	RtlGetAppContainerNamedObjectPath [0x7797CEF1+561]



During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\main\removal3.1.py", line 3100, in <module>

  File "C:\main\removal3.1.py", line 3078, in main

  File "C:\main\removal3.1.py", line 2399, in monitor_orders_auto
    run_state.wait_if_paused()
    │         └ <function RunState.wait_if_paused at 0x000001D612F04680>
    └ <__main__.RunState object at 0x000001D612B34C20>

  File "C:\main\removal3.1.py", line 2605, in process_single_order
    except WebDriverException as error:
           └ <class 'selenium.common.exceptions.WebDriverException'>

  File "C:\main\removal3.1.py", line 1163, in remove_tip
    if not self.send_message_to_support(message, driver):
           │    │                       │        └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
           │    │                       └ 'Ima Rowe\<EMAIL>\n\nHi, i want you to remove whole dasher tip and make it $0'
           │    └ <function Order.send_message_to_support at 0x000001D612F05BC0>
           └ <__main__.Order object at 0x000001D612F0F450>

> File "C:\main\removal3.1.py", line 1440, in send_message_to_support
    driver.execute_script(
    │      └ <function WebDriver.execute_script at 0x000001D61132CC20>
    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>

  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 528, in execute_script
    return self.execute(command, {"script": script, "args": converted_args})["value"]
           │    │       │                   │               └ [<undetected_chromedriver.webelement.WebElement (session="98f20538e9f64e7815b6272e9d31fd16", element="f.3C643A09403F6E022592D...
           │    │       │                   └ "arguments[0].dispatchEvent(new KeyboardEvent('keydown', {'key': 'Enter'}));"
           │    │       └ 'w3cExecuteScript'
           │    └ <function WebDriver.execute at 0x000001D61132C860>
           └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\webdriver.py", line 429, in execute
    self.error_handler.check_response(response)
    │    │             │              └ {'status': 404, 'value': '{"value":{"error":"no such window","message":"no such window: target window already closed\\nfrom u...
    │    │             └ <function ErrorHandler.check_response at 0x000001D611222520>
    │    └ <selenium.webdriver.remote.errorhandler.ErrorHandler object at 0x000001D612E97B60>
    └ <undetected_chromedriver.Chrome (session="98f20538e9f64e7815b6272e9d31fd16")>
  File "C:\main\.venv\Lib\site-packages\selenium\webdriver\remote\errorhandler.py", line 232, in check_response
    raise exception_class(message, screen, stacktrace)
          │               │        │       └ ['\tGetHandleVerifier [0x0085C7F3+24435]', '\t(No symbol) [0x007E2074]', '\t(No symbol) [0x006B06E3]', '\t(No symbol) [0x0068...
          │               │        └ None
          │               └ 'no such window: target window already closed\nfrom unknown error: web view not found\n  (Session info: chrome=134.0.6998.178)'
          └ <class 'selenium.common.exceptions.NoSuchWindowException'>

selenium.common.exceptions.NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=134.0.6998.178)
Stacktrace:
	GetHandleVerifier [0x0085C7F3+24435]
	(No symbol) [0x007E2074]
	(No symbol) [0x006B06E3]
	(No symbol) [0x0068F83E]
	(No symbol) [0x0072455E]
	(No symbol) [0x0073EB19]
	(No symbol) [0x0071D5B6]
	(No symbol) [0x006EC54F]
	(No symbol) [0x006ED894]
	GetHandleVerifier [0x00B670A3+3213347]
	GetHandleVerifier [0x00B7B0C9+3295305]
	GetHandleVerifier [0x00B7558C+3271948]
	GetHandleVerifier [0x008F7360+658144]
	(No symbol) [0x007EB27D]
	(No symbol) [0x007E8208]
	(No symbol) [0x007E83A9]
	(No symbol) [0x007DAAC0]
	BaseThreadInitThunk [0x76355D49+25]
	RtlInitializeExceptionChain [0x7797CF6B+107]
	RtlGetAppContainerNamedObjectPath [0x7797CEF1+561]

2025-03-31 01:37:15,491 | ERROR    | __main__:remove_tip:1164 - Failed to send initial message for order 498a42

================================================================================
SESSION START: 2025-03-31 01:37:54 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 01:37:54,277 | INFO     | __main__:add_log_handler:528 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 01:37:54,277 | DEBUG    | __main__:add_log_handler:529 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:37:54,277 | DEBUG    | __main__:add_log_handler:530 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 01:37:54,277 | INFO     | __main__:add_log_handler:531 - Console logging disabled - all logs will only go to file
2025-03-31 01:37:57,337 | DEBUG    | __main__:create_driver:910 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 01:38:20,729 | DEBUG    | __main__:handle_cloudflare_check:2770 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 01:38:20,731 | DEBUG    | __main__:is_intermediary_login_page:2841 - Checking if on intermediary login page...
2025-03-31 01:38:26,038 | DEBUG    | __main__:is_intermediary_login_page:2856 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 01:38:42,118 | DEBUG    | __main__:handle_cloudflare_check:2770 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 01:38:42,119 | DEBUG    | __main__:is_intermediary_login_page:2841 - Checking if on intermediary login page...
2025-03-31 01:38:47,221 | DEBUG    | __main__:is_intermediary_login_page:2856 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 01:38:47,226 | INFO     | __main__:main:2965 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=df84ee72-e6ee-4544-858c-bc72da2f6369&method=Email&state=none)
2025-03-31 01:40:55,557 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 68a17f
2025-03-31 01:41:09,168 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 68a17f
2025-03-31 01:41:14,759 | INFO     | __main__:remove_tip:1173 - Sending agent trigger message for order 68a17f
2025-03-31 01:41:19,949 | INFO     | __main__:remove_tip:1151 - Opening support chat for order 498a42
2025-03-31 01:41:32,023 | INFO     | __main__:remove_tip:1162 - Sending initial message for order 498a42
2025-03-31 01:41:37,722 | INFO     | __main__:remove_tip:1173 - Sending agent trigger message for order 498a42
2025-03-31 01:41:59,597 | INFO     | __main__:handle_keypress:2706 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-31 02:33:59 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 02:33:59,455 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 02:33:59,455 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 02:33:59,455 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 02:33:59,455 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file
2025-03-31 02:34:03,801 | DEBUG    | __main__:create_driver:912 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 02:34:24,870 | INFO     | __main__:handle_keypress:2708 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-31 02:34:49 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 02:34:49,443 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 02:34:49,443 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 02:34:49,443 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 02:34:49,443 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file
2025-03-31 02:34:50,216 | DEBUG    | __main__:create_driver:912 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 02:35:14,159 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:35:14,161 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:35:19,229 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:35:35,435 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:35:35,436 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:35:40,506 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:35:56,692 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:35:56,693 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:36:01,762 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:36:17,977 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:36:17,978 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:36:23,046 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:36:39,258 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:36:39,259 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:36:44,323 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:37:00,540 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:37:00,541 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:37:05,606 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:37:21,841 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:37:21,842 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:37:26,912 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:37:43,125 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:37:43,127 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:37:48,206 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:38:04,452 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:38:04,454 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:38:09,529 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:38:25,743 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:38:25,745 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:38:30,822 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:38:47,042 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:38:47,044 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:38:52,118 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:39:08,309 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:39:08,310 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:39:13,399 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:39:29,615 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:39:29,617 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:39:34,709 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:39:50,939 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:39:50,941 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:39:56,032 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:40:12,309 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 02:40:12,311 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-03-31 02:40:17,380 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 02:40:18,385 | ERROR    | __main__:main:3089 - Manual login timed out.

================================================================================
SESSION START: 2025-03-31 04:08:21 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:08:21,125 | INFO     | __main__:add_log_handler:550 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:08:21,125 | DEBUG    | __main__:add_log_handler:551 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:08:21,125 | DEBUG    | __main__:add_log_handler:552 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:08:21,125 | INFO     | __main__:add_log_handler:553 - Console logging disabled - all logs will only go to file
2025-03-31 04:08:24,641 | DEBUG    | __main__:create_driver:928 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 04:09:33 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:09:33,223 | INFO     | __main__:add_log_handler:550 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:09:33,224 | DEBUG    | __main__:add_log_handler:551 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:09:33,224 | DEBUG    | __main__:add_log_handler:552 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:09:33,224 | INFO     | __main__:add_log_handler:553 - Console logging disabled - all logs will only go to file
2025-03-31 04:09:35,532 | DEBUG    | __main__:create_driver:928 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-03-31 04:12:04 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:12:04,354 | INFO     | __main__:add_log_handler:548 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:12:04,354 | DEBUG    | __main__:add_log_handler:549 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:12:04,354 | DEBUG    | __main__:add_log_handler:550 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:12:04,354 | INFO     | __main__:add_log_handler:551 - Console logging disabled - all logs will only go to file
2025-03-31 04:12:08,422 | DEBUG    | __main__:create_driver:926 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 04:13:22,198 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:13:22,200 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:13:27,274 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:13:43,524 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:13:43,525 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:13:48,587 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:14:04,840 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:14:04,842 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:14:09,919 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:14:26,141 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:14:26,143 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:14:31,237 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:14:47,482 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:14:47,484 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:14:52,566 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:15:08,807 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:15:08,809 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:15:13,883 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:15:30,116 | DEBUG    | __main__:handle_cloudflare_check:2794 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:15:30,118 | DEBUG    | __main__:is_intermediary_login_page:2865 - Checking if on intermediary login page...
2025-03-31 04:15:35,199 | DEBUG    | __main__:is_intermediary_login_page:2880 - Intermediary login page NOT detected (Login button element NOT found within timeout).

================================================================================
SESSION START: 2025-03-31 04:24:53 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:24:53,558 | INFO     | __main__:add_log_handler:550 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:24:53,558 | DEBUG    | __main__:add_log_handler:551 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:24:53,558 | DEBUG    | __main__:add_log_handler:552 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:24:53,558 | INFO     | __main__:add_log_handler:553 - Console logging disabled - all logs will only go to file
2025-03-31 04:24:56,082 | DEBUG    | __main__:create_driver:928 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 04:26:08,889 | DEBUG    | __main__:handle_cloudflare_check:2795 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:26:08,891 | DEBUG    | __main__:is_intermediary_login_page:2866 - Checking if on intermediary login page...
2025-03-31 04:26:14,138 | DEBUG    | __main__:is_intermediary_login_page:2881 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:26:30,594 | DEBUG    | __main__:handle_cloudflare_check:2795 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:26:30,595 | DEBUG    | __main__:is_intermediary_login_page:2866 - Checking if on intermediary login page...
2025-03-31 04:26:35,613 | DEBUG    | __main__:is_intermediary_login_page:2881 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:26:35,623 | INFO     | __main__:main:3105 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=eebe3483-6844-4157-829f-3b9fd9169abc&method=Email&state=none)
2025-03-31 04:27:41,504 | INFO     | __main__:handle_keypress:2731 - Exit requested by user via 'q' key

================================================================================
SESSION START: 2025-03-31 04:44:02 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:44:02,845 | INFO     | __main__:add_log_handler:455 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:44:02,845 | DEBUG    | __main__:add_log_handler:456 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:44:02,845 | DEBUG    | __main__:add_log_handler:457 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:44:02,845 | INFO     | __main__:add_log_handler:458 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-03-31 04:46:25 - removal3.12.py
Command Arguments: None
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:46:25,597 | INFO     | __main__:add_log_handler:455 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:46:25,597 | DEBUG    | __main__:add_log_handler:456 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:46:25,597 | DEBUG    | __main__:add_log_handler:457 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:46:25,597 | INFO     | __main__:add_log_handler:458 - Console logging disabled - all logs will only go to file
2025-03-31 04:46:44,630 | DEBUG    | __main__:create_driver:791 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 04:47:11,279 | DEBUG    | __main__:handle_cloudflare_check:2350 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:47:11,281 | DEBUG    | __main__:is_intermediary_login_page:2414 - Checking if on intermediary login page...
2025-03-31 04:47:16,413 | DEBUG    | __main__:is_intermediary_login_page:2428 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:47:35,378 | DEBUG    | __main__:handle_cloudflare_check:2350 - Cloudflare checkbox not found within timeout - assuming not present.
2025-03-31 04:47:35,398 | DEBUG    | __main__:is_intermediary_login_page:2414 - Checking if on intermediary login page...
2025-03-31 04:47:40,820 | DEBUG    | __main__:is_intermediary_login_page:2428 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-03-31 04:47:40,826 | INFO     | __main__:main:2525 - Manual login successful (URL: https://www.doordash.com/home?action=Login&code=23053bc6-abfa-4884-af66-3bc5747d4842&method=Email&state=none)

================================================================================
SESSION START: 2025-03-31 04:50:18 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-03-31 04:50:18,095 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-03-31 04:50:18,095 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 04:50:18,095 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-03-31 04:50:18,095 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file
2025-03-31 04:50:20,806 | DEBUG    | __main__:create_driver:912 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-03-31 04:52:05,853 | INFO     | __main__:remove_tip:1153 - Opening support chat for order 68a17f
2025-03-31 04:52:20,007 | INFO     | __main__:remove_tip:1164 - Sending initial message for order 68a17f
2025-03-31 04:52:25,575 | INFO     | __main__:remove_tip:1175 - Sending agent trigger message for order 68a17f
2025-03-31 04:52:30,755 | INFO     | __main__:remove_tip:1153 - Opening support chat for order 498a42
2025-03-31 04:52:42,777 | INFO     | __main__:remove_tip:1164 - Sending initial message for order 498a42
2025-03-31 04:52:48,547 | INFO     | __main__:remove_tip:1175 - Sending agent trigger message for order 498a42

================================================================================
SESSION START: 2025-04-01 20:53:48 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-04-01 20:53:48,721 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-04-01 20:53:48,721 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-01 20:53:48,721 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-04-01 20:53:48,721 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-05-31 09:49:22 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-05-31 09:49:22,365 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-05-31 09:49:22,365 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-05-31 09:49:22,365 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-05-31 09:49:22,365 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file
2025-05-31 09:49:26,213 | DEBUG    | __main__:create_driver:912 - Password save prompts DISABLED via Chrome Preferences and Arguments.

================================================================================
SESSION START: 2025-06-08 00:09:38 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-06-08 00:09:38,613 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-06-08 00:09:38,614 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-06-08 00:09:38,614 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-06-08 00:09:38,614 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file

================================================================================
SESSION START: 2025-07-25 00:03:15 - removal3.1.py
Command Arguments: --skip-dependency-check --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: True
  - Max Orders Per Batch: 30
  - Cycle Size: 3
  - Log Level: WARNING
================================================================================
2025-07-25 00:03:15,583 | INFO     | __main__:add_log_handler:530 - Log file initialized at: logs\removal3.1log (Sink ID: 1)
2025-07-25 00:03:15,584 | DEBUG    | __main__:add_log_handler:531 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:03:15,584 | DEBUG    | __main__:add_log_handler:532 - Full logging path: C:\main\logs\removal3.1log
2025-07-25 00:03:15,584 | INFO     | __main__:add_log_handler:533 - Console logging disabled - all logs will only go to file
2025-07-25 00:03:18,967 | DEBUG    | __main__:create_driver:912 - Password save prompts DISABLED via Chrome Preferences and Arguments.
2025-07-25 00:03:41,613 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:03:41,614 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:03:46,685 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:04:02,901 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:04:02,902 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:04:07,981 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:04:24,222 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:04:24,224 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:04:29,308 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:04:45,585 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:04:45,587 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:04:50,683 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:05:06,951 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:05:06,953 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:05:12,040 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:05:28,293 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:05:28,294 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:05:33,376 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:05:49,596 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:05:49,597 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:05:54,673 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:06:10,970 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:06:10,973 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:06:16,143 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:06:32,435 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:06:32,438 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:06:37,526 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:06:53,746 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:06:53,747 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:06:58,856 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:07:15,081 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:07:15,083 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:07:20,165 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:07:36,390 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:07:36,392 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:07:41,474 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:07:57,704 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:07:57,706 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:08:02,788 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:08:19,030 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:08:19,032 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:08:24,110 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:08:40,368 | DEBUG    | __main__:handle_cloudflare_check:2772 - Cloudflare checkbox not found within timeout - assuming not present.
2025-07-25 00:08:40,370 | DEBUG    | __main__:is_intermediary_login_page:2843 - Checking if on intermediary login page...
2025-07-25 00:08:45,458 | DEBUG    | __main__:is_intermediary_login_page:2858 - Intermediary login page NOT detected (Login button element NOT found within timeout).
2025-07-25 00:08:46,464 | ERROR    | __main__:main:3098 - Manual login timed out.
