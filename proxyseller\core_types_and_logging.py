"""
Core types and logging setup for ProxySeller Firefox Container Setup Tool.
Includes AppConfig, ProxyInfo, ContainerInfo, and structlog setup.
"""
import argparse
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional
import structlog

@dataclass
class ProxyInfo:
    """
    Represents a single proxy instance from ProxySeller.
    """
    ip: str
    port: int
    proxy_type: str
    api_id: str
    username: Optional[str] = None
    password: Optional[str] = None
    country: Optional[str] = None
    status: Optional[str] = None
    raw_data: Optional[Dict[str, Any]] = field(default_factory=dict)

@dataclass
class ContainerInfo:
    """
    Represents a Firefox container definition.
    """
    user_context_id: int
    name: str
    icon: str
    color: str
    telemetry_id: str = ""
    access_key: str = ""
    public: bool = True

class AppConfig:
    """
    Holds all configuration for the ProxySeller Firefox Container Setup Tool.
    """
    CONTAINER_COLORS = ["blue", "turquoise", "green", "yellow", "orange", "red", "pink", "purple"]
    CONTAINER_ICONS = [
        "fingerprint", "briefcase", "dollar", "cart", "gift", "food", "vacation", "pet", "tree", "chill", "circle"
    ]
    API_PROXY_LIST_KEY = "ipv4"
    LOG_FILE_NAME = "proxy_setup.log"

    def __init__(self, cli_args: argparse.Namespace):
        self.api_key: Optional[str] = getattr(cli_args, "api_key", None)
        self.num_containers: int = getattr(cli_args, "num_containers", 5)
        self.proxy_type: str = getattr(cli_args, "proxy_type", "http")
        self.firefox_profile_path: Optional[str] = getattr(cli_args, "profile", None)
        self.skip_import: bool = getattr(cli_args, "skip_import", False)
        self.log_level: str = getattr(cli_args, "log_level", "INFO")
        self.foxyproxy_uuid_manual: Optional[str] = getattr(cli_args, "foxyproxy_uuid", None)
        # Add other CLI/config fields as needed

    def prompt_for_global_overrides(self):
        # Placeholder for interactive override logic if needed
        pass


def setup_structlog(log_level: str, log_file: Path, app_config: Optional[AppConfig] = None):
    """
    Configures structlog for structured logging to both console and file.
    """
    processors = [
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.dict_tracebacks,
        structlog.processors.JSONRenderer(),
    ]
    logging.basicConfig(
        level=getattr(logging, log_level, logging.INFO),
        format="%(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, mode="w", encoding="utf-8"),
        ],
    )
    structlog.configure(
        processors=processors,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    return structlog.get_logger()
