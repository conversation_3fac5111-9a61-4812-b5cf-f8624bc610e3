Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Requirement already satisfied: pip in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (25.1.1)
Looking in indexes: https://pypi.org/simple, https://pypi.ngc.nvidia.com
Requirement already satisfied: selenium in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (4.32.0)
Requirement already satisfied: webdriver-manager in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (4.0.2)
Requirement already satisfied: requests in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (2.32.3)
Requirement already satisfied: urllib3<3,>=1.26 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from urllib3[socks]<3,>=1.26->selenium) (2.3.0)
Requirement already satisfied: trio~=0.17 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from selenium) (0.28.0)
Requirement already satisfied: trio-websocket~=0.9 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from selenium) (0.11.1)
Requirement already satisfied: certifi>=2021.10.8 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from selenium) (2025.1.31)
Requirement already satisfied: typing_extensions~=4.9 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from selenium) (4.12.2)
Requirement already satisfied: websocket-client~=1.8 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from selenium) (1.8.0)
Requirement already satisfied: attrs>=23.2.0 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (25.1.0)
Requirement already satisfied: sortedcontainers in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (2.4.0)
Requirement already satisfied: idna in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (3.10)
Requirement already satisfied: outcome in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (1.3.0.post0)
Requirement already satisfied: sniffio>=1.3.0 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (1.3.1)
Requirement already satisfied: cffi>=1.14 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio~=0.17->selenium) (1.17.1)
Requirement already satisfied: wsproto>=0.14 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from trio-websocket~=0.9->selenium) (1.2.0)
Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from urllib3[socks]<3,>=1.26->selenium) (1.7.1)
Requirement already satisfied: python-dotenv in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from webdriver-manager) (1.0.0)
Requirement already satisfied: packaging in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from webdriver-manager) (24.2)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from requests) (3.4.1)
Requirement already satisfied: pycparser in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from cffi>=1.14->trio~=0.17->selenium) (2.22)
Requirement already satisfied: h11<1,>=0.9.0 in c:\users\<USER>\anaconda3\envs\newenv\lib\site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium) (0.14.0)
