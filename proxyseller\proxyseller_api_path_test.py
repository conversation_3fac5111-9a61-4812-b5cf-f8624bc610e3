import requests
import json
import logging
import time
import argparse
import sys

class ProxySellerAPI:
    def __init__(self, api_key, base_url=None):
        self.api_key = api_key
        self.base_url = base_url or "https://proxy-seller.com"
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self.logger = logging.getLogger('ProxySellerAPI')
        self.api_path = self._discover_working_api_path()

    def _discover_working_api_path(self):
        potential_paths = [
            "/v1",
            "/api/v1",
            "",
            "/api"
        ]
        for path in potential_paths:
            test_url = f"{self.base_url}{path}/balance/get"
            try:
                response = self.session.head(test_url, timeout=5)
                if response.status_code < 400:
                    response = self.session.get(test_url, timeout=5)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            if isinstance(data, dict) and ('status' in data or 'data' in data):
                                self.logger.info(f"Found working API path: {path}")
                                return path
                        except json.JSONDecodeError:
                            pass
            except requests.RequestException as e:
                self.logger.debug(f"Failed with path {path}: {str(e)}")
                continue
        self.logger.warning("Could not find working API path - defaulting to /v1")
        return "/v1"

    def make_request(self, endpoint, method="GET", data=None, params=None, max_retries=3):
        url = f"{self.base_url}{self.api_path}/{endpoint.lstrip('/')}"
        self.logger.debug(f"Making {method} request to {url}")
        for attempt in range(max_retries):
            try:
                if method.upper() == "GET":
                    response = self.session.get(url, params=params, timeout=10)
                elif method.upper() == "POST":
                    response = self.session.post(url, json=data, timeout=10)
                if response.status_code < 400:
                    try:
                        return response.json()
                    except json.JSONDecodeError:
                        self.logger.warning(f"Received non-JSON response: {response.text[:200]}...")
                if response.status_code in (429, 503, 504):
                    wait_time = min(2 ** attempt, 30)
                    self.logger.warning(f"Rate limited or server error, waiting {wait_time}s")
                    time.sleep(wait_time)
                    continue
                response.raise_for_status()
            except requests.RequestException as e:
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    self.logger.warning(f"Request error: {str(e)}. Retrying in {wait_time}s")
                    time.sleep(wait_time)
                else:
                    self.logger.error(f"Failed after {max_retries} attempts: {str(e)}")
                    raise
        self.logger.error(f"All {max_retries} requests to {url} failed")
        return {"status": "error", "message": f"Failed to connect to API after {max_retries} attempts"}

    def get_balance(self):
        return self.make_request("balance/get")

    def get_proxy_list(self, proxy_type="ipv4"):
        return self.make_request(f"proxy/list/{proxy_type}")

    def get_reference_list(self, proxy_type="ipv4"):
        return self.make_request(f"reference/list/{proxy_type}")

    def order_proxy(self, proxy_type="ipv4", country_id=None, period_id=None, quantity=1, target_id=None):
        data = {
            "countryId": country_id,
            "periodId": period_id,
            "quantity": quantity
        }
        if target_id:
            data["targetId"] = target_id
        return self.make_request(f"proxy/order/{proxy_type}", method="POST", data=data)

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    parser = argparse.ArgumentParser(description="ProxySeller API Path Test Tool")
    parser.add_argument("--api-key", required=True, help="Your ProxySeller API key")
    parser.add_argument("--base-url", help="Alternative API base URL to try")
    parser.add_argument("--test-all", action="store_true", help="Test all major API endpoints")
    parser.add_argument("--verbose", action="store_true", help="Show detailed debug information")
    args = parser.parse_args()
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    api = ProxySellerAPI(args.api_key, base_url=args.base_url)
    print(f"Testing ProxySeller API connection...")
    try:
        balance = api.get_balance()
        print(f"Connection result: {json.dumps(balance, indent=2)}")
        if args.test_all:
            print("\nTesting proxy list retrieval...")
            proxies = api.get_proxy_list()
            print(f"Proxy list response: {json.dumps(proxies, indent=2)[:1000]}")
            print("\nTesting reference data retrieval...")
            ref_data = api.get_reference_list()
            print(f"Reference list response: {json.dumps(ref_data, indent=2)[:1000]}")
        print("\nAPI connection and functionality test complete!")
        sys.exit(0)
    except Exception as e:
        print(f"API test failed: {str(e)}")
        sys.exit(1)
