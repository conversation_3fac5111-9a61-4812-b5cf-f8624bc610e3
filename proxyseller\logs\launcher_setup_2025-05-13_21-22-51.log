2025-05-13 21:22:51 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:22:51 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:22:51 [DEBUG] Checking for Python installation...
2025-05-13 21:22:51 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:22:51 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:22:51 [DEBUG] Checking for package: selenium
2025-05-13 21:22:52 [DEBUG] Package 'selenium' found.
2025-05-13 21:22:52 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:22:53 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:22:53 [DEBUG] Checking for package: requests
2025-05-13 21:22:54 [DEBUG] Package 'requests' found.
2025-05-13 21:22:54 [DEBUG] Checking for package: configparser
2025-05-13 21:22:55 [DEBUG] Package 'configparser' found.
2025-05-13 21:22:55 [INFO] All required Python packages seem to be installed.
2025-05-13 21:22:55 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:22:55 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:22:55 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:22:56 [INFO] Launching main Python script...
2025-05-13 21:22:56 [DEBUG] Full command: D:\Users\d0nbx\anaconda3\python.exe "C:\main\proxyseller\main.py"
2025-05-13 21:22:58 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-22-51.log
2025-05-13 21:22:58 [ERROR] Python script exited with code 1.
2025-05-13 21:22:58 [INFO] Launcher script execution finished. Final Exit Code: 1
2025-05-13 21:22:58 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
