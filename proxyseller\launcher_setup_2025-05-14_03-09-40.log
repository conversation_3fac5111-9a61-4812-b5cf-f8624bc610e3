2025-05-14 03:09:40 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-14 03:09:40 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-14 03:09:40 [DEBUG] Checking for Python installation...
2025-05-14 03:09:40 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-14 03:09:40 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-14 03:09:40 [DEBUG] Checking for package: selenium
2025-05-14 03:09:41 [DEBUG] Package 'selenium' found.
2025-05-14 03:09:41 [DEBUG] Checking for package: webdriver_manager
2025-05-14 03:09:42 [DEBUG] Package 'webdriver_manager' found.
2025-05-14 03:09:42 [DEBUG] Checking for package: requests
2025-05-14 03:09:43 [DEBUG] Package 'requests' found.
2025-05-14 03:09:43 [DEBUG] Checking for package: configparser
2025-05-14 03:09:44 [DEBUG] Package 'configparser' found.
2025-05-14 03:09:44 [INFO] All required Python packages seem to be installed.
2025-05-14 03:09:44 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-14 03:09:44 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-14 03:09:44 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-14 03:09:49 [INFO] Launching main Python script...
2025-05-14 03:09:49 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py"
2025-05-14 03:09:55 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-14_03-09-40.log
2025-05-14 03:09:55 [ERROR] Python script exited with code 1.
2025-05-14 03:09:55 [INFO] Launcher script execution finished. Final Exit Code: 1
