#!/usr/bin/env python3
import os
import platform
import subprocess
import sys
from pathlib import Path
from subprocess import CalledProcessError


def main():
    """
    Simple launcher for the removal script that ensures proper environment
    """
    # Get absolute path to the script directory using Path for better cross-platform compatibility
    script_dir = Path(__file__).parent.absolute()

    # Path to the main script
    main_script = script_dir / "removal3.1.py"

    # Make sure our working directory is the script directory
    os.chdir(script_dir)

    try:
        # Add skip dependency check flag to arguments
        args = [sys.executable, str(main_script), "--skip-dependency-check"] + sys.argv[1:]

        # Run the script with all arguments passed to this launcher
        result = subprocess.run(args, check=True)
        return result.returncode
    except KeyboardInterrupt:
        print("\nProgram terminated by user")
        return 1
    except CalledProcessError as e:
        print(f"Script execution failed with error code {e.returncode}")
        return e.returncode
    except FileNotFoundError as e:
        print(f"Error: Script file not found - {e}")
        return 1
    except PermissionError as e:
        print(f"Error: Permission denied - {e}")
        return 1
    except OSError as e:
        print(f"OS Error: {e}")
        return 1
    except ImportError as e:
        print(f"Import error: {e}")
        return 1
    except ValueError as e:
        print(f"Value error: {e}")
        return 1
    except (subprocess.SubprocessError, subprocess.TimeoutExpired) as e:
        print(f"Subprocess error: {e}")
        return 1
    # If we get here, something truly unexpected happened that we can't handle specifically
    # We still need to catch it to prevent crashing, but we'll log it clearly for debugging
    except BaseException as e:  # pylint: disable=broad-except
        print(f"CRITICAL ERROR: Unexpected error type {type(e).__name__}: {e}")
        print("Please report this with the following information:")
        print(f"Python version: {sys.version}")
        print(f"OS: {platform.system()} {platform.release()}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
