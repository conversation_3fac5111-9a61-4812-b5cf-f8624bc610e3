{"$help": "https://aka.ms/terminal-documentation", "$schema": "https://raw.githubusercontent.com/microsoft/terminal/main/doc/cascadia/profiles.schema.json", "actions": [{"command": "terminalChat", "id": "User.terminalChat"}], "aiConfig": {"activeProvider": "githubCopilot"}, "copyFormatting": "none", "copyOnSelect": false, "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "initialCols": 130, "initialRows": 35, "keybindings": [{"id": "Terminal.CopyToClipboard", "keys": "ctrl+c"}, {"id": "Terminal.PasteFromClipboard", "keys": "ctrl+v"}, {"id": "Terminal.DuplicatePaneAuto", "keys": "alt+shift+d"}, {"id": "User.terminalChat", "keys": "ctrl+alt+g"}], "newTabMenu": [{"type": "remainingProfiles"}], "profiles": {"defaults": {"colorScheme": "Dark+", "elevate": true, "experimental.rainbowSuggestions": true, "experimental.repositionCursorWithMouse": true, "font": {"face": "JetBrainsMono Nerd Font", "features": {"aalt": 0, "frac": 0, "mkmk": 1, "ordn": 0, "zero": 0}}, "startingDirectory": null}, "list": [{"colorScheme": "Dark+", "commandline": "%SystemRoot%\\System32\\WindowsPowerShell\\v1.0\\powershell.exe", "font": {"face": "JetBrainsMono Nerd Font Mono", "features": {"aalt": 0, "calt": 1, "case": 0, "ccmp": 1, "cv01": 0, "cv02": 0, "cv03": 0, "cv04": 0, "cv05": 0, "cv06": 0, "cv07": 0, "cv08": 0, "cv09": 0, "cv10": 0, "cv11": 0, "cv12": 0, "cv14": 0, "cv15": 0, "cv16": 0, "cv17": 0, "cv18": 0, "cv19": 0, "cv20": 0, "cv99": 0, "frac": 0, "mark": 1, "mkmk": 1, "ordn": 0, "sinf": 0, "ss01": 0, "ss02": 0, "ss19": 0, "ss20": 0, "subs": 0, "sups": 0, "zero": 0}}, "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}", "hidden": false, "name": "Windows PowerShell", "startingDirectory": null}, {"commandline": "%SystemRoot%\\System32\\cmd.exe", "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}", "hidden": false, "name": "Command Prompt"}, {"guid": "{574e775e-4f2a-5b96-ac1e-a2962a402336}", "hidden": false, "name": "PowerShell", "source": "Windows.Terminal.PowershellCore", "startingDirectory": "."}, {"guid": "{2c4de342-38b7-51cf-b940-2309a097f518}", "hidden": false, "name": "Ubuntu", "source": "Windows.Terminal.Wsl"}, {"guid": "{b453ae62-4e3d-5e58-b989-0a998ec441b8}", "hidden": false, "name": "Azure Cloud Shell", "source": "Windows.Terminal.Azure"}, {"guid": "{f1499698-5b0e-5946-9a86-1cabc1e0e6e8}", "hidden": false, "name": "Developer Command Prompt for VS 2022", "source": "Windows.Terminal.VisualStudio"}, {"guid": "{d82d437c-90dc-5d13-9d80-3db61031eff8}", "hidden": false, "name": "Developer PowerShell for VS 2022", "source": "Windows.Terminal.VisualStudio"}, {"guid": "{6b54aec4-01d2-5809-bac4-e94725353a87}", "hidden": false, "name": "Developer Command Prompt for VS 2019", "source": "Windows.Terminal.VisualStudio"}, {"elevate": false, "guid": "{b7cc8352-8582-565a-9710-e570a09269fd}", "hidden": true, "name": "Developer PowerShell for VS 2019", "source": "Windows.Terminal.VisualStudio"}, {"guid": "{8fe03d6f-e65f-5e28-a817-c1e5b1ec0ec1}", "hidden": false, "name": "Ubuntu", "source": "Microsoft.WSL"}, {"guid": "{c383afe0-0e3b-5827-bc27-0dc0e565c26b}", "hidden": false, "name": "kali-linux", "source": "Microsoft.WSL"}, {"guid": "{2ece5bfe-50ed-5f3a-ab87-5cd4baafed2b}", "hidden": false, "name": "<PERSON><PERSON>", "source": "Git"}, {"guid": "{f60dc41d-f3ac-57b9-9830-0998ae787071}", "hidden": false, "name": "Ubuntu-24.04", "source": "Microsoft.WSL"}]}, "schemes": [], "themes": [], "useAcrylicInTabRow": true}