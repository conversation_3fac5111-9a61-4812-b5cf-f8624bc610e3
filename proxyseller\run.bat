#!/bin/bash

# Setup script for Firefox Container Proxy Tool

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python
if ! command_exists python3; then
    echo "Error: Python 3 is not installed. Please install it first."
    exit 1
fi

# Check Git
if ! command_exists git; then
    echo "Error: Git is not installed. Please install it first."
    exit 1
fi

# Check Selenium
python3 -c "import selenium" >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Installing Selenium..."
    pip3 install selenium
fi

# Check Firefox WebDriver
if ! command_exists geckodriver; then
    echo "Error: Firefox WebDriver (geckodriver) is not installed."
    echo "Please install it following the instructions at: https://github.com/mozilla/geckodriver/releases"
    exit 1
fi

# Create main script
cat > firefox_container_proxy_setup.py << 'EOL'
# Full Python script goes here (copy the entire script from above)
EOL

# Make it executable
chmod +x firefox_container_proxy_setup.py

echo "Setup complete! You can now run the tool with:"
echo "./firefox_container_proxy_setup.py -k ecccd2b73700761bf93684c1a431170f -n 10"
echo ""
echo "Options:"
echo "  -k, --api-key       Proxy-Seller API key (required)"
echo "  -n, --num-containers  Number of containers to create (default: 5)"
echo "  -t, --proxy-type    Type of proxy to use: http or socks5 (default: http)"
echo "  -p, --profile-path  Path to Firefox profile (optional)"
