﻿# launcher.ps1 - Main PowerShell script with enhanced path detection and script launching
# Last updated: March 2025

# ANSI escape codes for styling
$ESC = [char]27
$RESET = "$ESC[0m"
$BOLD = "$ESC[1m"
$RED = "$ESC[31m"
$GREEN = "$ESC[32m"
$YELLOW = "$ESC[33m"
$BLUE = "$ESC[34m"
$MAGENTA = "$ESC[35m"
$CYAN = "$ESC[36m"
$WHITE = "$ESC[37m"
$BOLD_WHITE = "$ESC[1;37m"  # Bold white for menu text
$BOLD_RED = "$ESC[1;31m"    # Bold red for selected items
$BOLD_GREEN = "$ESC[1;32m"  # Bold green for success messages
$BOLD_YELLOW = "$ESC[1;33m" # Bold yellow for warnings
$BOLD_BLUE = "$ESC[1;34m"   # Bold blue for info
$DARK_RED = "$ESC[31m"      # Dark red for headers and borders

# Get the script directory with fallback mechanisms
$scriptDir = $null

# First try environment variable (set by VBScript launcher)
if ($env:SCRIPT_DIR -and (Test-Path $env:SCRIPT_DIR)) {
    $scriptDir = $env:SCRIPT_DIR
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Using directory from environment variable: ${CYAN}$scriptDir${RESET}"
}
# Next try PowerShell's own mechanisms
elseif ($PSScriptRoot) {
    $scriptDir = $PSScriptRoot
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Using PowerShell script root: ${CYAN}$scriptDir${RESET}"
}
# Finally fallback to executable path
else {
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Using invocation path: ${CYAN}$scriptDir${RESET}"
}

# Set working directory to script location
Set-Location $scriptDir
Write-Host "${BOLD_BLUE}[INFO]${RESET} Working directory set to: ${CYAN}$(Get-Location)${RESET}"

Write-Host "${BOLD_GREEN}[INFO]${RESET} Launcher script started!"

# Read the command from the LAUNCHER_COMMAND environment variable
$commandToExecute = Get-ChildItem Env:LAUNCHER_COMMAND -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Value

if ($commandToExecute) {
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Executing command from environment variable: '${CYAN}$commandToExecute${RESET}'"
    Invoke-Expression $commandToExecute
}
else {
    Write-Host "${BOLD_BLUE}[INFO]${RESET} LAUNCHER_COMMAND environment variable not set."
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Running in normal mode."
}

# DoorDash Tip Remover PowerShell Launcher
$Host.UI.RawUI.WindowTitle = "DoorDash Runner - PowerShell Launcher"
$ErrorActionPreference = "Stop"

function Show-Title {
    $title = "TIP REMOVAL SYSTEM"
    $borderChar = "═"
    $borderWidth = 70
    $border = $borderChar * $borderWidth

    Clear-Host
    Write-Host ""
    Write-Host "${BOLD_RED}╭${border}╮${RESET}"
    Write-Host "${BOLD_RED}│${RESET}${BOLD_WHITE}" -NoNewline
    Write-Host (" " * [math]::Floor(($borderWidth - $title.Length) / 2) + $title + " " * [math]::Ceiling(($borderWidth - $title.Length) / 2)) -NoNewline
    Write-Host "${RESET}${BOLD_RED}│${RESET}"
    Write-Host "${BOLD_RED}╰${border}╯${RESET}"
    Write-Host ""
}

function ShowHeader {
    param (
        [string]$title,
        [string]$color = "DarkRed"
    )

    $width = $Host.UI.RawUI.WindowSize.Width
    if ($width -gt 80) { $width = 80 }

    $padding = [Math]::Floor(($width - $title.Length) / 2)
    $header = "=" * $width

    # Map colors to ANSI codes
    $colorCode = switch ($color) {
        "DarkRed" { $DARK_RED }
        "Red" { $BOLD_RED }
        "Green" { $BOLD_GREEN }
        "Yellow" { $BOLD_YELLOW }
        "Cyan" { $BOLD_BLUE }
        default { $BOLD_WHITE }
    }

    Write-Host ""
    Write-Host "${colorCode}$header${RESET}"
    Write-Host "${colorCode}$(" " * $padding)$title${RESET}"
    Write-Host "${colorCode}$header${RESET}"
    Write-Host ""
}

function CheckForConfigFiles {
    $standardConfig = Join-Path $scriptDir "config.json"
    $bannedConfig = Join-Path $scriptDir "config_banned.json"
    $hasStandard = Test-Path $standardConfig
    $hasBanned = Test-Path $bannedConfig

    return @{
        "Standard" = $hasStandard
        "Banned"   = $hasBanned
    }
}

function ShowMenu {
    Clear-Host

    # Check if run scripts exist
    $hasRunStandard = Test-Path (Join-Path $scriptDir "run.py")
    $hasRemovalStandard = Test-Path (Join-Path $scriptDir "removal3.1.py")
    $hasRunBanned = Test-Path (Join-Path $scriptDir "removalBanned3.0.py")

    # Check if menu systems exist
    $hasMenuStandard = Test-Path (Join-Path $scriptDir "menu_system.py")
    $hasMenuBanned = Test-Path (Join-Path $scriptDir "menu_system_banned.py")

    # Check if config files exist
    $configStatus = CheckForConfigFiles

    # --- MODIFIED ASCII ART for RUNS!!! ---
    Write-Host @"
    ${BOLD_RED}██████╗ ██╗   ██╗███╗   ██╗███████╗ ██╗ ██╗ ██╗
    ██╔══██╗██║   ██║████╗  ██║██╔════╝ ██║ ██║ ██║
    ██████╔╝██║   ██║██╔██╗ ██║███████╗ ██║ ██║ ██║
    ██╔══██╗██║   ██║██║╚██╗██║╚════██║ ╚═╝ ╚═╝ ╚═╝
    ██║  ██║╚██████╔╝██║ ╚████║███████║ ██╗ ██╗ ██╗
    ╚═╝  ╚═╝ ╚═════╝ ╚═╝  ╚═══╝╚══════╝ ╚═╝ ╚═╝ ╚═╝${RESET}
"@

    # Separator line
    $width = $Host.UI.RawUI.WindowSize.Width
    if ($width -gt 80) { $width = 80 }
    Write-Host "${BOLD_RED}$([string]::new('=', $width))${RESET}"
    Write-Host " "

    Write-Host "${BOLD_YELLOW}Choose an option:${RESET}"
    Write-Host ""

    # Standard script status
    $standardStatus = if ($hasRunStandard -and $hasRemovalStandard) { "[Ready]" } elseif (-not $hasRunStandard) { "[Missing run.py]" } elseif (-not $hasRemovalStandard) { "[Missing removal3.1.py]" } else { "[Error]" }
    $standardColor = if ($standardStatus -eq "[Ready]") { $BOLD_GREEN } else { $BOLD_RED }
    $standardConfig = if ($configStatus.Standard) { "[Config +]" } else { "" }

    # Banned script status
    $bannedStatus = if ($hasRunBanned) { "[Ready]" } else { "[Missing removalBanned3.0.py]" }
    $bannedColor = if ($bannedStatus -eq "[Ready]") { $BOLD_GREEN } else { $BOLD_RED }
    $bannedConfig = if ($configStatus.Banned) { "[Config +]" } else { "" }

    Write-Host " ${BOLD_WHITE}[1] Standard Removal${RESET}" -NoNewline
    Write-Host " $standardColor$standardStatus${RESET}" -NoNewline
    Write-Host " ${CYAN}$standardConfig${RESET}"

    Write-Host " ${BOLD_WHITE}[2] Deactivated Removal${RESET}" -NoNewline
    Write-Host " $bannedColor$bannedStatus${RESET}" -NoNewline
    Write-Host " ${CYAN}$bannedConfig${RESET}"

    Write-Host ""
    Write-Host " ${WHITE}[3] Check for Cookies${RESET}"
    Write-Host " ${WHITE}[4] Backup/Restore Cookies${RESET}"
    Write-Host ""
    Write-Host " ${BOLD_WHITE}[5] System Check and Setup Dependencies${RESET}"
    Write-Host " ${BOLD_WHITE}[6] Virtual Environment Status${RESET}"
    Write-Host " ${BOLD_WHITE}[7] Clean Logs Folder${RESET}"
    Write-Host " ${BOLD_WHITE}[8] Create Run Scripts${RESET}"
    Write-Host ""
    Write-Host " ${BOLD_WHITE}[9] Exit${RESET}"
    Write-Host ""
    Write-Host "${BOLD_RED}$([string]::new('=', 80))${RESET}"
    Write-Host ""
}

function RunScript {
    param($scriptName, $scriptType)

    $fullPath = Join-Path $scriptDir $scriptName

    if (-not (Test-Path $fullPath)) {
        Clear-Host
        ShowHeader "ERROR" "Red"
        Write-Host "${BOLD_RED}Script not found: $scriptName${RESET}"
        Write-Host ""
        Write-Host "${BOLD_YELLOW}Please make sure the script file exists in:${RESET}"
        Write-Host "${YELLOW}$scriptDir${RESET}"
        Write-Host ""
        Read-Host "Press Enter to return to the menu"
        return
    }

    Clear-Host
    ShowHeader "$scriptType MODE SELECTED" "Green"

    Write-Host "${BOLD_BLUE}[INFO]${RESET} Running: ${CYAN}$scriptName${RESET}"
    Write-Host "${BOLD_BLUE}[INFO]${RESET} Mode: ${CYAN}$scriptType${RESET}"
    Write-Host ""
    Write-Host "${BOLD_YELLOW}Starting script in 3 seconds...${RESET}"
    Start-Sleep -Seconds 3

    $venvDir = Join-Path $scriptDir ".venv"
    if (Test-Path "$venvDir\Scripts\Activate.ps1") {
        Write-Host "${BOLD_BLUE}[INFO]${RESET} Using virtual environment"
        & "$venvDir\Scripts\Activate.ps1"
        python "$fullPath" --skip-dependency-check
    }
    else {
        Write-Host "${BOLD_YELLOW}[INFO]${RESET} No virtual environment found. Running with system Python."
        python "$fullPath" --skip-dependency-check
    }

    if ($LASTEXITCODE -ne 0) {
        Write-Host ""
        Write-Host "${BOLD_RED}[ERROR]${RESET} Script exited with error code $LASTEXITCODE"
        Write-Host "${BOLD_RED}Please check the error messages above${RESET}"
    }
    else {
        Write-Host ""
        Write-Host "${BOLD_GREEN}[SUCCESS]${RESET} Script completed successfully"
    }

    Write-Host ""
    Write-Host "${WHITE}Press any key to return to the menu...${RESET}"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

function CheckCookieStatus {
    Clear-Host
    ShowHeader "COOKIE STATUS CHECK" "Cyan"

    $standardCookiePath = Join-Path $scriptDir "cookies.pkl"
    $bannedCookiePath = Join-Path $scriptDir "cookies_deactivated.pkl"
    $hasCookies = Test-Path $standardCookiePath
    $hasDeactivatedCookies = Test-Path $bannedCookiePath

    $cookiesInfo = @()

    # Check standard cookies
    if ($hasCookies) {
        $cookieFile = Get-Item $standardCookiePath
        $size = "{0:N0} KB" -f ($cookieFile.Length / 1KB)
        $lastModified = $cookieFile.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
        $cookiesInfo += [PSCustomObject]@{
            Type         = "Standard Account"
            Status       = "+ Found"
            Size         = $size
            LastModified = $lastModified
            Path         = $standardCookiePath
        }
    }
    else {
        $cookiesInfo += [PSCustomObject]@{
            Type         = "Standard Account"
            Status       = "- Not Found"
            Size         = "N/A"
            LastModified = "N/A"
            Path         = $standardCookiePath
        }
    }

    # Check deactivated cookies
    if ($hasDeactivatedCookies) {
        $cookieFile = Get-Item $bannedCookiePath
        $size = "{0:N0} KB" -f ($cookieFile.Length / 1KB)
        $lastModified = $cookieFile.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
        $cookiesInfo += [PSCustomObject]@{
            Type         = "Deactivated Account"
            Status       = "+ Found"
            Size         = $size
            LastModified = $lastModified
            Path         = $bannedCookiePath
        }
    }
    else {
        $cookiesInfo += [PSCustomObject]@{
            Type         = "Deactivated Account"
            Status       = "- Not Found"
            Size         = "N/A"
            LastModified = "N/A"
            Path         = $bannedCookiePath
        }
    }

    # Check backup folder
    $backupDir = Join-Path $scriptDir "cookiesBAK"
    if (Test-Path $backupDir) {
        $backupFiles = Get-ChildItem -Path $backupDir -Filter "*.pkl" | Measure-Object
        $backupStatus = if ($backupFiles.Count -gt 0) {
            "+ Found ($($backupFiles.Count) backups)"
        }
        else {
            "+ Empty folder (no backups)"
        }
    }
    else {
        $backupStatus = "- Backup folder not found"
    }

    # Display cookie information in a table
    $cookiesInfo | Format-Table -Property Type, Status, Size, LastModified -AutoSize

    Write-Host "Backup Status: " -NoNewline
    if ($backupStatus.StartsWith("+")) {
        Write-Host $backupStatus -ForegroundColor Green
    }
    else {
        Write-Host $backupStatus -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "${BOLD_YELLOW}Note: Cookie files are required for automatic login.${RESET}"
    Write-Host "${BOLD_YELLOW}If cookies are missing, you'll need to log in manually when running the scripts.${RESET}"
    Write-Host ""
    Write-Host "${WHITE}Press Enter to return to main menu...${RESET}"
    Read-Host | Out-Null
}

function BackupRestoreCookies {
    Clear-Host
    ShowHeader "COOKIE BACKUP AND RESTORE" "Cyan"

    $standardCookiePath = Join-Path $scriptDir "cookies.pkl"
    $bannedCookiePath = Join-Path $scriptDir "cookies_deactivated.pkl"
    $backupDir = Join-Path $scriptDir "cookiesBAK"

    # Create backup directory if it doesn't exist
    if (-not (Test-Path $backupDir)) {
        New-Item -Path $backupDir -ItemType Directory | Out-Null
        Write-Host "[INFO] Created backup directory: $backupDir" -ForegroundColor Cyan
    }

    Write-Host "Cookie Management Options:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host " [1] Backup Standard Cookies"
    Write-Host " [2] Backup Deactivated Cookies"
    Write-Host " [3] Restore Standard Cookies from Backup"
    Write-Host " [4] Restore Deactivated Cookies from Backup"
    Write-Host " [5] Return to Main Menu"
    Write-Host ""

    $choice = Read-Host "Enter your choice (1-5)"

    switch ($choice) {
        "1" {
            if (Test-Path $standardCookiePath) {
                $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                $backupFile = Join-Path $backupDir "cookies_standard_$timestamp.pkl"
                Copy-Item -Path $standardCookiePath -Destination $backupFile
                Write-Host "[SUCCESS] Standard cookies backed up to: $backupFile" -ForegroundColor Green
            }
            else {
                Write-Host "[ERROR] Standard cookies file not found: $standardCookiePath" -ForegroundColor Red
            }
            Start-Sleep -Seconds 2
            BackupRestoreCookies
        }
        "2" {
            if (Test-Path $bannedCookiePath) {
                $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                $backupFile = Join-Path $backupDir "cookies_deactivated_$timestamp.pkl"
                Copy-Item -Path $bannedCookiePath -Destination $backupFile
                Write-Host "[SUCCESS] Deactivated cookies backed up to: $backupFile" -ForegroundColor Green
            }
            else {
                Write-Host "[ERROR] Deactivated cookies file not found: $bannedCookiePath" -ForegroundColor Red
            }
            Start-Sleep -Seconds 2
            BackupRestoreCookies
        }
        "3" {
            RestoreCookies "standard" $standardCookiePath
            BackupRestoreCookies
        }
        "4" {
            RestoreCookies "deactivated" $bannedCookiePath
            BackupRestoreCookies
        }
        "5" {
            return
        }
        default {
            Write-Host "[ERROR] Invalid choice. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            BackupRestoreCookies
        }
    }
}

function RestoreCookies {
    param (
        [string]$cookieType,
        [string]$destinationPath
    )

    Clear-Host
    ShowHeader "RESTORE $($cookieType.ToUpper()) COOKIES" "Cyan"

    $backupDir = Join-Path $scriptDir "cookiesBAK"
    if (-not (Test-Path $backupDir)) {
        Write-Host "[ERROR] Backup directory not found: $backupDir" -ForegroundColor Red
        Start-Sleep -Seconds 2
        return
    }

    $pattern = if ($cookieType -eq "standard") { "*standard*" } else { "*deactivated*" }
    $backups = Get-ChildItem -Path $backupDir -Filter $pattern | Sort-Object LastWriteTime -Descending

    if ($backups.Count -eq 0) {
        Write-Host "[INFO] No backups found for $cookieType cookies" -ForegroundColor Yellow
        Write-Host "Press Enter to continue..." -ForegroundColor Gray
        Read-Host | Out-Null
        return
    }

    Write-Host "Available Backups for $cookieType cookies:" -ForegroundColor Yellow
    Write-Host ""

    for ($i = 0; $i -lt $backups.Count; $i++) {
        $backup = $backups[$i]
        $size = "{0:N0} KB" -f ($backup.Length / 1KB)
        Write-Host " [$($i+1)] $($backup.Name) - $size - $($backup.LastWriteTime)" -ForegroundColor Cyan
        if ($i -eq 9) {
            Write-Host "(Showing only the 10 most recent backups)" -ForegroundColor Gray
            break
        }
    }

    Write-Host ""
    Write-Host " [C] Cancel" -ForegroundColor DarkYellow
    Write-Host ""

    $choice = Read-Host "Enter your choice"

    if ($choice -eq "C" -or $choice -eq "c") {
        return
    }

    if ([int]::TryParse($choice, [ref]$null)) {
        $index = [int]$choice - 1
        if ($index -ge 0 -and $index -lt $backups.Count) {
            # Create a backup of the current cookies file if it exists
            if (Test-Path $destinationPath) {
                $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                $preRestoreBackup = Join-Path $backupDir "pre_restore_$($cookieType)_$timestamp.pkl"
                Copy-Item -Path $destinationPath -Destination $preRestoreBackup
                Write-Host "[INFO] Created backup of current cookies before restore: $preRestoreBackup" -ForegroundColor Cyan
            }

            # Copy the selected backup to the destination
            Copy-Item -Path $backups[$index].FullName -Destination $destinationPath
            Write-Host "[SUCCESS] Restored $cookieType cookies from: $($backups[$index].Name)" -ForegroundColor Green
            Write-Host "Press Enter to continue..." -ForegroundColor Gray
            Read-Host | Out-Null
        }
        else {
            Write-Host "[ERROR] Invalid selection" -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
    else {
        Write-Host "[ERROR] Invalid input" -ForegroundColor Red
        Start-Sleep -Seconds 2
    }
}

function SystemCheck {
    Clear-Host
    ShowHeader "SYSTEM CHECK AND DEPENDENCY SETUP" "Green"

    # Check if Python is installed
    $pythonCmd = Get-Command python -ErrorAction SilentlyContinue
    if (-not $pythonCmd) {
        Write-Host "[ERROR] Python is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Python 3.8+ from https://www.python.org/downloads/"
        Write-Host "Make sure to check 'Add Python to PATH' during installation"
        Read-Host "Press Enter to continue..."
        return
    }

    # Check Python version
    Write-Host "[INFO] Checking Python version..." -ForegroundColor Cyan
    $pythonVersion = python -c "import sys; print('.'.join(map(str, sys.version_info[:3])))"
    Write-Host "[INFO] Detected Python $pythonVersion" -ForegroundColor Cyan

    # Check that Python is 3.8 or higher
    $pythonVersionCheck = python -c "import sys; print(1 if sys.version_info >= (3, 8) else 0)"
    if ($pythonVersionCheck -eq "0") {
        Write-Host "[ERROR] Python 3.8+ is required" -ForegroundColor Red
        Write-Host "Please install a newer version of Python"
        Read-Host "Press Enter to continue..."
        return
    }

    Write-Host "[INFO] Python version check passed!" -ForegroundColor Green
    Write-Host ""

    # Verify requirements.txt exists
    $requirementsFile = Join-Path $scriptDir "requirements.txt"
    if (-not (Test-Path $requirementsFile)) {
        Write-Host "[ERROR] requirements.txt file not found in $scriptDir" -ForegroundColor Red
        Write-Host "Please ensure the requirements.txt file exists in the script directory."
        Read-Host "Press Enter to continue..."
        return
    }

    Write-Host "Package Installation Options:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host " [1] Create virtual environment (Recommended)"
    Write-Host " [2] Install packages globally"
    Write-Host " [3] Upgrade pip only"
    Write-Host " [4] Skip package installation"
    Write-Host " [5] Return to main menu"
    Write-Host ""
    $choice = Read-Host "Enter your choice (1-5)"

    switch ($choice) {
        "1" {
            SetupVirtualEnvironment
        }
        "2" {
            InstallGlobally
        }
        "3" {
            UpgradePip
        }
        "4" {
            Write-Host ""
            Write-Host "[INFO] Skipping package installation." -ForegroundColor Yellow
            Write-Host "[INFO] Python environment check successful." -ForegroundColor Green
            Read-Host "Press Enter to return to the main menu..."
            return
        }
        "5" {
            return
        }
        default {
            Write-Host "[ERROR] Invalid choice. Returning to main menu." -ForegroundColor Red
            Start-Sleep -Seconds 2
            return
        }
    }
}

function UpgradePip {
    Write-Host ""
    Write-Host "[INFO] Upgrading pip to latest version..." -ForegroundColor Cyan

    try {
        $process = Start-Process -FilePath "python" -ArgumentList "-m", "pip", "install", "--upgrade", "pip" -NoNewWindow -Wait -PassThru
        if ($process.ExitCode -ne 0) {
            throw "Pip upgrade failed with exit code $($process.ExitCode)"
        }

        # Verify pip version
        $pipVersion = python -m pip --version
        Write-Host "[SUCCESS] Pip upgraded successfully!" -ForegroundColor Green
        Write-Host "[INFO] $pipVersion" -ForegroundColor Cyan
        Write-Host ""
        Read-Host "Press Enter to return to the main menu..."
    }
    catch {
        Write-Host "[ERROR] Failed to upgrade pip: $_" -ForegroundColor Red
        Read-Host "Press Enter to return to the main menu..."
    }
}

function SetupVirtualEnvironment {
    # Setup virtual environment
    Write-Host ""
    Write-Host "[INFO] Preparing virtual environment setup..." -ForegroundColor Cyan

    $venvDir = Join-Path $scriptDir ".venv"
    Write-Host "[INFO] Target location: $venvDir" -ForegroundColor Cyan

    # Check if venv directory exists and handle cleanup
    if (Test-Path $venvDir) {
        Write-Host "[INFO] Virtual environment already exists." -ForegroundColor Cyan
        $recreate = Read-Host "Would you like to recreate it? (y/n)"

        if ($recreate -eq "y") {
            # Try to kill any processes that might be using the venv
            try {
                if (Test-Path Function:deactivate) {
                    deactivate
                }

                Get-Process python -ErrorAction SilentlyContinue | Where-Object { $_.Path -like "$venvDir*" } | Stop-Process -Force
                Start-Sleep -Seconds 2

                Remove-Item -Path $venvDir -Recurse -Force -ErrorAction Stop
                Write-Host "[INFO] Cleaned up existing virtual environment." -ForegroundColor Cyan
            }
            catch {
                Write-Host "[ERROR] Could not remove existing virtual environment: $_" -ForegroundColor Red
                Read-Host "Press Enter to return to main menu..."
                return
            }
        }
    }

    # Create new venv if needed
    if (-not (Test-Path $venvDir)) {
        Write-Host "[INFO] Creating virtual environment..." -ForegroundColor Cyan
        try {
            python -m venv $venvDir

            if (-not (Test-Path "$venvDir\Scripts\python.exe")) {
                throw "Virtual environment creation failed"
            }
        }
        catch {
            Write-Host "[ERROR] Failed to create virtual environment: $_" -ForegroundColor Red
            Read-Host "Press Enter to return to main menu..."
            return
        }
    }

    Write-Host "[INFO] Installing and configuring packages..." -ForegroundColor Cyan

    try {
        # Activate the environment first
        & "$venvDir\Scripts\Activate.ps1"

        # Verify activation
        $pythonPath = (Get-Command python).Source
        if (-not $pythonPath.Contains($venvDir)) {
            throw "Virtual environment activation failed"
        }

        # Upgrade pip first
        Write-Host "[INFO] Upgrading pip to latest version..." -ForegroundColor Cyan
        $process = Start-Process -FilePath "$venvDir\Scripts\python.exe" -ArgumentList "-m", "pip", "install", "--upgrade", "pip" -NoNewWindow -Wait -PassThru
        if ($process.ExitCode -ne 0) {
            throw "Pip upgrade failed"
        }

        # Install packages
        Write-Host "[INFO] Installing required packages..." -ForegroundColor Cyan
        $process = Start-Process -FilePath "$venvDir\Scripts\pip.exe" -ArgumentList "install", "-r", $requirementsFile -NoNewWindow -Wait -PassThru
        if ($process.ExitCode -ne 0) {
            throw "Package installation failed"
        }

        # Verify installation
        $verificationSuccess = $true
        $requiredPackages = @(
            "selenium",
            "undetected-chromedriver",
            "webdriver-manager",
            "rich",
            "colorama",
            "loguru"
        )

        Write-Host "[INFO] Verifying package installation..." -ForegroundColor Cyan
        foreach ($package in $requiredPackages) {
            $checkPackage = python -c "import importlib.util; print(1 if importlib.util.find_spec('$($package.Replace('-', '_'))') else 0)"
            if ($checkPackage -eq "0") {
                $verificationSuccess = $false
                Write-Host "[ERROR] Package verification failed: $package" -ForegroundColor Red
            }
        }

        if (-not $verificationSuccess) {
            throw "One or more packages failed verification"
        }

        Write-Host "[SUCCESS] Virtual environment setup complete!" -ForegroundColor Green
        Write-Host "[SUCCESS] All dependencies installed and verified!" -ForegroundColor Green

        # Now it's safe to show activation success
        Write-Host "[SUCCESS] Virtual environment is active and ready!" -ForegroundColor Green
        Write-Host "Location: $venvDir" -ForegroundColor Cyan
        Write-Host "Python: $pythonPath" -ForegroundColor Cyan
    }
    catch {
        Write-Host "[ERROR] Setup failed: $_" -ForegroundColor Red
        Write-Host "You may want to try running the setup again or check for system issues." -ForegroundColor Yellow
        Read-Host "Press Enter to return to main menu..."
        return
    }

    SetupComplete
}

function InstallGlobally {
    Write-Host ""
    Write-Host "[INFO] Installing packages globally (not recommended)..." -ForegroundColor Yellow

    $requirementsFile = Join-Path $scriptDir "requirements.txt"

    if (-not (Test-Path $requirementsFile)) {
        Write-Host "[ERROR] requirements.txt not found!" -ForegroundColor Red
        Read-Host "Press Enter to continue..."
        return
    }

    try {
        Write-Host "[INFO] Installing packages from requirements.txt..." -ForegroundColor Cyan

        # Install packages with proper error handling
        $process = Start-Process -FilePath "python" -ArgumentList "-m", "pip", "install", "-r", $requirementsFile -NoNewWindow -Wait -PassThru

        if ($process.ExitCode -ne 0) {
            throw "Package installation failed with exit code $($process.ExitCode)"
        }

        Write-Host "[SUCCESS] All dependencies installed globally!" -ForegroundColor Green
        SetupComplete
    }
    catch {
        Write-Host "[ERROR] Failed to install packages globally: $_" -ForegroundColor Red
        Read-Host "Press Enter to continue..."
        return
    }
}

function SetupComplete {
    Write-Host ""
    Write-Host "======================================================" -ForegroundColor Green
    Write-Host "       [SUCCESS] System check and setup complete!      " -ForegroundColor Green
    Write-Host "======================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your system is now ready to run the DoorDash scripts."
    Write-Host ""
    Read-Host "Press Enter to return to the main menu..."
}

function VerifyVenvStatus {
    Clear-Host
    ShowHeader "VIRTUAL ENVIRONMENT STATUS CHECK" "Cyan"

    $venvDir = Join-Path $scriptDir ".venv"
    $success = $true
    $issues = @()

    # Check if venv exists
    if (-not (Test-Path $venvDir)) {
        Write-Host "[ERROR] Virtual environment not found at: $venvDir" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please run System Check and Setup (Option 5) to create the virtual environment." -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to return to the main menu"
        return
    }

    Write-Host "[INFO] Checking virtual environment..." -ForegroundColor Cyan

    # Check python executable
    if (-not (Test-Path "$venvDir\Scripts\python.exe")) {
        $success = $false
        $issues += "Python executable missing"
    }

    # Check pip executable
    if (-not (Test-Path "$venvDir\Scripts\pip.exe")) {
        $success = $false
        $issues += "Pip executable missing"
    }

    # Check activation script
    if (-not (Test-Path "$venvDir\Scripts\Activate.ps1")) {
        $success = $false
        $issues += "Activation script missing"
    }

    # Try to activate and verify Python path
    try {
        & "$venvDir\Scripts\Activate.ps1"
        $pythonPath = (Get-Command python).Source
        if (-not $pythonPath.Contains($venvDir)) {
            $success = $false
            $issues += "Virtual environment activation failed"
        }

        # Verify key packages
        $requiredPackages = @(
            "selenium",
            "undetected-chromedriver",
            "webdriver-manager",
            "rich",
            "colorama",
            "loguru"
        )

        foreach ($package in $requiredPackages) {
            $checkPackage = python -c "import importlib.util; print(1 if importlib.util.find_spec('$($package.Replace('-', '_'))') else 0)"
            if ($checkPackage -eq "0") {
                $success = $false
                $issues += "Package missing: $package"
            }
        }

    }
    catch {
        $success = $false
        $issues += "Error activating virtual environment: $_"
    }

    if ($success) {
        Write-Host ""
        Write-Host "[SUCCESS] Virtual environment is healthy and ready!" -ForegroundColor Green
        Write-Host "Location: $venvDir" -ForegroundColor Cyan
        Write-Host "Python: $pythonPath" -ForegroundColor Cyan
    }
    else {
        Write-Host ""
        Write-Host "[ERROR] Virtual environment has issues:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "- $issue" -ForegroundColor Red
        }
        Write-Host ""
        Write-Host "Suggestion: Run the System Check and Setup (Option 5) to fix these issues." -ForegroundColor Yellow
    }

    Write-Host ""
    Read-Host "Press Enter to return to the main menu"
}

function CleanLogsFolder {
    Clear-Host
    ShowHeader "CLEAN LOGS FOLDER" "Cyan"

    $logsDir = Join-Path $scriptDir "logs"

    if (-not (Test-Path $logsDir)) {
        Write-Host "[INFO] Logs folder does not exist: $logsDir" -ForegroundColor Yellow
        Write-Host "Creating logs directory..." -ForegroundColor Cyan
        New-Item -Path $logsDir -ItemType Directory | Out-Null
        Write-Host "[SUCCESS] Created logs directory" -ForegroundColor Green
        Write-Host ""
        Read-Host "Press Enter to return to the main menu"
        return
    }

    $logFiles = Get-ChildItem -Path $logsDir -Filter "*.log" -Recurse
    $totalCount = $logFiles.Count
    $totalSize = ($logFiles | Measure-Object -Property Length -Sum).Sum / 1MB

    Write-Host "Current Logs Status:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Total Log Files: $totalCount" -ForegroundColor Cyan
    Write-Host "Total Size: $($totalSize.ToString('0.00')) MB" -ForegroundColor Cyan
    Write-Host ""

    if ($totalCount -eq 0) {
        Write-Host "[INFO] No log files found to clean." -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to return to the main menu"
        return
    }

    Write-Host "Cleanup Options:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host " [1] Clean logs older than 7 days"
    Write-Host " [2] Clean logs older than 30 days"
    Write-Host " [3] Clean all logs"
    Write-Host " [4] Return to main menu"
    Write-Host ""

    $choice = Read-Host "Enter your choice (1-4)"

    switch ($choice) {
        "1" {
            $cutoffDate = (Get-Date).AddDays(-7)
            $filesToRemove = $logFiles | Where-Object { $_.LastWriteTime -lt $cutoffDate }
            CleanupFiles $filesToRemove "older than 7 days"
        }
        "2" {
            $cutoffDate = (Get-Date).AddDays(-30)
            $filesToRemove = $logFiles | Where-Object { $_.LastWriteTime -lt $cutoffDate }
            CleanupFiles $filesToRemove "older than 30 days"
        }
        "3" {
            $confirmation = Read-Host "Are you sure you want to clean ALL log files? (y/n)"
            if ($confirmation -eq "y") {
                CleanupFiles $logFiles "all logs"
            }
            else {
                Write-Host "[INFO] Cleanup cancelled." -ForegroundColor Yellow
                Start-Sleep -Seconds 1
                CleanLogsFolder
            }
        }
        "4" {
            return
        }
        default {
            Write-Host "[ERROR] Invalid choice. Please try again." -ForegroundColor Red
            Start-Sleep -Seconds 1
            CleanLogsFolder
        }
    }
}

function CleanupFiles {
    param (
        [System.IO.FileInfo[]]$filesToRemove,
        [string]$description
    )

    $count = $filesToRemove.Count

    if ($count -eq 0) {
        Write-Host "[INFO] No log files $description to clean." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
        CleanLogsFolder
        return
    }

    $size = ($filesToRemove | Measure-Object -Property Length -Sum).Sum / 1MB

    Write-Host ""
    Write-Host "Found $count log files $description ($($size.ToString('0.00')) MB)" -ForegroundColor Cyan
    Write-Host "Cleaning up files..." -ForegroundColor Yellow

    try {
        $filesToRemove | Remove-Item -Force
        Write-Host "[SUCCESS] Removed $count log files" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to remove some files: $_" -ForegroundColor Red
    }

    Write-Host ""
    Read-Host "Press Enter to return to the logs menu"
    CleanLogsFolder
}

function CreateRunScripts {
    Clear-Host
    ShowHeader "CREATE RUN SCRIPTS" "Cyan"

    $runStandardPath = Join-Path $scriptDir "run.py"
    $runBannedPath = Join-Path $scriptDir "run_banned.py"

    $hasRunStandard = Test-Path $runStandardPath
    $hasRunBanned = Test-Path $runBannedPath

    if ($hasRunStandard -and $hasRunBanned) {
        Write-Host "[INFO] Both run scripts already exist." -ForegroundColor Cyan
        Write-Host ""
        Read-Host "Press Enter to return to the main menu"
        return
    }

    # Create run.py if needed
    if (-not $hasRunStandard) {
        try {
            $runStandardContent = @"
#!/usr/bin/env python3
import sys
import os
import subprocess

def main():
    """
    Simple launcher for removal3.1.py that ensures proper environment
    """
    # Get absolute path to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Path to the main script
    main_script = os.path.join(script_dir, "removal3.1.py")

    # Make sure our working directory is the script directory
    os.chdir(script_dir)

    try:
        # Run the script with all arguments passed to this launcher
        result = subprocess.run([sys.executable, main_script] + sys.argv[1:])
        return result.returncode
    except KeyboardInterrupt:
        print("\nProgram terminated by user")
        return 1
    except Exception as e:
        print(f"Error launching program: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
"@
            $runStandardContent | Out-File -FilePath $runStandardPath -Encoding UTF8
            Write-Host "[SUCCESS] Created run.py script for standard removal." -ForegroundColor Green
        }
        catch {
            Write-Host "[ERROR] Failed to create run.py: $_" -ForegroundColor Red
        }
    }

    # Create run_banned.py if needed
    if (-not $hasRunBanned) {
        try {
            $runBannedContent = @"
#!/usr/bin/env python3
import sys
import os
import subprocess

def main():
    """
    Simple launcher for removalBanned3.0.py that ensures proper environment
    """
    # Get absolute path to the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Path to the main script
    main_script = os.path.join(script_dir, "removalBanned3.0.py")

    # Make sure our working directory is the script directory
    os.chdir(script_dir)

    try:
        # Run the script with all arguments passed to this launcher
        result = subprocess.run([sys.executable, main_script] + sys.argv[1:])
        return result.returncode
    except KeyboardInterrupt:
        print("\nProgram terminated by user")
        return 1
    except Exception as e:
        print(f"Error launching program: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
"@
            $runBannedContent | Out-File -FilePath $runBannedPath -Encoding UTF8
            Write-Host "[SUCCESS] Created run_banned.py script for deactivated removal." -ForegroundColor Green
        }
        catch {
            Write-Host "[ERROR] Failed to create run_banned.py: $_" -ForegroundColor Red
        }
    }

    Write-Host ""
    Read-Host "Press Enter to return to the main menu"
}

# Main program loop with ANSI color styling
function Main {
    try {
        while ($true) {
            ShowMenu
            $choice = Read-Host "Enter your choice (1-9)"

            switch ($choice) {
                "1" {
                    RunScript "run.py" "STANDARD"
                }
                "2" {
                    RunScript "removalBanned3.0.py" "DEACTIVATED"
                }
                "3" {
                    CheckCookieStatus
                }
                "4" {
                    BackupRestoreCookies
                }
                "5" {
                    SystemCheck
                }
                "6" {
                    VerifyVenvStatus
                }
                "7" {
                    CleanLogsFolder
                }
                "8" {
                    CreateRunScripts
                }
                "9" {
                    Clear-Host
                    ShowHeader "EXIT" "Yellow"
                    Write-Host "${BOLD_CYAN}Thank you for using DoorDash Tip Removal Launcher!${RESET}"
                    Write-Host ""
                    return
                }
                default {
                    Clear-Host
                    ShowHeader "INVALID CHOICE" "Red"
                    Write-Host "${BOLD_RED}Please select a valid option (1-9)${RESET}"
                    Write-Host ""
                    Start-Sleep -Seconds 1
                }
            }
        }
    }
    catch {
        Clear-Host
        ShowHeader "ERROR" "Red"
        Write-Host "${BOLD_RED}An unexpected error occurred:${RESET}"
        Write-Host "${RED}$_${RESET}"
        Write-Host ""
        Write-Host "${BOLD_YELLOW}Press Enter to exit...${RESET}"
        Read-Host | Out-Null
    }
}

# Start the program
Main
