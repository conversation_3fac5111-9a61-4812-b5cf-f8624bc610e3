html,
body {
  height: 100%;
  min-width: auto;
}
body {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 0;
  margin: 0;
  width: 100%;
  font-size: 1em;
  overflow: hidden;
}
.notransition *, .notransition *::before {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}
#cookie-container {
  overflow-y: auto;
  flex: 1 1 auto;
  min-height: initial;
  max-height: initial;
  min-width: initial;
  max-width: initial;
}

#no-permission > div {
  font-size: 0.95em;
  padding: 10px;
}

@media (prefers-color-scheme: dark) {
  #cookie-container {
    background-color: #202124;
  }
}
