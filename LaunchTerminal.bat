@echo off
REM Set working directory to script location
cd /d "%~dp0"

REM Define the path to WindowsTerminal.exe (using relative path)
SET TERMINAL_PATH=%~dp0terminal\WindowsTerminal.exe

REM Check if the terminal executable exists
IF NOT EXIST "%TERMINAL_PATH%" (
    echo ERROR: Windows Terminal executable not found.
    echo Expected location: %TERMINAL_PATH%
    echo.
    echo Please make sure the 'terminal' folder exists in the same directory as this batch file.
    goto error_exit
)

REM Get the full path to launcher.ps1
SET LAUNCHER_PATH=%~dp0launcher.ps1

REM Check if launcher.ps1 exists
IF NOT EXIST "%LAUNCHER_PATH%" (
    echo ERROR: launcher.ps1 not found.
    echo Expected location: %LAUNCHER_PATH%
    echo.
    goto error_exit
)

REM Remove trailing backslash from the path for consistency
SET SCRIPT_DIR=%~dp0
IF %SCRIPT_DIR:~-1%==\ SET SCRIPT_DIR=%SCRIPT_DIR:~0,-1%

R<PERSON> Create a temporary PowerShell script that will launch our script
echo Set-Location "%SCRIPT_DIR%" > "%TEMP%\temp_launcher.ps1"
echo $env:SCRIPT_DIR = "%SCRIPT_DIR%" >> "%TEMP%\temp_launcher.ps1"
echo Write-Host "Starting launcher.ps1..." >> "%TEMP%\temp_launcher.ps1"
echo ^& "%LAUNCHER_PATH%" >> "%TEMP%\temp_launcher.ps1"
echo pause >> "%TEMP%\temp_launcher.ps1"

REM Launch PowerShell directly to execute our temporary script
start "" powershell.exe -NoProfile -ExecutionPolicy Bypass -NoExit -File "%TEMP%\temp_launcher.ps1"

REM Exit quickly and quietly after launching
timeout /t 2 > nul
exit /b 0

:error_exit
echo.
echo An error occurred while launching the terminal.
echo Press any key to exit...
pause >nul
exit /b 1
