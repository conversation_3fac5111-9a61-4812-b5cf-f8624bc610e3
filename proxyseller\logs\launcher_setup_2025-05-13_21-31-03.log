2025-05-13 21:31:03 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:31:03 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:31:03 [DEBUG] Checking for Python installation...
2025-05-13 21:31:03 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:31:04 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:31:04 [DEBUG] Checking for package: selenium
2025-05-13 21:31:05 [DEBUG] Package 'selenium' found.
2025-05-13 21:31:05 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:31:06 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:31:06 [DEBUG] Checking for package: requests
2025-05-13 21:31:07 [DEBUG] Package 'requests' found.
2025-05-13 21:31:07 [DEBUG] Checking for package: configparser
2025-05-13 21:31:08 [DEBUG] Package 'configparser' found.
2025-05-13 21:31:08 [INFO] All required Python packages seem to be installed.
2025-05-13 21:31:08 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:31:08 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:31:08 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:31:08 [INFO] Launching main Python script...
2025-05-13 21:31:08 [DEBUG] Full command: D:\Users\d0nbx\anaconda3\python.exe "C:\main\proxyseller\main.py"
2025-05-13 21:31:09 [DEBUG] Python Script Standard Output file is empty: C:\main\proxyseller\python_stdout_2025-05-13_21-31-03.log
2025-05-13 21:31:09 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-31-03.log
2025-05-13 21:31:09 [ERROR] Python script exited with code -1073741510.
2025-05-13 21:31:09 [INFO] Launcher script execution finished. Final Exit Code: -1073741510
2025-05-13 21:31:09 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
