2025-05-13 21:15:59 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:15:59 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:15:59 [DEBUG] Checking for Python installation...
2025-05-13 21:15:59 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:15:59 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:15:59 [DEBUG] Checking for package: selenium
2025-05-13 21:16:00 [DEBUG] Package 'selenium' found.
2025-05-13 21:16:00 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:16:01 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:16:01 [DEBUG] Checking for package: requests
2025-05-13 21:16:02 [DEBUG] Package 'requests' found.
2025-05-13 21:16:02 [DEBUG] Checking for package: configparser
2025-05-13 21:16:03 [DEBUG] Package 'configparser' found.
2025-05-13 21:16:03 [INFO] All required Python packages seem to be installed.
2025-05-13 21:16:03 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:16:03 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:16:03 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:16:03 [FATAL] A critical error occurred in the PowerShell launcher: You cannot call a method on a null-valued expression.
2025-05-13 21:16:03 [DEBUG] Stack Trace: at Prompt-For-Launcher-Overrides, C:\main\proxyseller\start_proxy_setup.ps1: line 212
at <ScriptBlock>, C:\main\proxyseller\start_proxy_setup.ps1: line 283
2025-05-13 21:16:03 [INFO] Launcher script execution finished. Final Exit Code: 255
2025-05-13 21:16:03 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
