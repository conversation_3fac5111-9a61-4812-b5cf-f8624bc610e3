#  Template pypi-publish

#  This template allows you to publish your python package, as defined in setup.py, to pypi.org.
#  The workflow allows running tests, code linting and security scans on feature branches (as well as master).
#  The python package will be validated and published after the code is merged to master.

# Prerequisites: $PYPI_USERNAME and $PYPI_PASSWORD setup in the Deployment variables.
# For advanced cases, please, follow examples from the pipe's README https://bitbucket.org/atlassian/pypi-publish/src/master/README.md

image: python:3.8

pipelines:
  branches:
    master:
      - step:
          name: Deploy to Production
          deployment: Production
          script:
            # bump package version
            # - echo "__version__ = '1.0.${BITBUCKET_BUILD_NUMBER}'" > demo_pkg/__version__.py

            # Publish your python package to PyPI https://pypi.org/
            # See more details https://bitbucket.org/atlassian/pypi-publish/src/master
            - pipe: atlassian/pypi-publish:0.7.0
              variables:
                PYPI_USERNAME: $PYPI_USERNAME
                PYPI_PASSWORD: $PYPI_PASSWORD
