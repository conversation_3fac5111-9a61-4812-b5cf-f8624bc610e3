INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:52:26,583 - INFO - [__main__:load_config_and_args:2472] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 22:52:26,585 - INFO - [__main__:load_config_and_args:2474] - Config sections: []
2025-05-13 22:52:26,585 - INFO - [__main__:load_config_and_args:2475] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 22:52:26,587 - INFO - [__main__:load_config_and_args:2609] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 22:52:26,588 - INFO - [__main__:<module>:2653] - Console logging level set to: INFO
2025-05-13 22:52:26,588 - INFO - [__main__:<module>:2654] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:52:26,588 - INFO - [__main__:<module>:2655] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 22:52:26,589 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 22:52:26,589 - INFO - [__main__:__init__:201] - Attempting to determine Firefox profile path...
2025-05-13 22:52:26,590 - INFO - [__main__:_find_firefox_profile:294] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 22:52:26,591 - INFO - [__main__:_find_firefox_profile:309] - Found default profile section in profiles.ini: Profile1
2025-05-13 22:52:26,592 - INFO - [__main__:_find_firefox_profile:357] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:52:26,592 - INFO - [__main__:__init__:230] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:52:26,776 - INFO - [__main__:run:1465] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-13 22:52:26,776 - INFO - [__main__:run:1471] - Starting proxy container setup process...
2025-05-13 22:52:27,329 - INFO - [__main__:init_api_client:431] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 22:52:27,527 - WARNING - [__main__:init_api_client:443] - Could not parse balance from response: 11
2025-05-13 22:52:27,527 - INFO - [__main__:fetch_proxies:467] - Fetching proxies from Proxy-Seller...
2025-05-13 22:52:27,528 - INFO - [__main__:fetch_proxies:481] - Calling proxyList(type='ipv4') API method...
2025-05-13 22:52:27,729 - INFO - [__main__:fetch_proxies:506] - proxyList returned legacy/empty response with 'items': [], treating as no proxies.
2025-05-13 22:52:27,731 - INFO - [__main__:fetch_proxies:557] - Found 0 existing, active, matching USA HTTP proxies via API call and local filtering.
2025-05-13 22:52:27,731 - INFO - [__main__:fetch_proxies:568] - Attempting to create 5 new USA HTTP proxies.
2025-05-13 22:52:27,928 - ERROR - [__main__:create_usa_proxies:793] - No countries returned from referenceList('ipv4'). Check your API key/account permissions.
2025-05-13 22:52:27,928 - WARNING - [__main__:fetch_proxies:635] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-13 22:52:27,929 - WARNING - [__main__:fetch_proxies:733] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-13 22:52:27,929 - ERROR - [__main__:fetch_proxies:742] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-13 22:52:27,929 - ERROR - [__main__:run:1483] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 22:52:27,930 - ERROR - [__main__:<module>:2669] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 22:52:27,930 - ERROR - [__main__:<module>:2749] - ==================================================
2025-05-13 22:52:27,930 - ERROR - [__main__:<module>:2750] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 22:52:27,931 - ERROR - [__main__:<module>:2751] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 22:52:27,932 - ERROR - [__main__:<module>:2754] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 22:52:27,932 - ERROR - [__main__:<module>:2757] - ==================================================
2025-05-13 22:52:27,932 - INFO - [__main__:<module>:2763] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
