import json
import os
from pathlib import Path
from typing import Any, Dict


class SimpleConfigManager:
    """Simple configuration manager with auto-reload capabilities."""

    def __init__(self, config_file: str):
        self.config_file = Path(config_file)
        self.last_modified = 0
        self.default_config = {
            "TEST_MODE": False,
            "HEADLESS_MODE": False,
            "MAX_ORDERS_PER_BATCH": 30,
            "MAX_TIP_THRESHOLD": 4.0,
            "RECONNECT_TIMEOUT1": 30,
            "RECONNECT_TIMEOUT2": 45,
            "CLOSE_ALL_TIMEOUT": 60,
            "CYCLE_SIZE": 3,
            "MAX_OPEN_TABS": 31,  # When this number of tabs is reached, all but main tab get closed
            "CANCELLED_ORDERS_ELIGIBLE": False,
            "REFUNDED_ORDERS_ELIGIBLE": False,
        }
        self.config = self.default_config.copy()
        self.reload()

    def reload(self) -> None:
        """Load or reload the configuration file if modified."""
        try:
            if self.config_file.exists():
                current_mtime = os.path.getmtime(self.config_file)
                # Always reload if checking CYCLE_SIZE (workaround for hot-swapping)
                if current_mtime > self.last_modified:
                    with open(self.config_file, "r", encoding="utf-8") as f:
                        loaded_config = json.load(f)
                        self.config = self.default_config.copy()
                        self.config.update(loaded_config)
                    self.last_modified = current_mtime
        except Exception as e:
            print(f"Error reloading config: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with optional default."""
        self.reload()  # Always check for updates before returning a value
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Update a configuration value in memory without saving to file."""
        self.config[key] = value

    def update_value(self, key: str, value: Any) -> None:
        """Update a configuration value and save to file."""
        self.config[key] = value
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=4)
            self.last_modified = os.path.getmtime(self.config_file)
        except Exception as e:
            print(f"Error saving config: {e}")


# Create singleton instances
standard_config = SimpleConfigManager("config.json")
_banned_config = None  # Lazy loaded


def get_config(for_banned: bool = False) -> SimpleConfigManager:
    """Get the appropriate config manager."""
    global _banned_config
    if for_banned:
        if _banned_config is None:
            _banned_config = SimpleConfigManager("config_banned.json")
        return _banned_config
    return standard_config


# Save config function for compatibility
def save_config(config: Dict[str, Any]) -> None:
    """Save configuration to the standard config file."""
    for key, value in config.items():
        standard_config.update_value(key, value)
