2025-05-13 21:19:34 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:19:34 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:19:34 [DEBUG] Checking for Python installation...
2025-05-13 21:19:34 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-13 21:19:34 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:19:34 [DEBUG] Checking for package: selenium
2025-05-13 21:19:35 [DEBUG] Package 'selenium' found.
2025-05-13 21:19:35 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:19:36 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:19:36 [DEBUG] Checking for package: requests
2025-05-13 21:19:37 [DEBUG] Package 'requests' found.
2025-05-13 21:19:37 [DEBUG] Checking for package: configparser
2025-05-13 21:19:38 [DEBUG] Package 'configparser' found.
2025-05-13 21:19:38 [INFO] All required Python packages seem to be installed.
2025-05-13 21:19:38 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:19:38 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:19:38 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:19:50 [INFO] Launching main Python script...
2025-05-13 21:19:50 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py"
2025-05-13 21:19:51 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-19-33.log
2025-05-13 21:19:51 [ERROR] Python script exited with code 5.
2025-05-13 21:19:51 [INFO] Launcher script execution finished. Final Exit Code: 5
