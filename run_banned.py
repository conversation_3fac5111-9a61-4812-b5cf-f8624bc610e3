#!/usr/bin/env python3
import sys
import os
import subprocess
import logging
from subprocess import CalledProcessError
from pathlib import Path
import platform

def setup_basic_logging():
    """Set up a simple logger for the launcher"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler()]
    )
    return logging.getLogger("banned_launcher")

def main():
    """
    Simple launcher for removalBanned3.0.py that ensures proper environment
    """
    logger = setup_basic_logging()
    
    # Get absolute path to the script directory using Path for better cross-platform compatibility
    script_dir = Path(__file__).parent.absolute()
    logger.info(f"Script directory: {script_dir}")

    # Path to the main script
    main_script = script_dir / "removalBanned3.0.py"
    logger.info(f"Main script path: {main_script}")

    # Make sure our working directory is the script directory
    os.chdir(script_dir)
    logger.info(f"Changed working directory to: {script_dir}")

    try:
        # Add skip dependency check flag to arguments
        args = [sys.executable, str(main_script), "--skip-dependency-check"] + sys.argv[1:]
        
        logger.info(f"Launching script with command: {' '.join(args)}")
        
        # Run the script with all arguments passed to this launcher
        result = subprocess.run(args, check=True)
        logger.info(f"Script completed with return code: {result.returncode}")
        return result.returncode
    except KeyboardInterrupt:
        logger.warning("Program terminated by user")
        return 1
    except CalledProcessError as e:
        logger.error(f"Script execution failed with error code {e.returncode}")
        return e.returncode
    except FileNotFoundError as e:
        logger.error(f"Error: Script file not found - {e}")
        return 1
    except PermissionError as e:
        logger.error(f"Error: Permission denied - {e}")
        return 1
    except OSError as e:
        logger.error(f"OS Error: {e}")
        return 1
    except ImportError as e:
        logger.error(f"Import error: {e}")
        return 1
    except ValueError as e:
        logger.error(f"Value error: {e}")
        return 1
    except (subprocess.SubprocessError, subprocess.TimeoutExpired) as e:
        logger.error(f"Subprocess error: {e}")
        return 1
    # If we get here, something truly unexpected happened that we can't handle specifically
    # We still need to catch it to prevent crashing, but we'll log it clearly for debugging
    except BaseException as e:  # pylint: disable=broad-except
        logger.critical(f"CRITICAL ERROR: Unexpected error type {type(e).__name__}: {e}")
        logger.critical("Please report this with the following information:")
        logger.critical(f"Python version: {sys.version}")
        logger.critical(f"OS: {platform.system()} {platform.release()}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
