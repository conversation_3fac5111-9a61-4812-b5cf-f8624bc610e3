#!/usr/bin/env python3
"""
DoorDash Banned Account Tip <PERSON>mo<PERSON> (v3.1 - Manual Login Fix v6 - Faster Send)

Enhanced script for deactivated/banned accounts with improved reliability,
maintainability, and added features. Includes fixed manual sign-in flow
and cookie sign-in flow, both prompting to reuse previous session data.
Simplified cookie login check. Fixed Rich markup error. Faster message sending.

🔧 CONFIGURATION NOTE:
- To change the chat URL path, update Config.CHAT_URL (around line 220)
- <PERSON><PERSON><PERSON> will ALWAYS stay open for manual inspection
- Improved error handling shows clear messages when paths are wrong

Features:
- Optimized parallel processing for faster operations
- Uses undetected-chromedriver for improved stealth
- Rich console interface for better visibility
- Advanced error handling and recovery
- Auto-reconnect when disconnected from chat support
- Session persistence with cookie saving
- Message logging to CSV for auditing
- Improved stale chat detection
- Infinite loop prevention
- Type hints for better code clarity
- PEP 8 compliance

Author: Geminid System
Version: 3.1 (Manual Login Fix v6)
Date: 2024-05-17

Requires:
- Python 3.8+
- Dependencies in requirements.txt
"""

import argparse
import csv
import importlib.util
import json
import os
import pickle
import platform
import random
import select
import sys
import threading
import time
import traceback
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Set, Union, cast

import undetected_chromedriver as uc
from loguru import logger
from rich.align import Align
from rich.console import Console
from rich.errors import MarkupError  # Import MarkupError
from rich.panel import Panel
from rich.progress import BarColumn, Progress, SpinnerColumn, TextColumn
from rich.style import Style as RichStyle
from rich.table import Table
from rich.theme import Theme
from selenium.common.exceptions import NoSuchElementException  # Added
from selenium.common.exceptions import NoSuchWindowException, TimeoutException, WebDriverException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.remote.webelement import WebElement as SeleniumWebElement
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

# Platform-specific imports and checks
if platform.system() == "Windows":
    import msvcrt

    win32api_spec = importlib.util.find_spec("win32api")
    win32api_available = win32api_spec is not None
else:
    try:
        import termios
        import tty
    except ImportError:
        pass

# Import configuration module
try:
    from config_module import get_config

    def load_config() -> Dict[str, Any]:
        """Load configuration from config module."""
        try:
            # Get banned account config
            banned_config = get_config(for_banned=True)

            # Update app_state with values from configuration
            app_state.test_mode = banned_config.get("TEST_MODE", False)
            app_state.headless_mode = banned_config.get("HEADLESS_MODE", True)
            app_state.max_orders_per_batch = banned_config.get("MAX_ORDERS_PER_BATCH", Config.MAX_BATCH_SIZE)
            Config.CYCLE_SIZE = banned_config.get("CYCLE_SIZE", Config.CYCLE_SIZE)
            app_state.log_level = banned_config.get("LOG_LEVEL", "INFO")

            return {
                "TEST_MODE": app_state.test_mode,
                "HEADLESS_MODE": app_state.headless_mode,
                "MAX_ORDERS_PER_BATCH": app_state.max_orders_per_batch,
                "CYCLE_SIZE": Config.CYCLE_SIZE,
                "LOG_LEVEL": app_state.log_level,
            }
        except Exception as e:
            logger.error(f"Error loading config from module: {e}")
            return {}

    def save_config(config: Dict[str, Any]) -> None:
        """Save configuration to config module."""
        try:
            # Update app_state with new values
            app_state.test_mode = config.get("TEST_MODE", app_state.test_mode)
            app_state.headless_mode = config.get("HEADLESS_MODE", app_state.headless_mode)
            app_state.max_orders_per_batch = config.get("MAX_ORDERS_PER_BATCH", app_state.max_orders_per_batch)
            Config.CYCLE_SIZE = config.get("CYCLE_SIZE", Config.CYCLE_SIZE)
            app_state.log_level = config.get("LOG_LEVEL", app_state.log_level)

            # Update configuration module
            for key, value in config.items():
                banned_config = get_config(for_banned=True)
                banned_config.set(key, value)

            logger.info("Configuration saved via module.")
        except Exception as e:
            logger.error(f"Error saving config via module: {e}")

except ImportError:
    # Fallback to menu_system_banned if available
    try:
        from menu_system_banned import load_config
    except ImportError:

        def load_config() -> Dict[str, Any]:
            """Load configuration from JSON file."""
            try:
                config_path = Path("config_banned.json")
                if config_path.exists():
                    with open(config_path, "r") as f:
                        saved_config = json.load(f)
                        logger.info(f"Loaded configuration from {config_path}")
                        return saved_config
                else:
                    logger.warning(f"Config file {config_path} not found, using defaults")
                    return {}
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                return {}

        def save_config(config: Dict[str, Any]) -> None:
            """Save configuration to JSON file."""
            try:
                config_path = Path("config_banned.json")
                with open(config_path, "w") as f:
                    json.dump(config, f, indent=4)
            except Exception as e:
                logger.error(f"Error saving config: {e}")


def run_menu_system() -> Optional[str]:
    """Run the menu system and return the selected action."""
    # Store current log level before forcing INFO for menu display
    before_menu_log_level = app_state.log_level
    set_log_level("INFO")  # Force INFO during menu display

    try:
        from menu_system_banned import MenuSystem

        menu = MenuSystem()
        return menu.run()
    except Exception as e:
        logger.error(f"Error running menu system: {e}")
        return "start"  # Default to starting the script
    finally:
        # After menu, load latest config settings and apply them
        config = load_config()
        if config and "LOG_LEVEL" in config:
            # Apply config log level, not restore previous
            set_log_level(config["LOG_LEVEL"])
            logger.debug(f"Applied config log level: {config['LOG_LEVEL']} after menu")
        else:
            # If no config available, reapply the original log level
            set_log_level(before_menu_log_level)


# =============================================================================
#  CONFIGURATION & GLOBAL STATE
# =============================================================================


class Config:
    """Configuration settings for the application."""

    # These will be overridden by config values
    CYCLE_SIZE: int = 2  # Default, will be loaded from config
    MAX_BATCH_SIZE: int = 30  # Default, match the MAX_ORDERS_PER_BATCH from config
    ORDERS_PROCESSED: int = 10  # Default, number of orders to process in total
    MAX_OPEN_TABS: int = 31  # When this number of tabs is reached, all but main tab get closed

    HEADER_WIDTH: int = 80
    MESSAGE_RETRIES: int = 3
    INITIAL_DELAY: float = 0.15
    MESSAGE_RETRY_DELAY: float = 0.05
    AGENT_CHECK_COUNTDOWN: int = 1
    ORDER_TIMEOUT: int = 0.5  # Increased slightly from 0.01 for stability
    RETRY_DELAY: int = 1  # Increased retry delay
    SUPPORT_DOWN_TIMER: int = 300
    BATCH_TIMEOUT: int = 5
    RECONNECT_TIMEOUT: int = 10
    CLEANUP_TIMEOUT: int = 300
    STALE_RETRY_DELAY: int = 1
    MAX_RETRIES: int = 3
    KEY_CHECK_INTERVAL: float = 0.05
    CONNECTION_RETRY_DELAY: int = 5
    MIN_CHAT_LIFETIME: int = 60
    PRESERVE_BROWSER: bool = True  # Always keep browser open

    # Chat URL Configuration - Change this if DoorDash updates their chat path
    # NOTE: The current URL appears to be incorrect - it goes to main page, not chat
    CHAT_URL: str = "https://help.doordash.com/consumers/s/consumer-support"

    SEND_AGENT_MESSAGE: bool = False
    STALE_CHAT_TEXT: str = "Still need help with your issue?"
    MESSAGE_PREVIEW_HEADER: str = "MESSAGE PREVIEW"
    INPUT_SYMBOL: str = "➜"
    MAIN_COLOR: str = "#D91400"
    ACCENT_COLOR: str = "#FFD700"

    # Dynamic browser paths
    BROWSER_PATHS: List[Path] = [
        # Brave paths - Windows
        Path("C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"),
        Path("C:/Program Files (x86)/BraveSoftware/Brave-Browser/Application/brave.exe"),
        Path(os.path.expandvars("%LOCALAPPDATA%/BraveSoftware/Brave-Browser/Application/brave.exe")),
        # Chrome paths - Windows
        Path("C:/Program Files/Google/Chrome/Application/chrome.exe"),
        Path("C:/Program Files (x86)/Google/Chrome/Application/chrome.exe"),
        Path(os.path.expandvars("%LOCALAPPDATA%/Google/Chrome/Application/chrome.exe")),
        # macOS paths
        Path("/Applications/Brave Browser.app/Contents/MacOS/Brave Browser"),
        Path("/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
        # Linux paths
        Path("/usr/bin/brave-browser"),
        Path("/usr/bin/brave"),
        Path("/usr/bin/google-chrome"),
        Path("/usr/bin/chromium"),
    ]

    @classmethod
    def get_browser_path(cls) -> str:
        """Return the first available browser path."""
        for path in cls.BROWSER_PATHS:
            if path.exists():
                return str(path)
        return ""  # Return empty string if no browser found

    @classmethod
    def get_data_dir(cls) -> Path:
        """Return the appropriate data directory based on the OS."""
        if platform.system() == "Windows":
            base_dir = Path(os.environ.get("APPDATA", str(Path.home())))
        elif platform.system() == "Darwin":
            base_dir = Path.home() / "Library" / "Application Support"
        else:
            base_dir = Path.home() / ".config"

        data_dir = base_dir / "ddtipremover"
        data_dir.mkdir(exist_ok=True)
        return data_dir

    @classmethod
    def get_logs_dir(cls) -> Path:
        """Return the logs directory path."""
        # Use a simple relative path
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        return logs_dir


# Style configuration
class StyleConfig:
    """Styling configuration for the application."""

    SUCCESS_STYLE: RichStyle = RichStyle(color="spring_green1", bold=True)
    ERROR_STYLE: RichStyle = RichStyle(color="red1", bold=True)
    INFO_STYLE: RichStyle = RichStyle(color="white", bold=True)
    WARNING_STYLE: RichStyle = RichStyle(color="gold1", bold=True)
    HEADER_STYLE: RichStyle = RichStyle(color="#D91400", bold=True, underline=False)
    HIGHLIGHT_STYLE: RichStyle = RichStyle(color="#FFD700", bold=True, italic=True)

    CUSTOM_THEME: Theme = Theme(
        {
            "info": "bold white",
            "warning": "gold1",
            "error": "bold red1",
            "success": "bold spring_green1",
            "processing": "bold #A0A0A0",
            "highlight": "bold #ADD8E6 italic",
            "debug": "dim grey70",
            "header": "bold #8B0000",
            "countdown": "bold gold1",
            "agent": "bold #008080",
            "input": "bold #4682B4",
            "progress": "bold #708090",
            "status": "bold #9370DB",
            "link": "bold #00008B underline",
        }
    )

    SYMBOLS: Dict[str, str] = {
        "success": "✅",
        "error": "❌",
        "arrow": "❯ ",
        "processing": "🔄",
        "countdown": "⏱️",
        "info": "ℹ️",
        "debug": "⚙️",
        "warning": "⛔",
        "pause": "⏸️",
        "play": "▶️",
    }


# Global state
class AppState:
    """Manage application state."""

    def __init__(self):
        self.console: Console = Console(theme=StyleConfig.CUSTOM_THEME)
        self.print_lock: threading.Lock = threading.Lock()
        self.customer_name: Optional[str] = None
        self.customer_email: Optional[str] = None
        self.customer_phone: Optional[str] = None
        self.num_orders: Optional[str] = None
        self.restaurant_name: Optional[str] = None
        self.test_mode: bool = False
        self.headless_mode: bool = True
        self.max_orders_per_batch: int = Config.MAX_BATCH_SIZE
        self.log_level: str = "INFO"  # Track current log level
        self.current_log_file: Optional[str] = None  # Track current log file path
        self.message_preview_shown: bool = False  # Track if message preview has been shown


# Create global app state
app_state = AppState()


# Global state for pausing
class RunState:
    """Track global run state."""

    def __init__(self):
        self.is_paused = False
        self.pause_lock = threading.Lock()

    def toggle_pause(self) -> bool:
        """Toggle pause state and return new state."""
        with self.pause_lock:
            self.is_paused = not self.is_paused
            return self.is_paused

    def is_running(self) -> bool:
        """Check if script is running (not paused)."""
        with self.pause_lock:
            return not self.is_paused

    def wait_if_paused(self) -> None:
        """Wait if script is paused."""
        with self.pause_lock:
            if not self.is_paused:
                # Not paused, no need to wait or show messages
                return

        # If we get here, the script is paused
        StatusPrinter.print_status("Script paused ⏸️ Press 'p' to resume...", "warning")

        while True:
            with self.pause_lock:
                if not self.is_paused:
                    # No longer paused
                    break

            if InputHandler.kbhit():
                key = InputHandler.getch()
                if key in (b"p", "p"):
                    self.toggle_pause()
                    break
                elif key in (b"q", "q"):
                    logger.info("Exit requested by user via 'q' key while paused")
                    StatusPrinter.print_status("Exit requested. Shutting down...", "warning")
                    os._exit(0)  # Force exit
            time.sleep(0.1)

        StatusPrinter.print_status("Script resumed ▶️", "success")


# Configure logger
def setup_logger() -> None:
    """Configure the logger with appropriate handlers and formats."""
    logger.remove()

    # Read config to get log level if available
    log_level = "INFO"  # Default level
    try:
        config = load_config()
        if config and "LOG_LEVEL" in config:
            log_level = config["LOG_LEVEL"].upper()
    except Exception:
        pass  # Use default if config can't be loaded

    # Map our custom level "OFF" to a valid loguru level (WARNING)
    # because loguru doesn't have an "OFF" level
    loguru_level = log_level if log_level != "OFF" else "WARNING"

    logger.add(
        sys.stderr,
        format="<white>{time:YYYY-MM-DD HH:mm}</white> | <level>{level: <8}</level> | <cyan>{function}</cyan>: <white>{message}</white>",
        level=loguru_level,  # Use the mapped level for loguru
        colorize=True,
    )

    # Add file handler always at DEBUG level for complete logs
    add_log_handler()

    # CRITICAL FIX: Update standard logging module level to match loguru logger level
    # This ensures libraries using standard logging respect our log level
    import logging

    logging_level_map = {
        "OFF": logging.WARNING,
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    logging.getLogger().setLevel(logging_level_map.get(log_level, logging.INFO))

    # CRITICAL FIX: Also update the internal loguru filter level for programmatic filtering
    logger.configure(
        handlers=[
            {
                "sink": sys.stderr,
                "level": loguru_level,
                "format": "<white>{time:YYYY-MM-DD HH:mm}</white> | <level>{level: <8}</level> | <cyan>{function}</cyan>: <white>{message}</white>",
            }
        ]
    )

    # Log message at the appropriate level to confirm it's working
    if log_level == "OFF":
        pass
    else:
        log_method = getattr(logger, log_level.lower(), logger.info)
        log_method(f"Log level set to {log_level}")


def add_log_handler() -> None:
    """Add file handler for logging with rotation.

    This function adds a file handler to the logger with rotation
    settings and proper session markers in the log file. It ensures
    that all debug information is captured to files regardless of
    the console logging level.

    Features:
    - Creates logs in the application data directory
    - Uses 'removal3.0bannedlog' as the log file name
    - Rotates logs at 1MB with 5 backup files
    - Adds detailed session markers with system information
    - Captures all DEBUG level logs regardless of console settings
    - Handles file permission errors gracefully
    """
    # Add file handler always at DEBUG level for complete logs
    logs_dir = Config.get_logs_dir()

    try:
        # Ensure logs directory exists and is writable
        if not logs_dir.exists():
            logs_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created logs directory at: {logs_dir}")

        # Use removal3.0bannedlog as requested for log file name
        log_file_name = "removal3.0bannedlog"
        app_log_path = logs_dir / log_file_name

        # Collect system information for detailed logging
        system_info = {
            "Python Version": platform.python_version(),
            "OS": f"{platform.system()} {platform.release()} ({platform.architecture()[0]})",
            "Machine": platform.machine(),
            "Processor": platform.processor() or "Unknown",
            "User": os.environ.get("USERNAME", os.environ.get("USER", "Unknown")),
            "Hostname": platform.node(),
        }

        # Generate detailed session boundary separator
        script_name = Path(sys.argv[0]).name
        cmd_args = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else "None"

        session_marker = (
            f"\n{'=' * 80}\n"
            f"SESSION START: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {script_name}\n"
            f"Command Arguments: {cmd_args}\n"
            f"System Information:\n"
        )

        # Add system info to session marker
        for key, value in system_info.items():
            session_marker += f"  - {key}: {value}\n"

        # Add configuration info
        session_marker += (
            f"Configuration:\n"
            f"  - Headless Mode: {app_state.headless_mode}\n"
            f"  - Test Mode: {app_state.test_mode}\n"
            f"  - Max Orders Per Batch: {app_state.max_orders_per_batch}\n"
            f"  - Cycle Size: {Config.CYCLE_SIZE}\n"
            f"  - Log Level: {app_state.log_level}\n"
            f"{'=' * 80}\n"
        )

        # Write session marker directly to log file with UTF-8 encoding
        # This ensures the marker appears even if logging initialization fails
        try:
            with open(app_log_path, "a", encoding="utf-8") as f:
                f.write(session_marker)
            logger.debug("Successfully wrote session marker to log file")
        except PermissionError:
            # Handle permission errors specifically
            logger.error(f"Permission denied when writing to log file: {app_log_path}")
            logger.warning("Try running with elevated privileges or check file permissions")
            return
        except Exception as e:
            logger.error(f"Failed to write session marker: {e}")

        # Add file handler with enhanced rotation settings (5 files, 1MB each)
        # This ensures we keep more history while still managing disk space
        log_id = logger.add(
            str(app_log_path),
            rotation="1 MB",  # Rotate at 1MB file size
            retention=5,  # Keep 5 rotation files instead of 3
            compression="zip",  # Compress rotated logs to save space
            format="{time:YYYY-MM-DD HH:mm:ss,SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",  # Always log everything to file regardless of console level
            encoding="utf-8",  # Ensure proper encoding for all characters
            backtrace=True,  # Include backtrace for errors
            diagnose=True,  # Include variables in stack trace
        )

        # Store the current log file path in app_state for reference
        app_state.current_log_file = str(app_log_path)

        # Log detailed confirmation that file logging is enabled
        logger.info(f"Log file initialized at: {app_log_path} (Sink ID: {log_id})")
        logger.debug(f"Log rotation settings: 1MB max size, 5 backup files, zip compression")
        logger.debug(f"Full logging path: {app_log_path}")

    except PermissionError as e:
        # Special handling for permission errors
        logger.error(f"Permission error setting up log file: {e}")
        logger.warning("Check that you have write access to the logs directory")
        logger.warning("Continuing with console logging only")
    except OSError as e:
        # Handle OS errors like disk full or invalid file path
        logger.error(f"Operating system error for log file: {e}")
        logger.warning("Continuing with console logging only")
    except Exception as e:
        # Catch-all for other exceptions
        logger.error(f"Failed to initialize file logging: {type(e).__name__}: {e}")
        logger.warning("Continuing with console logging only")


def set_log_level(level: str) -> None:
    """Update the log level for the console handler.

    Args:
        level: Log level to set (OFF, DEBUG, INFO, WARNING, ERROR)
    """
    # Make sure we have a valid level
    valid_levels = ["OFF", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    level = level.upper() if level.upper() in valid_levels else "INFO"

    # Store in app_state for reference throughout the application
    app_state.log_level = level

    # Also update StatusPrinter class level
    StatusPrinter.log_level = level

    # Special handling for verbose output based on log level
    # In OFF mode, only show emoji status messages
    # In DEBUG mode, show all logs
    if level == "OFF":
        StatusPrinter.verbose = False
        # Use WARNING as the actual logger level to suppress most messages
        # but allow emoji status messages that are explicitly printed
        logger_level = "WARNING"
    else:
        StatusPrinter.verbose = level == "DEBUG"
        logger_level = level

    # First, remove all handlers
    logger.remove()

    # Add console handler with the specified level
    logger.add(
        sys.stderr,
        format="<white>{time:YYYY-MM-DD HH:mm}</white> | <level>{level: <8}</level> | <cyan>{function}</cyan>: <white>{message}</white>",
        level=logger_level,
        colorize=True,
    )

    # Add file handler always at DEBUG level for complete logs
    add_log_handler()

    # CRITICAL FIX: Update standard logging module level to match loguru logger level
    # This ensures libraries using standard logging respect our log level
    import logging

    logging_level_map = {
        "OFF": logging.WARNING,
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    logging.getLogger().setLevel(logging_level_map.get(level, logging.INFO))

    # Update internal loguru configuration with the correct mapped level
    logger.configure(
        handlers=[
            {
                "sink": sys.stderr,
                "level": logger_level,
                "format": "<white>{time:YYYY-MM-DD HH:mm}</white> | <level>{level: <8}</level> | <cyan>{function}</cyan>: <white>{message}</white>",
            }
        ]
    )

    # Log message at the appropriate level to confirm it's working
    if level == "OFF":
        pass
    else:
        log_method = getattr(logger, level.lower(), logger.info)
        log_method(f"Log level set to {level}")


# Session utilities
class SessionUtils:
    """Utilities for session data handling."""

    SESSION_FILE = "session.pkl"

    @staticmethod
    def save_session_data() -> None:
        """Save current session data using pickle."""
        session_data = {
            "customer_name": app_state.customer_name,
            "customer_email": app_state.customer_email,
            "customer_phone": app_state.customer_phone,
            "num_orders": app_state.num_orders,
            "restaurant_name": app_state.restaurant_name,
        }
        try:
            with open(SessionUtils.SESSION_FILE, "wb") as f:
                pickle.dump(session_data, f)
            logger.info(f"Session data saved to {SessionUtils.SESSION_FILE}.")
        except Exception as e:
            StatusPrinter.print_status(f"Failed to save session: {str(e)}", "error")
            logger.error(f"Failed to save session data: {e}")

    @staticmethod
    def load_session_data() -> bool:
        """Load previous session data using pickle."""
        try:
            if not Path(SessionUtils.SESSION_FILE).exists():
                logger.info(f"Session file {SessionUtils.SESSION_FILE} not found. No previous session loaded.")
                return False

            with open(SessionUtils.SESSION_FILE, "rb") as f:
                data = pickle.load(f)

            app_state.customer_name = data.get("customer_name")
            app_state.customer_email = data.get("customer_email")
            app_state.customer_phone = data.get("customer_phone")
            app_state.num_orders = data.get("num_orders")
            app_state.restaurant_name = data.get("restaurant_name")

            logger.info(f"Session data loaded from {SessionUtils.SESSION_FILE}.")
            # Check if essential data is present
            return bool(
                app_state.customer_email or app_state.customer_name or app_state.customer_phone or app_state.num_orders
            )  # Return true if *any* data was loaded
        except FileNotFoundError:
            logger.info(f"Session file {SessionUtils.SESSION_FILE} not found. No previous session loaded.")
            return False
        except (pickle.UnpicklingError, EOFError, Exception) as e:
            logger.warning(f"Failed to load or parse session data from {SessionUtils.SESSION_FILE}: {e}")
            # Optionally remove corrupted file
            # Path(SessionUtils.SESSION_FILE).unlink(missing_ok=True)
            return False


# UI handling
class StatusPrinter:
    """Handle display of status messages and UI elements."""

    log_level: str = "INFO"  # Track current log level
    verbose: bool = True  # Control verbose output (detailed logs) - toggled with log level

    # Map status codes to log levels for filtering
    STATUS_MAP = {
        "success": "INFO",
        "info": "INFO",
        "warning": "WARNING",
        "error": "ERROR",
        "alert": "WARNING",
    }

    # Level hierarchy for comparison
    LEVEL_HIERARCHY = {
        "OFF": -1,
        "DEBUG": 0,
        "INFO": 1,
        "WARNING": 2,
        "ERROR": 3,
        "CRITICAL": 4,
    }

    @staticmethod
    def print_header(title: str) -> None:
        """Print a styled header."""
        with app_state.print_lock:
            app_state.console.print(
                Panel(
                    Align.center(title),
                    width=Config.HEADER_WIDTH,
                    style="header",
                    border_style=Config.MAIN_COLOR,
                )
            )

    @staticmethod
    def print_status(message: str, status: str = "info") -> None:
        """Print a status message with appropriate styling, respecting log level.

        Args:
            message: Message to display
            status: Status type (success, info, warning, error, etc.)
        """
        # Always show messages with prefixes in OFF mode, regardless of content
        prefix = StatusPrinter.get_prefix(status)
        has_emoji = any(
            emoji in message
            for emoji in StyleConfig.SYMBOLS.values()
            if isinstance(emoji, str) and len(emoji) == 1  # Check for single char emojis
        )
        has_emoji_prefix = prefix in StyleConfig.SYMBOLS.values()

        # Special case handling:
        # 1. In OFF mode, show messages with emoji prefix or emoji in content, or important UI prompts
        important_ui_keywords = [
            "Press",
            "Using",
            "Enter",
            "Select",
            "saved",
            "Waiting",
            "Currently have",
            "Please log in",
            "Initializing",
            "Detected",
            "completion",
        ]
        is_important_ui = any(keyword in message for keyword in important_ui_keywords)

        if StatusPrinter.log_level == "OFF" and not (has_emoji or has_emoji_prefix or is_important_ui):
            return  # Hide non-essential messages in OFF mode

        # Get the log level corresponding to this status
        msg_level = StatusPrinter.STATUS_MAP.get(status, "INFO")

        # Skip if current log level doesn't include this message's level
        # But allow all messages with emoji indicators or important UI regardless of level
        if StatusPrinter.log_level != "OFF":  # Skip level check for OFF mode
            level_order = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            if StatusPrinter.LEVEL_HIERARCHY[StatusPrinter.log_level] > StatusPrinter.LEVEL_HIERARCHY[msg_level]:
                if not (has_emoji or has_emoji_prefix or is_important_ui):
                    return

        # In non-verbose mode (OFF or !DEBUG), only show messages with emojis or important UI
        if not StatusPrinter.verbose and not (has_emoji or has_emoji_prefix or is_important_ui):
            return

        # Format and print the styled message
        app_state.console.print(f"{prefix} : {message}", markup=True)

    @staticmethod
    def show_countdown(seconds: int, message: str) -> bool:
        """Show a countdown timer with skip option. Returns True if skipped."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[countdown]{task.description}"),
            BarColumn(),
            console=app_state.console,
            transient=True,
        ) as progress:
            task = progress.add_task(f"{message}: {seconds}s (ENTER to skip)", total=seconds)

            # Calculate loop iterations based on check interval
            iterations = int(seconds / Config.KEY_CHECK_INTERVAL)
            advance_per_iteration = seconds / iterations if iterations > 0 else 0

            for _ in range(iterations):
                run_state.wait_if_paused()  # Check for pause during countdown
                if InputHandler.kbhit():
                    key = InputHandler.getch()
                    if key in (b"\r", b"\n", "\r", "\n"):
                        return True  # Skipped
                    if key in (b"q", "q"):
                        StatusPrinter.print_status("User requested exit during countdown", "info")
                        logger.info("User requested exit during countdown")
                        os._exit(0)  # Force exit
                    # Allow pausing during countdown too
                    elif key in (b"p", "p"):
                        is_paused = run_state.toggle_pause()
                        if is_paused:
                            StatusPrinter.print_status("Countdown PAUSED - Press 'p' to resume", "warning")
                        else:
                            StatusPrinter.print_status("Countdown RESUMED", "success")

                time.sleep(Config.KEY_CHECK_INTERVAL)
                progress.update(task, advance=advance_per_iteration)

            # Ensure progress bar completes
            progress.update(task, completed=seconds, total=seconds)
            return False  # Not skipped

    @staticmethod
    def show_session_preview(session: Dict[str, Any]) -> None:
        """Display a preview of the session data in a table."""
        # Use the PREVIOUS SESSION header for consistency
        StatusPrinter.print_header("PREVIOUS SESSION")
        app_state.console.print("")

        table = Table(show_header=False, box=None, padding=(0, 2))
        table.add_column("Field", style="info", justify="right")
        table.add_column("Value", style="white", justify="left")

        # Map internal keys to user-friendly names
        key_map = {
            "customer_name": "Full Name",
            "customer_email": "Email",
            "customer_phone": "Phone Number",
            "num_orders": "Number of Orders",
            "restaurant_name": "Restaurant Name",
        }

        # Add rows for non-empty values
        added_row = False
        for key, display_name in key_map.items():
            value = session.get(key)
            if value:  # Only show fields with values
                table.add_row(f"{display_name}:", str(value))
                added_row = True

        if not added_row:
            app_state.console.print("  [dim]No previous session details found.[/dim]")
        else:
            app_state.console.print(table)

        app_state.console.print("")  # Add spacing after the table

    @staticmethod
    def print_large_title() -> None:
        """Print the application title with styling."""
        current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        date_message = f"[#D91400]Current Date & Time: {current_datetime}[/#D91400]"
        app_state.console.print(date_message.rjust(Config.HEADER_WIDTH))
        app_state.console.print("═" * Config.HEADER_WIDTH)
        title = "REMOVAL FROM DEACTIVATED PROv3.1".center(Config.HEADER_WIDTH - 2)
        app_state.console.print(f"║{title}║")
        app_state.console.print("═" * Config.HEADER_WIDTH)

    @classmethod
    def get_prefix(cls, status: str) -> str:
        """Get the emoji prefix for a given status type.

        Args:
            status: Status level (success, info, warning, error, etc.)

        Returns:
            The appropriate emoji symbol for the status
        """
        # Map status to appropriate symbol from StyleConfig
        status_map = {
            "success": StyleConfig.SYMBOLS.get("success", "✅"),
            "info": StyleConfig.SYMBOLS.get("info", "ℹ️"),
            "warning": StyleConfig.SYMBOLS.get("warning", "⚠️"),
            "error": StyleConfig.SYMBOLS.get("error", "❌"),
            "alert": StyleConfig.SYMBOLS.get("warning", "⚠️"),  # Use warning symbol for alert
            "processing": StyleConfig.SYMBOLS.get("processing", "🔄"),
            "debug": StyleConfig.SYMBOLS.get("debug", "⚙️"),
        }

        # Return the appropriate symbol or a default one if not found
        return status_map.get(status, StyleConfig.SYMBOLS.get("arrow", ">"))  # Use arrow as default


class InputHandler:
    """Cross-platform input handling."""

    @staticmethod
    def kbhit() -> bool:
        """Check if a keyboard key has been pressed (cross-platform)."""
        if platform.system() == "Windows":
            return msvcrt.kbhit()
        else:
            dr, _, _ = select.select([sys.stdin], [], [], 0)
            return bool(dr)

    @staticmethod
    def getch() -> Union[bytes, str]:
        """Get a single character from the keyboard (cross-platform)."""
        if platform.system() == "Windows":
            return msvcrt.getch()
        else:
            try:
                fd = sys.stdin.fileno()
                old_settings = termios.tcgetattr(fd)
                try:
                    tty.setraw(fd)
                    ch = sys.stdin.read(1)
                finally:
                    termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
            except (ImportError, AttributeError, termios.error):  # Added termios.error
                ch = sys.stdin.read(1)
                if ch == "\n":
                    ch = "\r"
            return ch

    @staticmethod
    def get_user_input(prompt: str, required: bool = True, allow_empty: bool = False) -> str:
        """Get user input with validation."""
        while True:
            try:
                value = app_state.console.input(f"[yellow]{Config.INPUT_SYMBOL} {prompt}: [/yellow]").strip()

                if allow_empty and value == "":
                    return value

                if not required:
                    return value

                if value in ["1", "2", "3", "4", "5"]:  # Handle menu inputs
                    return value

                if required and not value:
                    StatusPrinter.print_status("This field is required", "error")
                    continue

                return value
            except EOFError:  # Handle cases where input stream ends unexpectedly (e.g., piping)
                logger.error("EOFError encountered while getting user input. Exiting.")
                StatusPrinter.print_status("Input stream ended unexpectedly. Exiting.", "error")
                sys.exit(1)
            except Exception as e:
                logger.error(f"Unexpected error during input: {e}")
                StatusPrinter.print_status(f"An input error occurred: {e}", "error")
                # Decide whether to retry or exit based on error type if needed
                sys.exit(1)

    @staticmethod
    def countdown_timer(seconds: float, message: str = "Waiting") -> None:
        """Display a countdown timer that can be skipped. (Simplified Version)"""
        if seconds <= 0:
            return

        interval = 0.1  # Check interval
        iterations = int(seconds / interval)

        for i in range(iterations):
            remaining = seconds - (i * interval)
            # Ensure minimum 0 seconds displayed
            display_seconds = max(0, round(remaining))

            # Use \r for carriage return to overwrite the line
            sys.stdout.write(f"\r{' ' * Config.HEADER_WIDTH}")  # Clear line
            sys.stdout.write(f"\r{Config.INPUT_SYMBOL} {message}: {display_seconds}s (ENTER to skip)")
            sys.stdout.flush()

            # Check for input without blocking
            start_time = time.time()
            while time.time() - start_time < interval:
                if InputHandler.kbhit():
                    key = InputHandler.getch()
                    if key in (b"\r", b"\n", "\r", "\n"):
                        sys.stdout.write(f"\r{' ' * Config.HEADER_WIDTH}\r")  # Clear line
                        sys.stdout.flush()
                        return  # Skip countdown
                    elif key in (b"q", "q"):
                        StatusPrinter.print_status("User requested exit during countdown", "info")
                        logger.info("User requested exit during countdown")
                        sys.exit(0)  # Force exit
                time.sleep(0.01)  # Prevent busy-waiting

        # Clear the line after countdown finishes
        sys.stdout.write(f"\r{' ' * Config.HEADER_WIDTH}\r")
        sys.stdout.flush()

    @staticmethod
    def print_menu_option(key: str, text: str, selected: bool = False) -> None:
        """Print a menu option with styling."""
        bg_style = StyleConfig.HIGHLIGHT_STYLE if selected else RichStyle(color="white")
        app_state.console.print(f" [{bg_style}][{key}][/{bg_style}] {text}")

    @staticmethod
    def get_simple_input(prompt: str) -> str:
        """Get user input with consistent styling and spacing."""
        app_state.console.print("")
        return app_state.console.input(f"{Config.INPUT_SYMBOL} {prompt}: ").strip()


# Error handling
def print_error(error: Exception, context: str = "") -> None:
    """Print formatted error message."""
    error_msg = f"{type(error).__name__}: {str(error)}"
    if context:
        error_msg = f"{context}: {error_msg}"
    StatusPrinter.print_status(error_msg, "error")
    app_state.console.print("")


def retry_on_error(func):
    """Decorator for retrying functions with proper error handling."""

    def wrapper(*args, **kwargs):
        for attempt in range(Config.MAX_RETRIES):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if attempt < Config.MAX_RETRIES - 1:
                    StatusPrinter.print_status(
                        f"Attempt {attempt + 1}/{Config.MAX_RETRIES} failed: {str(e)}",
                        "warning",
                    )
                    logger.warning(f"Attempt {attempt + 1}/{Config.MAX_RETRIES} of {func.__name__} failed: {str(e)}")
                    time.sleep(Config.RETRY_DELAY * (attempt + 1))  # Exponential backoff
                else:
                    print_error(e, f"Failed after {Config.MAX_RETRIES} attempts")
                    logger.error(f"{func.__name__} failed after {Config.MAX_RETRIES} attempts: {str(e)}")
                    return None  # Indicate failure

    return wrapper


# WebDriver utilities
class BrowserManager:
    """Manage browser instances and interactions."""

    COOKIE_FILE = "cookies_deactivated.pkl"

    def __init__(self):
        self.driver: Optional[uc.Chrome] = None

    def create_driver(self, force_headless: bool = False) -> uc.Chrome:
        """Create and configure Chrome WebDriver with undetected-chromedriver."""
        # Get latest configuration values
        try:
            from config_module import get_config

            banned_config = get_config(for_banned=True)
            app_state.headless_mode = banned_config.get("HEADLESS_MODE", app_state.headless_mode)
            app_state.test_mode = banned_config.get("TEST_MODE", app_state.test_mode)
            app_state.max_orders_per_batch = banned_config.get("MAX_ORDERS_PER_BATCH", Config.MAX_BATCH_SIZE)
            logger.debug(f"Refreshed configuration: headless={app_state.headless_mode}, test={app_state.test_mode}")
        except Exception as e:
            logger.error(f"Failed to refresh configuration for driver: {e}")
            # Continue with existing values

        options = uc.ChromeOptions()
        browser_path = Config.get_browser_path()
        if browser_path:
            options.binary_location = browser_path
            logger.debug(f"Using browser at: {browser_path}")
        else:
            logger.warning("No supported browser found. Using default Chrome.")

        options.add_argument("--disable-notifications")
        options.add_argument("--disable-infobars")
        options.add_argument("--mute-audio")
        # Use a more common mobile user agent
        options.add_argument(
            "--user-agent=Mozilla/5.0 (Linux; Android 11; SM-G991U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.50 Mobile Safari/537.36"
        )
        cookies_ext_path = Path(__file__).parent / "cookie_editor"
        if cookies_ext_path.exists():
            options.add_argument(f"--load-extension={cookies_ext_path}")
            logger.debug(f"Loading extension: {cookies_ext_path}")
        else:
            logger.warning(f"Cookie editor extension not found at {cookies_ext_path}")

        use_headless = force_headless or app_state.headless_mode
        logger.debug(
            f"Creating driver with headless={use_headless} (app_state.headless_mode={app_state.headless_mode}, "
            f"force_headless={force_headless})"
        )

        if use_headless:
            options.add_argument("--headless=new")
            options.add_argument("--window-size=1920,1080")  # Often needed for headless
            options.add_argument("--disable-gpu")  # Recommended for headless
            logger.debug("Headless mode enabled")

        try:
            # Use the `headless` parameter to ensure proper headless mode activation
            driver_args = {
                "options": options,
                "driver_executable_path": None,
            }  # Explicitly set path to None to use internal logic
            # Only pass headless parameter with older versions of undetected-chromedriver
            # Check if uc version attribute exists and compare
            uc_version_str = getattr(uc, "__version__", "0.0.0")
            try:
                from packaging import version

                if version.parse(uc_version_str) < version.parse("3.4.0"):
                    driver_args["headless"] = use_headless
            except ImportError:
                logger.warning(
                    "`packaging` library not found. Cannot check undetected-chromedriver version accurately."
                )
                # Fallback to checking attribute existence for older versions
                if "headless" in uc.Chrome.__init__.__code__.co_varnames:
                    driver_args["headless"] = use_headless

            self.driver = uc.Chrome(**driver_args)
            if not use_headless:
                self.driver.set_window_size(660, 940)  # Set mobile size only if not headless
            logger.debug(f"Browser driver created successfully (Headless: {use_headless})")
            return self.driver
        except WebDriverException as e:
            logger.error(f"WebDriverException creating driver: {e}")
            if "cannot find" in str(e).lower() and browser_path:
                logger.error(f"Browser binary not found at specified path: {browser_path}")
            elif "version" in str(e).lower():
                logger.error("Mismatch between ChromeDriver and Chrome browser version.")
                logger.error("Ensure your ChromeDriver is compatible with your installed Chrome/Brave version.")
            else:
                logger.error("Check if Chrome/Brave is installed and accessible.")
            raise  # Re-raise the exception after logging details
        except Exception as e:
            logger.error(f"Failed to create driver: {str(e)}")
            logger.error(traceback.format_exc())  # Log full traceback for other errors
            raise

    def cleanup_driver(self) -> None:
        """Clean up the driver and browser resources."""
        if not self.driver:
            return

        try:
            # Always preserve browser - never close it automatically
            app_state.console.print("")
            StatusPrinter.print_status("🔒 Browser windows preserved - staying open for manual inspection", "success")
            StatusPrinter.print_status("💡 You can manually close the browser when you're done", "info")
            logger.info("Browser windows preserved - never closing automatically for debugging purposes.")

            # Don't call driver.quit() - this keeps the browser open
            # Just clear the driver reference so we don't try to control it anymore

        except Exception as e:
            StatusPrinter.print_status(f"Error during cleanup: {str(e)}", "error")
            logger.error(f"Error during driver cleanup: {e}")
        finally:
            # Clear driver reference but don't quit the browser
            self.driver = None  # Ensure driver is marked as None after cleanup attempt

    @staticmethod
    @retry_on_error
    def handle_stale_element(driver: uc.Chrome, find_func, *args, **kwargs):
        """Handle stale element with retries."""
        element = find_func(*args, **kwargs)
        # Basic check for element existence before interacting
        if element:
            # Attempt a simple check that forces re-validation
            _ = element.is_displayed()
            return element
        return None  # Indicate element not found or couldn't be validated

    @staticmethod
    def get_element_from_text(
        parent: Union[uc.Chrome, uc.WebElement],
        tag_name: str,
        text: str,
        exact: bool = True,
    ) -> Optional[Union[uc.WebElement, SeleniumWebElement]]:
        """Find element by tag and text."""
        try:
            elements = parent.find_elements(By.TAG_NAME, tag_name)
            for element in elements:
                try:
                    element_text = element.text
                    if exact:
                        if element_text == text:
                            return element
                    else:
                        if text.lower() in element_text.lower():
                            return element
                except Exception:  # Catch potential stale element errors during text access
                    logger.debug(f"Stale element encountered while checking text for tag '{tag_name}'. Skipping.")
                    continue  # Skip this element and check the next
            return None
        except Exception as e:
            logger.debug(f"Element search by text failed: {e}")
            return None

    @staticmethod
    def get_element_from_attribute(
        parent: uc.WebElement, tag_name: str, attribute: str, value: str
    ) -> Optional[Union[uc.WebElement, SeleniumWebElement]]:
        """Find element by attribute."""
        try:
            # More specific XPath search for attribute
            elements = parent.find_elements(By.XPATH, f".//{tag_name}[@{attribute}='{value}']")
            if elements:
                return elements[0]  # Return the first match
        except Exception as e:
            logger.debug(f"Element search by attribute failed: {e}")
        return None

    # --- Updated auto_login (v4 - Simplified Check) ---
    @staticmethod
    def auto_login(driver: uc.Chrome) -> bool:
        """Attempt automatic login with saved cookies."""
        if not Path(BrowserManager.COOKIE_FILE).exists():
            logger.warning(f"Cookie file not found: {BrowserManager.COOKIE_FILE}. Cannot auto-login.")
            return False
        try:
            with open(BrowserManager.COOKIE_FILE, "rb") as f:
                cookies = pickle.load(f)

            # Navigate to a base domain first before adding cookies
            driver.get("https://www.doordash.com/")
            WebDriverWait(driver, 5).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(0.5)  # Short wait

            logger.debug(f"Attempting auto-login with {len(cookies)} cookies from {BrowserManager.COOKIE_FILE}...")

            # Delete existing cookies for the domain first
            driver.delete_all_cookies()
            time.sleep(0.2)

            # Add cookies carefully
            for cookie in cookies:
                if not all(k in cookie for k in ["name", "value"]):
                    logger.warning(f"Skipping invalid cookie: {cookie}")
                    continue
                cookie.pop("expiry", None)
                cookie.pop("httpOnly", None)
                cookie.pop("sameSite", None)
                if "domain" in cookie and cookie["domain"].startswith("."):
                    cookie["domain"] = cookie["domain"][1:]
                # Ensure domain is broadly correct for doordash.com
                if "domain" in cookie and not ("doordash.com" in cookie["domain"]):
                    logger.warning(f"Skipping cookie with potentially incorrect domain: {cookie['domain']}")
                    continue
                # Add domain if missing, defaulting to base domain
                if "domain" not in cookie:
                    cookie["domain"] = "doordash.com"

                try:
                    driver.add_cookie(cookie)
                except Exception as cookie_error:
                    logger.warning(f"Could not add cookie: {cookie.get('name', 'N/A')} - Error: {cookie_error}")

            # Navigate to the home page to verify login
            driver.get("https://www.doordash.com/home")

            # --- SIMPLIFIED LOGIN CHECK ---
            # Wait for page load and potential redirects
            login_wait_time = 5  # Wait up to 5 seconds
            logger.debug(f"Waiting up to {login_wait_time}s for URL to confirm login...")
            time.sleep(login_wait_time)  # Static wait after navigation

            current_url = driver.current_url
            if "doordash.com/home" in current_url or "doordash.com/orders" in current_url:
                logger.info(f"Auto-login successful (URL check: {current_url}).")
                return True
            else:
                logger.warning(f"Auto-login failed: URL after wait is {current_url} (Expected /home or /orders).")
                return False
            # --- END SIMPLIFIED LOGIN CHECK ---

        except FileNotFoundError:
            logger.warning(f"Cookie file not found: {BrowserManager.COOKIE_FILE}")
            return False
        except (pickle.UnpicklingError, EOFError) as e:
            logger.error(f"Error loading cookies from {BrowserManager.COOKIE_FILE}: {e}. File might be corrupted.")
            return False
        except Exception as e:
            logger.error(f"Auto-login failed with unexpected error: {e}")
            logger.error(traceback.format_exc())
            return False

    # --- End of updated auto_login ---

    @staticmethod
    def save_cookies(driver: uc.Chrome) -> bool:
        """Save browser cookies to file."""
        try:
            cookies = driver.get_cookies()
            if not cookies:
                logger.warning("No cookies found in browser to save.")
                return False

            with open(BrowserManager.COOKIE_FILE, "wb") as f:
                pickle.dump(cookies, f)
            logger.info(f"Cookies saved successfully to {BrowserManager.COOKIE_FILE} ({len(cookies)} cookies).")
            return True
        except Exception as e:
            logger.error(f"Failed to save cookies: {e}")
            return False


# =============================================================================
#  CHAT LOGGER (INNOVATIVE ENHANCEMENT)
# =============================================================================


class ChatLogger:
    """
    Logs messages sent to support chat into a CSV file for auditing or reference.
    This is an optional enhancement that doesn't break existing functionality.
    """

    FIELDNAMES = ["timestamp", "order_id", "agent_name", "sender", "message"]

    def __init__(self):
        # Use the data directory for logs instead of current directory
        self.log_dir = Config.get_data_dir()
        self.log_file = self.log_dir / "chat_log.csv"

        # Create log file with headers if not present
        if not self.log_file.exists():
            try:
                self.log_dir.mkdir(parents=True, exist_ok=True)  # Ensure directory exists
                with open(self.log_file, "w", newline="", encoding="utf-8") as f:
                    writer = csv.DictWriter(f, fieldnames=self.FIELDNAMES)
                    writer.writeheader()
                logger.info(f"Chat log file created at: {self.log_file}")
            except Exception as e:
                logger.error(f"Failed to create chat log file: {e}")

    def log_message(self, order_id: str, agent_name: str, sender: str, message: str) -> None:
        """Log a message to the CSV file with current timestamp.
        This function logs both outgoing (User) and incoming (Agent) messages
        to maintain a complete conversation history.

        Args:
            order_id: The order ID associated with this message
            agent_name: Name of the support agent
            sender: Who sent the message ('User' or 'Agent')
            message: Content of the message
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        direction = "→" if sender == "User" else "←"

        try:
            with open(self.log_file, "a", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=self.FIELDNAMES)
                writer.writerow(
                    {
                        "timestamp": timestamp,
                        "order_id": order_id or "N/A",  # Handle potential None order_id
                        "agent_name": agent_name or "Unknown",  # Handle potential None agent_name
                        "sender": sender,
                        "message": message.replace("\n", "\\n"),  # Escape newlines for CSV
                    }
                )

            # Trim message for logging if it's too long
            log_message = message[:50].replace("\n", " ") + "..." if len(message) > 50 else message.replace("\n", " ")
            logger.debug(f"Logged {direction} {sender} message for order {order_id or 'N/A'}: '{log_message}'")
        except Exception as e:
            logger.error(f"Failed to log chat message: {e}")
            # Still attempt to log to console if file logging fails
            StatusPrinter.print_status(f"Message ({sender}): {message[:30]}...", "debug")


class Order:
    """Represent and manage a DoorDash order."""

    def __init__(self, element: Union[uc.WebElement, SeleniumWebElement]):
        """Initialize order from receipt link element."""
        self.receipt_url: Optional[str] = None
        self.id: Optional[str] = None
        self.short_id: Optional[str] = None
        self.url: Optional[str] = None
        self.tab_handle: Optional[str] = None
        self.wait_time: Optional[int] = None

        try:
            # Attempt to get href directly first
            self.receipt_url = element.get_attribute("href")

            # If href is missing or empty, try finding a nested link (fallback)
            if not self.receipt_url:
                try:
                    nested_link = element.find_element(By.CSS_SELECTOR, 'a[href*="/orders/"]')
                    self.receipt_url = nested_link.get_attribute("href")
                except NoSuchElementException:
                    logger.warning("Nested receipt link not found within element.")
                    pass  # Allow graceful failure if nested link isn't there

            if not self.receipt_url:
                raise ValueError("No receipt URL found in element or its children")

            # Extract ID robustly, handling potential query parameters
            path_parts = self.receipt_url.split("?")[0].split("/")
            order_id_part = next(
                (part for part in reversed(path_parts) if part.isdigit() or len(part) > 10), None
            )  # Find likely ID

            if not order_id_part or "orders" not in self.receipt_url:
                raise ValueError(f"Could not extract valid order ID from URL: {self.receipt_url}")

            self.id = order_id_part
            self.short_id = self.id[-6:].upper()
            self.url = f"https://doordash.com/orders/{self.id}/help/"
            logger.info(f"Initialized order: {self.id}, Short ID: {self.short_id}, URL: {self.url}")
        except Exception as e:
            StatusPrinter.print_status(f"Error initializing order: {str(e)}", "error")
            logger.error(f"Error initializing order: {e} - URL: {self.receipt_url}")
            raise  # Re-raise to signal failure

    @classmethod
    def create_virtual(cls, url: str) -> "Order":
        """Create a virtual order that points directly to the help chat URL."""
        order = cls.__new__(cls)  # Create instance without calling __init__
        order.receipt_url = url
        order.id = "virtual"
        order.short_id = "VIRTUAL"
        order.url = url
        order.tab_handle = None
        order.wait_time = None
        logger.info(f"Created virtual order with URL: {url}")
        return order

    @retry_on_error
    def open_support_chat(self, driver: uc.Chrome) -> bool:
        """Open support chat with improved retry logic and logging."""
        if not self.url:
            StatusPrinter.print_status("❌ CRITICAL ERROR: Order URL is missing - cannot open support chat", "error")
            logger.error(f"Cannot open support chat: Order URL is missing.")
            return False

        try:
            StatusPrinter.print_status(f"🌐 Navigating to: {self.url}", "info")
            driver.get(self.url)
            logger.info(f"Opening support chat for order: {self.short_id} at URL: {self.url}")

            # Check if page loaded successfully
            current_url = driver.current_url
            if "error" in current_url.lower() or "404" in current_url or "not-found" in current_url:
                StatusPrinter.print_status(f"❌ PATH ERROR: Invalid URL detected - {current_url}", "error")
                logger.error(f"Invalid URL detected after navigation: {current_url}")
                return False

        except Exception as e:
            StatusPrinter.print_status(f"❌ NAVIGATION ERROR: Failed to load URL {self.url}: {str(e)}", "error")
            logger.error(f"Failed to navigate to URL {self.url}: {e}")
            return False

        # For virtual order, we're already at the chat page
        if self.id == "virtual":
            try:
                StatusPrinter.print_status("🔍 Looking for chat interface elements...", "info")

                # Check if we're actually on a chat page by looking for specific chat indicators
                chat_indicators = [
                    "textarea[data-testid='chat-input-textarea']",
                    "textarea[placeholder*='message']",
                    "textarea[placeholder*='chat']",
                    "[data-testid*='chat']",
                    ".chat-container",
                    ".support-chat",
                ]

                chat_found = False
                for indicator in chat_indicators:
                    try:
                        element = WebDriverWait(driver, 2).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, indicator))
                        )
                        if element:
                            StatusPrinter.print_status(f"✅ Chat interface found with selector: {indicator}", "success")
                            chat_found = True
                            break
                    except TimeoutException:
                        continue

                if not chat_found:
                    # Check if we're on the wrong page (like main DoorDash page)
                    current_url = driver.current_url
                    if "doordash.com" in current_url and "help" not in current_url and "chat" not in current_url:
                        StatusPrinter.print_status(
                            "❌ WRONG PAGE: Landed on main DoorDash page instead of chat", "error"
                        )
                        StatusPrinter.print_status(f"Current URL: {current_url}", "warning")
                        StatusPrinter.print_status("🔧 The chat URL is incorrect - update Config.CHAT_URL", "warning")
                        return False
                    else:
                        StatusPrinter.print_status("❌ CHAT INTERFACE ERROR: No chat elements found", "error")
                        StatusPrinter.print_status(f"Current URL: {current_url}", "warning")
                        return False

                logger.info(f"Successfully opened chat interface for virtual order")
                return True

            except Exception as e:
                StatusPrinter.print_status(f"❌ CHAT INTERFACE ERROR: {str(e)}", "error")
                logger.error(f"Failed to open chat for virtual order: {e}")
                logger.error(traceback.format_exc())
                return False

        # Original implementation for real orders
        wait = WebDriverWait(driver, 3)
        try:
            # Wait for essential elements to be present
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located(
                    (
                        By.XPATH,
                        '//button[@aria-label="It\'s something else"] | //button[contains(., "Contact support")]',
                    )
                )
            )
            time.sleep(0.3)  # Static wait can sometimes help with tricky UI updates

            # Click "It's something else"
            something_else = wait.until(
                EC.element_to_be_clickable((By.XPATH, '//button[@aria-label="It\'s something else"]'))
            )
            # Use JS click for reliability
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", something_else)
            time.sleep(0.1)
            driver.execute_script("arguments[0].click();", something_else)
            logger.debug(f"Clicked 'It's something else' for order: {self.short_id}")

            # Wait briefly for the next button to appear/become clickable
            time.sleep(0.2)

            # Click "Contact support"
            contact = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Contact support')]")))
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", contact)
            time.sleep(0.1)
            driver.execute_script("arguments[0].click();", contact)
            logger.debug(f"Clicked 'Contact support' for order: {self.short_id}")

            # Wait for chat interface indicator (e.g., the input area)
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "textarea, [role='textbox']"))
            )
            logger.info(f"Successfully opened chat interface for order: {self.short_id}")
            return True

        except TimeoutException as e:
            logger.error(f"Timeout waiting for chat elements for order {self.short_id}: {e}")
            # Capture page source for debugging timeouts
            try:
                page_source = driver.page_source[:5000]  # Limit source size
                logger.debug(f"Page source at timeout for {self.short_id}:\n{page_source}...")
            except Exception as ps_error:
                logger.error(f"Could not get page source on timeout: {ps_error}")
            return False
        except Exception as e:
            logger.error(f"Failed to open chat for order {self.short_id}: {e}")
            logger.error(traceback.format_exc())
            return False

    @staticmethod
    def validate_chat_url(url: str) -> bool:
        """Validate that the chat URL is accessible and contains expected elements."""
        try:
            import requests

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                StatusPrinter.print_status(f"✅ URL is accessible: {url}", "success")
                return True
            else:
                StatusPrinter.print_status(f"❌ URL returned status {response.status_code}: {url}", "error")
                return False
        except Exception as e:
            StatusPrinter.print_status(f"❌ URL validation failed: {str(e)}", "error")
            return False

    @staticmethod
    def get_virtual_order() -> List["Order"]:
        """Create a virtual order pointing directly to the help chat URL."""
        try:
            # Use configurable chat URL from Config
            chat_url = Config.CHAT_URL

            StatusPrinter.print_status(f"🔗 Using configured chat URL: {chat_url}", "info")
            StatusPrinter.print_status("💡 To change URL, update Config.CHAT_URL in the script", "info")

            # Validate URL accessibility (optional, can be disabled for speed)
            # Order.validate_chat_url(chat_url)

            virtual_order = Order.create_virtual(chat_url)
            StatusPrinter.print_status("✅ Created virtual order for direct chat access", "success")
            logger.info(f"Created virtual order for direct chat access with URL: {chat_url}")
            return [virtual_order]
        except Exception as e:
            StatusPrinter.print_status(f"❌ CRITICAL ERROR: Failed to create virtual order: {str(e)}", "error")
            StatusPrinter.print_status(
                f"🔗 Attempted URL: {chat_url if 'chat_url' in locals() else Config.CHAT_URL}", "warning"
            )
            StatusPrinter.print_status(
                "🔧 To fix: Update Config.CHAT_URL with the correct DoorDash chat path", "warning"
            )
            logger.error(f"CRITICAL ERROR: Failed to create virtual order: {e}\n{traceback.format_exc()}")
            return []

    def send_message_to_support(self, message: str, driver: uc.Chrome) -> bool:
        """Send message to support chat using SupportChat utility class."""
        try:
            support_chat = SupportChat()
            logger.info(f"Sending message to support for order {self.short_id}: {message[:30]}...")
            # Pass order ID to the send method
            return support_chat.send_message_to_support(message, driver, self.id)
        except Exception as e:
            StatusPrinter.print_status(f"Message send failed for {self.short_id}: {str(e)}", "error")
            logger.error(f"Message send failed for order {self.short_id}: {e}")
            return False


class SupportChat:
    """Handle interactions with the support chat interface."""

    class TextAreaNotFoundException(Exception):
        """Exception raised when textarea cannot be found."""

        pass

    MAX_RETRIES: int = 3
    WAIT_TIMEOUT: int = 1  # Increased wait timeout for finding elements
    RETRY_DELAY: float = 1.5  # Increased delay between retries
    SELECTORS: List[str] = [
        "textarea[data-testid='chat-input-textarea']",  # Specific chat textarea
        "textarea[placeholder*='message']",  # Textarea with message placeholder
        "textarea[placeholder*='chat']",  # Textarea with chat placeholder
        "textarea[aria-label*='message']",  # Textarea with message aria-label
        ".chat-input textarea",  # Textarea inside chat input container
        "[data-testid*='chat'] textarea",  # Textarea inside chat component
        # Avoid generic selectors that might match search bars:
        # "textarea",  # TOO GENERIC - might match search
        # "input[type='text']",  # TOO GENERIC - might match search
    ]

    def __init__(self):
        self.chat_logger = ChatLogger()  # Initialize our new logger

    def is_chat_support_down(self, driver: uc.Chrome) -> bool:
        """Check if chat support is temporarily down."""
        try:
            down_message_elements = driver.find_elements(
                By.XPATH,
                "//*[contains(text(), 'Chat support is temporarily down') or contains(text(), 'unavailable at the moment')]",
            )
            if down_message_elements and any(elem.is_displayed() for elem in down_message_elements):
                logger.warning("Detected 'Chat support is temporarily down' message")
                return True
            return False
        except Exception as e:
            logger.debug(f"Error checking if chat support is down: {e}")
            return False

    def handle_support_down_countdown(self, driver: uc.Chrome) -> None:
        """Handle the situation when chat support is temporarily down."""
        StatusPrinter.print_status("⚠️ CHAT SUPPORT IS TEMPORARILY DOWN ⚠️", "warning")
        StatusPrinter.print_status(f"Taking a {Config.SUPPORT_DOWN_TIMER}s break before continuing...", "info")
        logger.warning(f"Chat support is temporarily down, taking a {Config.SUPPORT_DOWN_TIMER}s break")

        app_state.console.print("")  # Add spacing

        skipped = StatusPrinter.show_countdown(Config.SUPPORT_DOWN_TIMER, "Chat support down - resuming in")

        if skipped:
            StatusPrinter.print_status("Countdown skipped by user", "info")
            logger.info("Support down countdown skipped by user")
        else:
            StatusPrinter.print_status("Break completed, resuming operations...", "success")
            logger.info("Support down break completed, resuming operations")
        app_state.console.print("")  # Add spacing

    def handle_chat_buttons(self, driver: uc.Chrome) -> bool:
        """Handle chat interface scenarios with optimized button detection."""
        try:
            # Improved XPath for better reliability and combines multiple texts
            xpath = "//button[.//span[normalize-space(.)='Reconnect with an Agent' or normalize-space(.)='Chat with an agent'] or contains(., 'Yes, connect')]"
            buttons = driver.find_elements(By.XPATH, xpath)

            clicked = False
            for button in buttons:
                # Check visibility *and* clickability
                if button.is_displayed() and button.is_enabled():
                    try:
                        # Scroll into view slightly above center for better visibility
                        driver.execute_script(
                            "arguments[0].scrollIntoView({block: 'center', inline: 'nearest'});", button
                        )
                        time.sleep(0.1)  # Short pause after scroll
                        # Use JS click as it's often more reliable
                        driver.execute_script("arguments[0].click();", button)
                        logger.debug(f"Clicked chat button: {button.text[:30]}...")
                        # Wait slightly longer after click for UI update
                        time.sleep(0.5)
                        clicked = True
                        break  # Exit loop once a button is clicked
                    except Exception as click_err:
                        logger.warning(f"Could not click button '{button.text[:30]}...': {click_err}")

            return clicked  # Return True if any button was clicked
        except Exception as e:
            logger.warning(f"Error handling chat buttons: {e}")
            return False

    def find_text_area(self, driver: uc.Chrome, wait: WebDriverWait) -> Optional[uc.WebElement]:
        """Find text area with improved button handling and explicit waits."""
        from selenium.common.exceptions import NoSuchElementException

        if self.handle_chat_buttons(driver):
            # Wait briefly for chat UI to potentially update after button click
            time.sleep(0.5)

        # Try each specific selector individually first
        for selector in self.SELECTORS:
            try:
                # Wait for element to be present first, then check interactability
                element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                if element.is_displayed() and element.is_enabled():
                    logger.debug(f"Found text area with selector: {selector}")
                    return cast(uc.WebElement, element)
            except TimeoutException:
                continue  # Try next selector
            except Exception as e:
                logger.warning(f"Error checking selector '{selector}': {e}")
                continue

        # If specific selectors fail, try a broader search (less reliable)
        try:
            generic_selector = "textarea, [role='textbox']"
            elements = driver.find_elements(By.CSS_SELECTOR, generic_selector)
            for element in elements:
                if element.is_displayed() and element.is_enabled():
                    logger.debug("Found text area using generic selector.")
                    return cast(uc.WebElement, element)
        except Exception as e:
            logger.debug(f"Generic text area search failed: {e}")

        logger.error("Textarea not found or not interactable after trying all selectors.")
        return None  # Explicitly return None if not found

    def focus_text_area(self, text_area: uc.WebElement, driver: uc.Chrome) -> None:
        """Focus and clear the text area using JavaScript."""
        try:
            # Scroll element into view first
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", text_area)
            time.sleep(0.1)
            # Use JS to clear and focus
            driver.execute_script("arguments[0].value = ''; arguments[0].click(); arguments[0].focus();", text_area)
            logger.debug("Focused and cleared text area using JS.")
        except Exception as e:
            logger.warning(f"Failed to focus/clear text area using JS: {e}")
            # Fallback to Selenium methods (less reliable)
            try:
                text_area.clear()
                text_area.click()
                logger.debug("Focused and cleared text area using Selenium methods (fallback).")
            except Exception as fallback_e:
                logger.error(f"Fallback focus/clear failed: {fallback_e}")

    # --- Updated send_message_to_support (v7 - Reverted SendKeys) ---
    def send_message_to_support(self, message: str, driver: uc.Chrome, order_id: str = "unknown") -> bool:
        """
        Send message to support chat using send_keys with small delays, similar to older script.
        Captures and logs both user messages and agent responses.

        Args:
            message: Message to send to support
            driver: WebDriver instance
            order_id: Order ID associated with this message

        Returns:
            True if message was sent successfully
        """
        agent_name = getattr(driver, "_agent_name", None)
        if agent_name is None:
            agent_name = extract_agent_name(driver) or "Support Agent"
            driver._agent_name = agent_name  # Cache it

        for attempt in range(self.MAX_RETRIES):
            run_state.wait_if_paused()  # Check for pause state

            try:
                wait = WebDriverWait(driver, self.WAIT_TIMEOUT)
                text_area = self.find_text_area(driver, wait)

                if not text_area:
                    if self.is_chat_support_down(driver):
                        self.handle_support_down_countdown(driver)
                        return False
                    raise self.TextAreaNotFoundException("Text area not found")

                # Ensure text area is focused and cleared (using existing JS method)
                self.focus_text_area(text_area, driver)
                time.sleep(0.1)  # Brief pause after focus

                # --- Use send_keys approach (like older script) ---
                lines = message.split("\n")
                for i, line in enumerate(lines):
                    try:
                        # Send line at once (usually faster than char by char)
                        text_area.send_keys(line)

                        if i < len(lines) - 1:
                            # Send Shift+Enter for newline using ActionChains
                            ActionChains(driver).key_down(Keys.SHIFT).send_keys(Keys.ENTER).key_up(Keys.SHIFT).perform()
                            # Alternative if ActionChains fails:
                            # text_area.send_keys(Keys.SHIFT, Keys.ENTER)
                        time.sleep(0.05)  # Small delay between lines/actions, as in older script
                    except Exception as send_err:
                        logger.warning(f"Error sending line '{line[:20]}...': {send_err}")
                        raise  # Re-raise to trigger retry or failure

                logger.debug("Finished sending message keys.")
                # --- End send_keys approach ---

                # Brief pause before submission
                time.sleep(0.1)

                # Try submitting with Enter key
                try:
                    # Ensure focus before sending Enter
                    driver.execute_script("arguments[0].focus();", text_area)
                    text_area.send_keys(Keys.RETURN)
                    logger.debug("Submitted message using Enter key.")
                except Exception as enter_err:
                    logger.warning(f"Failed to send with Enter key: {enter_err}. Trying Send button.")
                    # Fallback: Try clicking a potential send button
                    try:
                        # Use a more specific selector if possible, or keep the general one
                        send_button_selector = "button[data-testid='send-message-button'], button[aria-label*='Send']"
                        send_button = WebDriverWait(driver, 2).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, send_button_selector))
                        )
                        # Use JS click for reliability here too
                        driver.execute_script("arguments[0].click();", send_button)
                        logger.debug("Submitted message using Send button.")
                    except Exception as button_err:
                        logger.error(f"Failed to find or click Send button: {button_err}")
                        raise  # Re-raise if both submit methods fail

                log_msg = message[:50].replace("\n", " ") + "..." if len(message) > 50 else message.replace("\n", " ")
                logger.info(f"Sent message to agent '{agent_name}': '{log_msg}'")
                self.chat_logger.log_message(order_id, agent_name, "User", message)

                self.try_capture_agent_response(driver, order_id, agent_name)

                return True  # Message sent successfully

            except self.TextAreaNotFoundException as e:
                logger.warning(f"Text area not found (attempt {attempt + 1}/{self.MAX_RETRIES}): {e}")
                if self.is_chat_support_down(driver):
                    self.handle_support_down_countdown(driver)
                    return False
                # Continue retry loop

            except Exception as e:
                logger.error(f"Failed to send message (attempt {attempt + 1}/{self.MAX_RETRIES}): {e}")
                logger.error(traceback.format_exc())
                if "detached" in str(e).lower() or "no such window" in str(e).lower():
                    logger.error("Browser window or element might be closed. Aborting send.")
                    return False

            if attempt < self.MAX_RETRIES - 1:
                retry_delay = self.RETRY_DELAY * (attempt + 1)
                logger.debug(f"Waiting {retry_delay}s before retry {attempt + 2}")
                time.sleep(retry_delay)

        logger.error(f"Failed to send message after {self.MAX_RETRIES} attempts.")
        return False

    # --- End of updated send_message_to_support ---

    def try_capture_agent_response(self, driver: uc.Chrome, order_id: str, agent_name: str) -> None:
        """Lightweight attempt to capture agent response without blocking execution."""
        try:
            # Check for incoming message bubbles shortly after sending
            time.sleep(0.5)  # Short wait for potential response
            # More specific selector for incoming messages
            elements = driver.find_elements(
                By.CSS_SELECTOR, ".chat-message-bubble:not([class*='outgoing']) .chat-message-text"
            )
            if elements:
                # Check the last few messages for new content
                last_few_messages = elements[-3:]  # Check last 3 messages
                for element in reversed(last_few_messages):  # Check newest first
                    message_text = element.text.strip()
                    # Basic checks: not empty, not just "typing...", not already logged recently
                    if message_text and "typing" not in message_text.lower() and len(message_text) > 3:
                        # Rudimentary check to avoid logging the same message multiple times quickly
                        # A proper solution would involve tracking logged message IDs/hashes
                        if not hasattr(self, "_last_logged_agent_msg") or self._last_logged_agent_msg != message_text:
                            self.chat_logger.log_message(order_id, agent_name, "Agent", message_text)
                            self._last_logged_agent_msg = message_text  # Store last logged message
                            logger.debug(f"Captured immediate agent response: {message_text[:50]}...")
                            break  # Logged the newest response
        except Exception:
            # Don't log exceptions - this is a best-effort feature
            pass

    def extract_agent_messages(self, driver: uc.Chrome) -> List[str]:
        """
        Extract messages from the support agent in the chat window.

        Returns:
            List of message texts from the agent, newest first
        """
        messages = []
        try:
            # Combine selectors for efficiency
            combined_selector = ", ".join(
                [
                    ".chat-message-bubble:not([class*='outgoing']) .chat-message-text",  # Standard incoming text
                    ".message-bubble:not(.outgoing) p",  # Alternative structure
                    ".message:not(.user-message) .content",  # Another common format
                    "[data-testid='agent-message'] span",  # Test ID specific format
                    ".agent-message p",  # Class-based
                    ".chat-message:not(.user-message) .message-body",  # Another variation
                ]
            )

            elements = driver.find_elements(By.CSS_SELECTOR, combined_selector)
            seen_messages = set()  # Avoid duplicates

            # Iterate in reverse to get newest first
            for element in reversed(elements):
                try:
                    if element.is_displayed():
                        message_text = element.text.strip()
                        # Filter out empty, "typing", or already seen messages
                        if (
                            message_text
                            and "is typing" not in message_text.lower()
                            and message_text not in seen_messages
                        ):
                            messages.append(message_text)
                            seen_messages.add(message_text)
                except Exception:
                    continue  # Skip element if error occurs (e.g., stale)

            return messages
        except Exception as e:
            logger.warning(f"Failed to extract agent messages: {e}")
            return []

    def get_latest_agent_message(self, driver: uc.Chrome) -> Optional[str]:
        """
        Get the most recent message from the agent.

        Returns:
            The latest message text or None if no messages found
        """
        messages = self.extract_agent_messages(driver)
        if messages:
            return messages[0]  # Return the most recent message (first in the list)
        return None

    def capture_agent_response(self, driver: uc.Chrome, order_id: str, agent_name: str) -> None:
        """
        Capture any agent responses after sending a message. (Potentially redundant with try_capture)
        Kept for potential future use or different timing.
        """
        try:
            # Wait a moment longer for agent response
            time.sleep(1.0)

            latest_agent_msg = self.get_latest_agent_message(driver)
            if latest_agent_msg:
                # Avoid double logging if try_capture already got it
                if not hasattr(self, "_last_logged_agent_msg") or self._last_logged_agent_msg != latest_agent_msg:
                    logger.info(f"Agent responded: {latest_agent_msg[:50]}...")
                    self.chat_logger.log_message(order_id, agent_name, "Agent", latest_agent_msg)
                    self._last_logged_agent_msg = latest_agent_msg  # Update last logged
        except Exception as e:
            logger.warning(f"Failed to capture agent response: {e}")


class TabManager:
    """Manage browser tabs and their states."""

    def __init__(self, driver: uc.Chrome):
        self.driver: uc.Chrome = driver
        self.active_tabs: Dict[str, str] = {}  # {order_id: handle}
        self.tab_open_times: Dict[str, float] = {}  # {handle: timestamp}
        self.original_handle: Optional[str] = None
        try:
            self.original_handle = driver.current_window_handle
        except NoSuchWindowException:
            logger.warning("TabManager init: Main window already closed or inaccessible.")
            # Attempt to recover or create a new window if necessary
            if driver.window_handles:
                self.original_handle = driver.window_handles[0]
                driver.switch_to.window(self.original_handle)
            else:
                # If absolutely no windows exist, this is problematic
                logger.error("TabManager init: No browser windows available.")
                # Cannot truly initialize TabManager state without a window.
                # The calling code should handle driver creation failure.

        self.processed_tabs: Set[str] = set()  # Track processed tabs
        self.tabs_with_wait_times: Dict[str, int] = {}  # {order_id: wait_time}
        logger.info("TabManager initialized.")

    def track_new_tab(self, handle: str, order_id: str) -> None:
        """Track a new tab's open time."""
        self.tab_open_times[handle] = time.time()
        self.active_tabs[order_id] = handle
        logger.info(f"Tracking new tab: {handle[:8]} for order ID: {order_id}")

    def get_oldest_tabs(self, count: int) -> List[str]:
        """Get handles of oldest tabs by open time."""
        # Filter out the original handle before sorting
        other_tabs = {h: t for h, t in self.tab_open_times.items() if h != self.original_handle}
        sorted_tabs = sorted(other_tabs.items(), key=lambda x: x[1])
        oldest_tabs = [handle for handle, _ in sorted_tabs[:count]]
        logger.debug(f"Oldest tabs (excluding main): {[t[:8] for t in oldest_tabs]}")
        return oldest_tabs

    def close_tabs(self, handles: List[str]) -> int:
        """Close specific tabs and return count of closed tabs."""
        closed = 0
        current_handles = set(self.driver.window_handles)  # Get current set of handles

        for handle in handles:
            if handle == self.original_handle:
                logger.warning("Attempted to close the original tab. Skipping.")
                continue
            if handle not in current_handles:
                logger.debug(f"Tab {handle[:8]} already closed. Skipping.")
                self.tab_open_times.pop(handle, None)  # Clean up tracking if needed
                continue

            try:
                self.driver.switch_to.window(handle)
                self.driver.close()
                closed += 1
                self.tab_open_times.pop(handle, None)
                current_handles.remove(handle)  # Update live set
                logger.debug(f"Closed tab: {handle[:8]}...")
            except NoSuchWindowException:
                logger.warning(f"Tab {handle[:8]} disappeared before it could be closed.")
                self.tab_open_times.pop(handle, None)
                current_handles.discard(handle)  # Ensure it's removed
            except Exception as e:
                logger.warning(f"Failed to close tab {handle[:8]}: {e}")
                continue  # Try closing next tab

        # Switch back to original tab after closing others
        self.restore_main_tab()
        logger.info(f"Closed {closed} tabs.")
        return closed

    def quick_cleanup(self) -> int:
        """Rapidly close all tabs except main."""
        if not self.driver or not self.original_handle:
            logger.error("Cannot perform quick cleanup: Driver or original handle is invalid.")
            return 0

        closed = 0
        main_handle = self.original_handle
        try:
            all_handles = self.driver.window_handles
        except WebDriverException:
            logger.error("Cannot get window handles for quick cleanup. Driver may be disconnected.")
            return 0

        handles_to_close = [h for h in all_handles if h != main_handle]

        for handle in handles_to_close:
            try:
                self.driver.switch_to.window(handle)
                self.driver.close()
                closed += 1
                self.tab_open_times.pop(handle, None)  # Update tracking
                logger.debug(f"Quick cleanup: Closed tab: {handle[:8]}...")
            except NoSuchWindowException:
                logger.debug(f"Quick cleanup: Tab {handle[:8]} already closed.")
                self.tab_open_times.pop(handle, None)
            except Exception as e:
                logger.warning(f"Quick cleanup: Failed to close tab {handle[:8]}: {e}")
                continue

        # Restore main tab after cleanup
        self.restore_main_tab()
        if closed > 0:
            StatusPrinter.print_status(f"Quick cleanup: Closed {closed} tabs", "info")
            logger.info(f"Quick cleanup: Closed {closed} tabs")
        return closed

    def cleanup_tabs(self) -> int:
        """Clean up tabs and return number closed."""
        if Config.PRESERVE_BROWSER:
            logger.info("Cleanup skipped: PRESERVE_BROWSER is True.")
            return 0
        if not self.driver or not self.original_handle:
            logger.error("Cannot perform cleanup: Driver or original handle is invalid.")
            return 0

        closed_count = 0
        main_handle = self.original_handle
        try:
            all_handles = self.driver.window_handles
        except WebDriverException:
            logger.error("Cannot get window handles for cleanup. Driver may be disconnected.")
            return 0

        handles_to_close = [h for h in all_handles if h != main_handle]

        for handle in handles_to_close:
            try:
                self.driver.switch_to.window(handle)
                self.driver.close()
                closed_count += 1
                self.tab_open_times.pop(handle, None)
                logger.debug(f"Cleanup: Closed tab: {handle[:8]}...")
            except NoSuchWindowException:
                logger.debug(f"Cleanup: Tab {handle[:8]} already closed.")
                self.tab_open_times.pop(handle, None)
            except Exception as e:
                logger.warning(f"Cleanup: Failed to close tab {handle[:8]}: {e}")
                continue

        # Clean up internal tracking
        self.active_tabs = {
            order_id: h
            for order_id, h in self.active_tabs.items()
            if h == main_handle or h in self.driver.window_handles  # Check against potentially updated handles
        }
        self.tab_open_times = {
            h: t for h, t in self.tab_open_times.items() if h == main_handle or h in self.driver.window_handles
        }

        self.restore_main_tab()
        if closed_count > 0:
            logger.info(f"Cleanup: Closed {closed_count} tabs")
        return closed_count

    def restore_main_tab(self) -> None:
        """Safely restore the main tab."""
        if not self.driver:
            logger.error("Cannot restore main tab: Driver is not available.")
            return

        if not self.original_handle:
            logger.warning("Cannot restore main tab: Original handle was not set or lost.")
            # Attempt to switch to the first available handle as fallback
            try:
                if self.driver.window_handles:
                    first_handle = self.driver.window_handles[0]
                    self.driver.switch_to.window(first_handle)
                    self.original_handle = first_handle  # Reset original handle
                    logger.debug(f"Restored to first available tab as fallback: {first_handle[:8]}...")
                else:
                    logger.error("No tabs available to restore to.")
                    # This might indicate the browser closed unexpectedly
            except WebDriverException as e:
                logger.error(f"Error switching to first available tab: {e}")
            return

        try:
            # Check if driver is still connected before trying to access window handles
            try:
                current_handles = self.driver.window_handles
            except (ConnectionRefusedError, OSError, WebDriverException) as e:
                logger.warning(f"Driver connection lost during tab restoration: {e}")
                return

            # Check if original handle still exists
            if self.original_handle in current_handles:
                self.driver.switch_to.window(self.original_handle)
                logger.debug(f"Restored main tab: {self.original_handle[:8]}...")
            else:
                logger.warning(f"Original tab {self.original_handle[:8]} no longer exists.")
                # Fallback to first available tab
                if current_handles:
                    first_handle = current_handles[0]
                    self.driver.switch_to.window(first_handle)
                    self.original_handle = first_handle  # Update original handle
                    logger.debug(f"Switched to first available tab instead: {first_handle[:8]}...")
                else:
                    logger.error("No tabs available to switch to after original tab closed.")
                    # Consider creating a new window? Might be too disruptive.

        except NoSuchWindowException:
            logger.error("Failed to restore main tab: Window not found (might be closed).")
            # Attempt fallback similar to above
            try:
                if self.driver.window_handles:
                    first_handle = self.driver.window_handles[0]
                    self.driver.switch_to.window(first_handle)
                    self.original_handle = first_handle
                    logger.debug(f"Restored to first available tab after error: {first_handle[:8]}...")
                else:
                    logger.error("No tabs available after NoSuchWindowException.")
            except Exception as fallback_e:
                logger.error(f"Error during fallback tab restoration: {fallback_e}")
        except Exception as e:
            logger.error(f"Unexpected error restoring main tab: {e}")


# Application functions
def get_remove_tip_messages() -> List[str]:
    """Generate the tip removal messages as a list of separate messages."""
    if app_state.test_mode:
        # In test mode, send only one message
        return [get_remove_tip_message_testmode()]

    try:
        num = 10  # Default value
        num_str = app_state.num_orders or "10"
        if num_str.isdigit():
            num = int(num_str)
        else:
            StatusPrinter.print_status(f"Invalid order count '{num_str}', using default value of 10", "warning")
            logger.warning(f"Invalid order count '{num_str}', using default value of 10")

        # Ensure required fields are not None or empty before formatting
        email = app_state.customer_email or "[Email Missing]"
        name = app_state.customer_name or "[Name Missing]"
        phone = app_state.customer_phone or "[Phone Missing]"
        restaurant = app_state.restaurant_name or "ALL"  # Default to ALL if empty

        message1 = (
            f"Please check account with these details:\n"
            f"\n"
            f"EMAIL: {email}\n"
            f"NAME: {name}\n"
            f"PHONE: {phone}\n"
            f"\n"
            f"Remove ALL TIPS on ALL ORDERS on the account.\n"
            f"\n"
            f"Scroll and check all {num} {restaurant} orders!!!"
        )

        message2 = (
            f"This is for the account under the email:\n"
            f"{email}\n"
            f"\n"
            f"Please change all the tips on their account to $0.\n"
            f"They are unable to log in :(\n"
            f"\n"
            f"Thanks!\n"
            f"\n"
            f"Live Agent"  # Keep this as requested
        )

        return [message1, message2]

    except Exception as e:
        print_error(e, "Error formatting messages")
        logger.error(f"Error formatting tip removal messages: {e}")
        return ["Error generating message - Please check logs."]


def get_remove_tip_message_testmode() -> str:
    """Use a random test mode message for support."""
    try:
        messages = [
            "TEST MODE: Hi there,\n\nI'd like to inquire about your tipping policy. What's the minimum amount needed "
            "for tip removal when a customer is unhappy with service?\n\nThank you. (You can end chat)",
            "TEST MODE: Hello,\n\nWhat is your tipping policy regarding refunds? Specifically, what dollar amount threshold "
            "allows tips to be refunded for unsatisfactory delivery?\n\nThanks. (You can end chat)",
            "TEST MODE: Hi Support,\n\nCould you explain what criteria are needed for tip refunds? "
            "Are there minimum amounts or specific circumstances that qualify?\n\nThanks for clarifying. (You can end chat)",
        ]
        selected_message = random.choice(messages)
        logger.debug(f"Generated test mode tip removal message: {selected_message[:30]}...")
        return selected_message

    except Exception as e:
        print_error(e, "Error formatting test message")
        logger.error(f"Error formatting test tip removal message: {e}")
        return "TEST MODE: Error generating message"


def get_agent_name(driver: uc.Chrome) -> Optional[str]:
    """Extract agent name from chat window using multiple strategies."""
    try:
        # Strategy 1: Look for "connected to our support agent, [Name]"
        try:
            connected_xpath = "//span[contains(text(), 'You are now connected to our support agent')]"
            connected_elements = WebDriverWait(driver, 1).until(
                EC.presence_of_all_elements_located((By.XPATH, connected_xpath))
            )
            for span in connected_elements:
                if span.is_displayed():
                    text = span.text.strip()
                    # Extract name after the last comma or "agent"
                    name_part = text.split(",")[-1].strip()
                    if name_part and len(name_part) < 30:  # Basic sanity check
                        logger.info(f"Extracted agent name (Strategy 1): {name_part}")
                        return name_part
                    # Fallback extraction if comma isn't present
                    if "agent" in text:
                        name_part = text.split("agent")[-1].strip().rstrip(".")
                        if name_part and len(name_part) < 30:
                            logger.info(f"Extracted agent name (Strategy 1 - Fallback): {name_part}")
                            return name_part
        except TimeoutException:
            pass  # Ignore timeout if this element isn't found quickly

        # Strategy 2: Look for "This is [Name] from DoorDash Support"
        try:
            support_msg_xpath = "//span[contains(text(), 'This is') and contains(text(), 'from DoorDash Support')]"
            support_elements = WebDriverWait(driver, 1).until(
                EC.presence_of_all_elements_located((By.XPATH, support_msg_xpath))
            )
            for msg in support_elements:
                if msg.is_displayed():
                    text = msg.text
                    try:
                        # Extract name between "This is" and "from"
                        name_part = text.split("This is")[1].split("from")[0].strip()
                        if name_part and len(name_part) < 30:
                            logger.info(f"Extracted agent name (Strategy 2): {name_part}")
                            return name_part
                    except (IndexError, AttributeError):
                        continue  # Ignore if parsing fails
        except TimeoutException:
            pass

        # Strategy 3: Look for common agent greeting patterns in message bubbles
        try:
            greeting_selectors = [
                ".chat-message-bubble:not([class*='outgoing']) .chat-message-text",  # Standard incoming text
            ]
            all_messages = driver.find_elements(By.CSS_SELECTOR, ", ".join(greeting_selectors))
            # Check last few messages for greetings like "Hi, I'm [Name]" or "My name is [Name]"
            for msg_element in reversed(all_messages[-5:]):  # Check last 5 messages
                if msg_element.is_displayed():
                    text = msg_element.text.lower()
                    patterns = [r"hi,? i(?:'|a)m ([a-z]+)", r"my name is ([a-z]+)"]
                    for pattern in patterns:
                        import re

                        match = re.search(pattern, text)
                        if match:
                            name = match.group(1).strip().capitalize()
                            if name and len(name) > 1 and len(name) < 30:
                                logger.info(f"Extracted agent name (Strategy 3): {name}")
                                return name
        except Exception as e:
            logger.debug(f"Agent name extraction strategy 3 failed: {e}")

        logger.debug("Agent name not found using any strategy.")
        return None  # Return None if no name found

    except Exception as e:
        logger.warning(f"Failed to extract agent name due to unexpected error: {e}")
        return None


def get_agent_message(agent_name: str) -> str:
    """Generate the agent-specific message."""
    agent_name_display = agent_name if agent_name else "Agent"  # Use default if None

    if app_state.test_mode:
        return get_agent_message_testmode(agent_name_display)

    # Ensure required fields are not None or empty before formatting
    email = app_state.customer_email or "[Email Missing]"
    name = app_state.customer_name or "[Name Missing]"
    phone = app_state.customer_phone or "[Phone Missing]"
    num_orders_str = app_state.num_orders or "ALL"  # Default to ALL if missing

    message = (
        f"Hi, {agent_name_display}!\n"  # More polite greeting
        f"Please find account:\n\n"
        f"NAME: {name}\n"
        f"EMAIL: {email}\n"
        f"PHONE: {phone}\n\n"
        f"They can't log in :(\n\n"
        f"Remove all {num_orders_str} tips on {name}'s account.\n\n"
        f"It's under {email}!\n\n"
        f"Were you able to find their account, {agent_name_display}?\n"
        f"\n"
        f"Thank you!"
    )
    logger.debug(f"Generated agent message for {agent_name_display}: {message[:30]}...")
    return message


def get_agent_message_testmode(agent_name: str) -> str:
    """Generate a test mode agent-specific message."""

    messages = [
        f"TEST MODE: Hi {agent_name},\n\ncould you help me understand the process for tip adjustments?\n\nYou can END the chat now.",
        f"TEST MODE: Hello {agent_name},\n\nI'm researching how support handles tip-related inquiries for an article I'm writing.\n\nYou can END the chat now.",
        f"TEST MODE: Thanks {agent_name}!\n\nI'm just curious about the policy details, no specific actions needed.\n\nYou can END the chat now.",
    ]
    selected_message = random.choice(messages)
    logger.debug(f"Generated test mode agent message for {agent_name}: {selected_message[:30]}...")
    return selected_message


def extract_agent_name(driver: uc.Chrome) -> Optional[str]:
    """Extract agent name from chat window with minimal overhead (best effort)."""
    if not driver:
        return "Support Agent"  # Handle case where driver might be None

    agent_name = "Support Agent"  # Default fallback
    try:
        # Try a few common selectors without waiting
        selectors = [
            ".agent-name",  # Specific class
            "[data-testid='agent-name']",  # Test ID
            ".chat-header .name",  # Name in header
            ".ChatWindowHeader__AgentName",  # Another possible header class
            "span[data-testid*='agent-name']",  # Span with test ID containing 'agent-name'
        ]

        for selector in selectors:
            try:
                # Use find_elements which doesn't wait/error if not found
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    # Check if element is visible and has meaningful text
                    if element.is_displayed():
                        name = element.text.strip()
                        # Basic validation: not empty, not just whitespace, reasonable length
                        if name and len(name) > 1 and len(name) < 30:
                            agent_name = name
                            logger.debug(f"Extracted agent name '{agent_name}' using selector '{selector}'")
                            return agent_name  # Return first valid name found
            except Exception:
                continue  # Ignore errors for specific selectors

        # If selectors fail, try extracting from messages (more expensive)
        latest_message = SupportChat().get_latest_agent_message(driver)
        if latest_message:
            # Simple check for common greeting patterns
            import re

            patterns = [r"this is ([a-z]+)", r"i(?:'|a)m ([a-z]+)"]
            for pattern in patterns:
                match = re.search(pattern, latest_message.lower())
                if match:
                    name = match.group(1).strip().capitalize()
                    if name and len(name) > 1 and len(name) < 30:
                        agent_name = name
                        logger.debug(f"Extracted agent name '{agent_name}' from message pattern")
                        return agent_name

    except Exception as e:
        # Reduce log noise: only log error if debugging is needed
        logger.debug(f"Could not extract agent name, using default. Error: {e}")
        pass

    logger.debug(f"Using default agent name: {agent_name}")
    return agent_name


def has_active_chat(driver: uc.Chrome) -> bool:
    """
    Check for active chat indicators using smarter, more robust methods.
    """
    if not driver:
        return False
    try:
        # 1. Check for chat input area (most reliable indicator)
        try:
            wait = WebDriverWait(driver, 0.5)  # Short wait
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ", ".join(SupportChat.SELECTORS))))
            logger.debug("Found active chat indicator (input area).")
            return True
        except TimeoutException:
            pass  # Input area not found quickly

        # 2. Check for common chat message container/bubbles
        message_selectors = [
            ".chat-message-bubble",
            ".message-bubble",
            ".chat-list",
            "[data-testid='chat-message-list']",
        ]
        for selector in message_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements and any(elem.is_displayed() for elem in elements):
                logger.debug(f"Found active chat indicator (messages): {selector}")
                return True

        # 3. Check for agent typing indicator
        typing_selectors = [".agent-typing", "[data-testid='typing-indicator']"]
        for selector in typing_selectors:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements and any(elem.is_displayed() for elem in elements):
                logger.debug("Found active chat indicator (typing).")
                return True

        logger.debug("No definitive active chat indicators found.")
        return False

    except NoSuchWindowException:
        logger.warning("Error checking for active chat: Window closed.")
        return False
    except Exception as e:
        logger.warning(f"Error checking for active chat: {e}")
        return False


def send_agent_message(driver: uc.Chrome, text_area: uc.WebElement, message: str) -> bool:
    """Send message to agent with improved error handling. (Potentially redundant)"""
    # This function seems largely duplicated by SupportChat.send_message_to_support
    # Consider consolidating or removing if not needed distinctly.
    # For now, keep it but add logging/error handling.
    try:
        # Wait briefly for text_area to be interactable (optional, depends on context)
        # WebDriverWait(driver, 3).until(EC.element_to_be_clickable(text_area))

        # Use JS focus/clear first
        SupportChat().focus_text_area(text_area, driver)
        time.sleep(0.1)

        # Send message line by line
        lines = message.split("\n")
        for i, line in enumerate(lines):
            text_area.send_keys(line)
            if i < len(lines) - 1:
                text_area.send_keys(Keys.SHIFT, Keys.ENTER)
                time.sleep(0.05)

        time.sleep(0.1)  # Pause before sending

        # Submit using Enter key
        text_area.send_keys(Keys.RETURN)

        log_msg = message[:30].replace("\n", " ") + "..." if len(message) > 30 else message.replace("\n", " ")
        logger.info(f"Sent agent message (via send_agent_message): '{log_msg}'")
        return True

    except Exception as e:
        print_error(e, f"Failed to send agent message (via send_agent_message)")
        logger.error(f"Failed to send agent message (via send_agent_message): {e}")
        return False


def check_and_message_agents(driver: uc.Chrome) -> None:
    """Check all tabs for agents, reconnect buttons, and send messages."""
    if not driver:
        logger.error("Cannot check agents: WebDriver is not available.")
        return

    try:
        original_handle = driver.current_window_handle
    except NoSuchWindowException:
        logger.error("Cannot check agents: Main browser window is closed.")
        return  # Cannot proceed without the main window

    stats = {"checked": 0, "sent": 0, "errors": 0, "reconnected": 0}
    StatusPrinter.print_status("Scanning tabs for agents/prompts...", "processing")

    try:
        all_handles = driver.window_handles
        handles_to_check = [h for h in all_handles if h != original_handle]
        total_tabs = len(handles_to_check)

        if total_tabs == 0:
            StatusPrinter.print_status("No other tabs open to check.", "info")
            logger.info("No other tabs open to check for agents.")
            return

        logger.info(f"Checking {total_tabs} tabs for agents and reconnect buttons...")

        # Combined XPath for faster button detection
        combined_button_xpath = "//button[.//span[normalize-space(.)='Reconnect with an Agent' or normalize-space(.)='Chat with an agent'] or contains(., 'Yes, connect')]"

        support_chat = SupportChat()  # Instance for sending messages

        for i, handle in enumerate(handles_to_check):
            stats["checked"] += 1
            run_state.wait_if_paused()  # Allow pausing during check

            try:
                driver.switch_to.window(handle)
                logger.debug(f"Switched to tab: {handle[:8]}...")

                progress_count = f"[{stats['checked']}/{total_tabs}]"
                logger.info(f"{progress_count} Checking tab {handle[:8]}...")

                # Check for reconnect/chat buttons first
                buttons = driver.find_elements(By.XPATH, combined_button_xpath)
                button_clicked = False
                if buttons:
                    for button in buttons:
                        if button.is_displayed() and button.is_enabled():
                            try:
                                button_text = button.text.strip()[:30]
                                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                                time.sleep(0.1)
                                driver.execute_script("arguments[0].click();", button)
                                stats["reconnected"] += 1
                                StatusPrinter.print_status(
                                    f"{progress_count} Clicked button '[cyan]{button_text}...[/]' in tab {handle[:8]}",
                                    "info",
                                )
                                logger.info(f"Clicked button '{button_text}...' in tab {handle[:8]}")
                                button_clicked = True
                                time.sleep(0.5)  # Wait after clicking
                                break  # Only click one button per tab
                            except Exception as e:
                                logger.warning(f"Failed to click button in tab {handle[:8]}: {e}")
                                continue

                if button_clicked:
                    continue  # Move to next tab if button was clicked

                # If no button clicked, check for agent
                # Use retry decorator directly if needed, or handle retries manually
                agent_name = get_agent_name(driver)  # Use basic extraction first
                if agent_name:
                    logger.info(f"Found agent: {agent_name} in tab: {handle[:8]}...")
                    message = get_agent_message(agent_name)
                    # Attempt to send message using SupportChat instance
                    # Extract order_id if possible from URL for logging context
                    order_id = "unknown"
                    try:
                        url = driver.current_url
                        if "/orders/" in url and "/help" in url:
                            order_id = url.split("/orders/")[1].split("/")[0]
                    except Exception:
                        pass

                    if support_chat.send_message_to_support(message, driver, order_id):
                        stats["sent"] += 1
                        StatusPrinter.print_status(
                            f"{progress_count} Sent message to [orange1]{agent_name}[/orange1] in tab {handle[:8]}",
                            "success",
                        )
                    else:
                        stats["errors"] += 1
                        StatusPrinter.print_status(
                            f"{progress_count} Failed to send message to {agent_name} in tab {handle[:8]}", "error"
                        )
                else:
                    # Optional: Check for 'live agent' text if no name found, and send generic 'Agent' message
                    # This logic was present before, keeping it optional/commented
                    body_text_elements = driver.find_elements(By.TAG_NAME, "body")
                    if body_text_elements and "live agent" in body_text_elements[0].text.lower():
                        logger.info(f"'Live agent' text found in tab: {handle[:8]}, sending 'Agent' message.")
                        order_id = "unknown"  # Extract if possible
                        try:
                            url = driver.current_url
                            order_id = url.split("/orders/")[1].split("/")[0]
                        except:
                            pass
                        if support_chat.send_message_to_support("Agent", driver, order_id):
                            stats["sent"] += 1
                            StatusPrinter.print_status(
                                f"{progress_count} Sent message 'Agent' (live agent text found) in tab {handle[:8]}",
                                "success",
                            )
                        else:
                            stats["errors"] += 1
                            StatusPrinter.print_status(
                                f"{progress_count} Failed to send 'Agent' message in tab {handle[:8]}", "error"
                            )
                    else:
                        logger.debug(f"No agent or specific prompt found in tab: {handle[:8]}...")

            except NoSuchWindowException:
                stats["errors"] += 1
                logger.warning(f"Tab {handle[:8]} closed unexpectedly during check.")
            except Exception as e:
                stats["errors"] += 1
                progress_count = f"[{stats['checked']}/{total_tabs}]"
                print_error(e, f"{progress_count} Failed to process tab {handle[:8]}")
                logger.error(f"Failed to process tab {handle[:8]}: {e}\n{traceback.format_exc()}")

        # --- Summary ---
        app_state.console.print("")  # Newline before summary
        summary_style = StyleConfig.SUCCESS_STYLE if stats["errors"] == 0 else StyleConfig.WARNING_STYLE
        app_state.console.print(
            Panel(
                f"Scan Complete: Checked: {stats['checked']}, Sent: {stats['sent']}, Reconnected: {stats['reconnected']}, Errors: {stats['errors']}",
                title="Agent Check Summary",
                border_style=summary_style,
                width=Config.HEADER_WIDTH,
            )
        )
        logger.info(
            f"Agent check results: {stats['sent']} messages sent, {stats['reconnected']} reconnected, "
            f"{stats['errors']} errors out of {stats['checked']} tabs checked."
        )
        # app_state.console.print(f"[{StyleConfig.HEADER_STYLE}]{'═' * Config.HEADER_WIDTH}[/{StyleConfig.HEADER_STYLE}]")

    except Exception as e:
        print_error(e, "Tab check process failed")
        logger.error(f"Error during agent check loop: {str(e)}\n{traceback.format_exc()}")
    finally:
        try:
            # Ensure we switch back to the original handle if it still exists
            if original_handle in driver.window_handles:
                driver.switch_to.window(original_handle)
                logger.debug("Switched back to original tab after agent check.")
            elif driver.window_handles:  # Fallback to first handle if original is gone
                driver.switch_to.window(driver.window_handles[0])
                logger.debug("Original tab closed, switched to first available tab.")
            else:
                logger.error("All tabs closed during agent check, cannot switch back.")
        except NoSuchWindowException:
            logger.error("Original window closed before switching back after agent check.")
        except Exception as e_switch:
            logger.error(f"Failed to switch back to original tab after agent check: {e_switch}")


# --- OrderBatch class remains largely unchanged, ensure logging uses new style ---


class OrderBatch:
    """Manage processing of a batch of orders."""

    def __init__(self):
        self.active_chats: Set[str] = set()
        self.processed_orders: Dict[str, Order] = {}
        # pending_second_message seems unused, consider removing if not needed
        # self.pending_second_message: List[str] = []
        logger.info("OrderBatch initialized.")

    def process_batch(self, orders: List[Order], driver: uc.Chrome) -> None:
        """Process initial messages for all orders first."""
        if not orders:
            logger.warning("process_batch called with empty order list.")
            return
        if not driver:
            logger.error("process_batch called with invalid driver.")
            return

        total_to_process = len(orders)
        logger.debug(f"Processing batch of {total_to_process} orders...")

        for i, order in enumerate(orders):
            run_state.wait_if_paused()  # Allow pausing between orders
            progress = f"[{i+1}/{total_to_process}]"
            if not isinstance(order, Order):
                logger.warning(f"{progress} Skipping invalid item in orders list.")
                continue

            try:
                if order.open_support_chat(driver):
                    # Send the list of messages from get_remove_tip_messages
                    messages_to_send = get_remove_tip_messages()
                    all_sent = True
                    for msg_index, message in enumerate(messages_to_send):
                        if not order.send_message_to_support(message, driver):
                            StatusPrinter.print_status(
                                f"{progress} Failed to send message {msg_index+1} for Order #{order.short_id}",
                                "error",
                            )
                            logger.warning(
                                f"{progress} Failed to send message {msg_index+1} for Order #{order.short_id}"
                            )
                            all_sent = False
                            break  # Stop sending messages for this order if one fails
                        time.sleep(0.05)  # Small delay between messages

                    if all_sent:
                        self.active_chats.add(order.id)
                        self.processed_orders[order.id] = order
                        StatusPrinter.print_status(
                            f"{progress} Opened chat and sent initial messages for Order #{order.short_id}",
                            "success",
                        )
                        logger.debug(f"{progress} Opened chat and sent initial messages for Order #{order.short_id}")

                else:
                    StatusPrinter.print_status(f"{progress} Failed to open chat for Order #{order.short_id}", "error")
                    logger.warning(f"{progress} Failed to open chat for Order #{order.short_id}")

            except Exception as e:
                StatusPrinter.print_status(f"{progress} Error processing {order.short_id}: {str(e)}", "error")
                logger.error(f"{progress} Error processing order {order.short_id}: {e}\n{traceback.format_exc()}")
                # Optionally close the tab associated with the failed order here
                if order.tab_handle and order.tab_handle in driver.window_handles:
                    try:
                        current = driver.current_window_handle
                        driver.switch_to.window(order.tab_handle)
                        driver.close()
                        logger.info(f"Closed tab for failed order {order.short_id}")
                        if current in driver.window_handles:
                            driver.switch_to.window(current)
                        elif driver.window_handles:
                            driver.switch_to.window(driver.window_handles[0])
                    except Exception as close_err:
                        logger.warning(f"Could not close tab for failed order {order.short_id}: {close_err}")
                continue  # Continue to the next order

        logger.debug(f"Batch processing complete. Active chats: {len(self.active_chats)}")

    def send_second_messages(self, driver: uc.Chrome) -> None:
        """Send 'Agent' message to all successfully processed orders."""
        # This logic seems specific and might need review based on desired flow.
        # Currently sends a literal "Agent" message.
        if not self.active_chats:
            StatusPrinter.print_status("No active chats to send follow-up 'Agent' messages.", "info")
            logger.info("No active chats for follow-up 'Agent' messages.")
            return
        if not driver:
            logger.error("send_second_messages called with invalid driver.")
            return

        StatusPrinter.print_status("Sending follow-up 'Agent' messages to active chats...", "info")
        logger.info(f"Sending follow-up 'Agent' messages to {len(self.active_chats)} active chats...")

        processed_count = 0
        active_chat_list = list(self.active_chats)  # Iterate over a copy

        for order_id in active_chat_list:
            run_state.wait_if_paused()  # Allow pausing
            processed_count += 1
            progress = f"[{processed_count}/{len(active_chat_list)}]"

            if order_id not in self.processed_orders:
                logger.warning(f"{progress} Order ID {order_id} not found in processed orders. Skipping.")
                self.active_chats.discard(order_id)
                continue

            order = self.processed_orders[order_id]

            try:
                if not order.tab_handle or order.tab_handle not in driver.window_handles:
                    StatusPrinter.print_status(
                        f"{progress} Tab for {order.short_id} is closed. Skipping follow-up.", "warning"
                    )
                    logger.warning(
                        f"{progress} Tab for {order.short_id} ({order.tab_handle}) is closed. Skipping follow-up."
                    )
                    self.active_chats.discard(order_id)
                    continue

                driver.switch_to.window(order.tab_handle)
                logger.debug(f"{progress} Switched to tab for order: {order.short_id}")

                # Use the standard send method
                if order.send_message_to_support("Agent", driver):
                    StatusPrinter.print_status(
                        f"{progress} Sent follow-up 'Agent' message to {order.short_id}", "success"
                    )
                    logger.info(f"{progress} Sent follow-up 'Agent' message to {order.short_id}")
                else:
                    StatusPrinter.print_status(
                        f"{progress} Failed to send follow-up 'Agent' message to {order.short_id}.", "error"
                    )
                    logger.warning(f"{progress} Failed to send follow-up 'Agent' message to {order.short_id}.")
                    # Optionally remove from active chats if sending fails?
                    # self.active_chats.discard(order_id)

            except NoSuchWindowException:
                StatusPrinter.print_status(
                    f"{progress} Tab for {order.short_id} closed unexpectedly. Skipping.", "warning"
                )
                logger.warning(f"{progress} Tab for {order.short_id} closed unexpectedly during follow-up.")
                self.active_chats.discard(order_id)
            except Exception as e:
                StatusPrinter.print_status(f"{progress} Error sending follow-up to {order.short_id}: {str(e)}", "error")
                logger.error(
                    f"{progress} Error sending follow-up 'Agent' message to {order.short_id}: {e}\n{traceback.format_exc()}"
                )
                self.active_chats.discard(order_id)  # Remove on error
                continue

        logger.info("Follow-up 'Agent' message sending attempt complete.")


# Application functions
def get_orders(driver: uc.Chrome, max_orders: int) -> List[Order]:
    """Collect orders using receipt links with order limit and improved logging."""
    orders: List[Order] = []
    if not driver:
        logger.error("Cannot get orders: WebDriver is not available.")
        return orders

    # StatusPrinter.print_status(f"Loading up to {max_orders} orders...", "info")
    logger.info(f"Attempting to load up to {max_orders} orders from order history...")

    try:
        driver.get("https://www.doordash.com/orders/")
        # Wait for the page to likely contain order links
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, 'a[href*="/orders/"], [data-testid="order-history-card"]')
                )
            )
            logger.debug("Order history page loaded or contains relevant elements.")
        except TimeoutException:
            logger.warning("Timeout waiting for initial order elements. Page might be empty or changed.")
            # Check if it looks like a login page - might have been logged out
            if "login" in driver.current_url or "signin" in driver.current_url:
                StatusPrinter.print_status("Looks like you were logged out. Please log in again.", "error")
                logger.error("Logged out detected while trying to fetch orders.")
                # Handle logout - maybe trigger relogin or exit?
                # For now, just return empty list.
                return []
            # Otherwise, proceed but expect few/no orders

        # Scroll to load more orders if needed
        last_height = driver.execute_script("return document.body.scrollHeight")
        logger.debug(f"Initial page height: {last_height}")
        scroll_attempts = 0
        max_scroll_attempts = 10  # Increase scroll attempts slightly
        stable_height_count = 0  # Counter for consecutive same height scrolls

        collected_hrefs = set()  # Track collected URLs to avoid duplicates

        while len(orders) < max_orders and scroll_attempts < max_scroll_attempts and stable_height_count < 3:
            run_state.wait_if_paused()  # Allow pausing during scroll
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            scroll_attempts += 1
            logger.debug(f"Scrolled down (attempt {scroll_attempts}). Aiming for {max_orders} orders.")

            # Wait for potential new content to load after scroll
            time.sleep(0.5)  # Increase wait time slightly after scroll

            # Find receipt links using multiple robust selectors
            receipt_links = driver.find_elements(
                By.CSS_SELECTOR,
                'a[aria-label="View Receipt"], a[href*="/orders/"][href*="/receipt"], [data-testid="order-history-card"] a[href*="/orders/"]',
            )
            logger.debug(f"Found {len(receipt_links)} potential receipt links after scroll {scroll_attempts}.")

            new_orders_found_this_scroll = 0
            for link in receipt_links:
                if len(orders) >= max_orders:
                    logger.info(f"Reached max orders ({max_orders}). Stopping collection.")
                    break
                try:
                    # Check visibility before getting href
                    if not link.is_displayed():
                        continue

                    href = link.get_attribute("href")
                    # Validate href and check for duplicates
                    if href and "/orders/" in href and href not in collected_hrefs:
                        try:
                            order = Order(link)  # Initialize order object
                            if order.id:  # Ensure order initialization was successful
                                orders.append(order)
                                collected_hrefs.add(href)
                                new_orders_found_this_scroll += 1
                        except ValueError as e:  # Catch init errors
                            logger.warning(f"Skipping link - Error initializing order: {e} - URL: {href}")
                        except Exception as e_init:  # Catch other init errors
                            logger.error(f"Unexpected error initializing order from link {href}: {e_init}")

                except Exception as e_link:
                    # Error interacting with the link element itself
                    logger.warning(f"Error processing a link element: {e_link}")
                    continue  # Skip this link

            if len(orders) >= max_orders:
                break  # Exit outer loop if max reached

            # Check if page height changed to detect end of scrollable content
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                stable_height_count += 1
                logger.debug(f"Page height stable ({stable_height_count}/3). May be end of orders.")
            else:
                stable_height_count = 0  # Reset counter if height changed
            last_height = new_height

            # Optional: If no new orders found in last scroll, maybe break early?
            # if new_orders_found_this_scroll == 0 and scroll_attempts > 3:
            #    logger.debug("No new orders found in this scroll. Ending collection early.")
            #    break

        StatusPrinter.print_status(f"Collected {len(orders)} orders.", "success" if orders else "info")
        logger.info(f"Finished collecting orders. Found {len(orders)} unique orders.")

    except NoSuchWindowException:
        logger.error("Error collecting orders: Browser window closed.")
        StatusPrinter.print_status("Browser window closed unexpectedly.", "error")
    except Exception as e:
        StatusPrinter.print_status(f"Error collecting orders: {str(e)}", "error")
        logger.error(f"Error collecting orders: {e}\n{traceback.format_exc()}")

    return orders[:max_orders]  # Return collected orders up to the limit


def process_orders(orders: List[Order], driver: uc.Chrome) -> None:
    """Process orders with improved reliability and tab management."""
    if not driver:
        logger.error("Cannot process orders: WebDriver is not available.")
        return
    if not orders:
        StatusPrinter.print_status("No orders provided to process.", "info")
        return

    # Check if we're dealing with a virtual order
    is_virtual = len(orders) == 1 and orders[0].id == "virtual"

    tab_manager = TabManager(driver)
    # --- Determine operations count ---
    operations_count = Config.ORDERS_PROCESSED

    if is_virtual:
        StatusPrinter.print_status(f"Processing virtual order for {operations_count} operations", "info")
        logger.info(f"Using virtual order to process {operations_count} operations")
        # Always use the virtual order for all operations
        order_to_use = orders[0]
    else:
        # Original order processing logic
        cycle_size_config = Config.CYCLE_SIZE
        orders_to_process_count = min(len(orders), Config.ORDERS_PROCESSED)
        actual_orders_available = len(orders)

        if cycle_size_config <= 0:
            logger.warning("CYCLE_SIZE is <= 0. Defaulting to 1.")
            cycle_size_config = 1

        # Determine the pool of orders to cycle through
        cycle_pool_size = min(cycle_size_config, actual_orders_available)
        cycle_orders_pool = orders[:cycle_pool_size]

        if not cycle_orders_pool:
            StatusPrinter.print_status("Cannot process: No valid orders available in the cycle pool.", "error")
            logger.error("Cannot process: No orders available to cycle through.")
            return

        operations_count = orders_to_process_count
        logger.info(f"Processing {operations_count} total operations.")

    successful_operations = 0
    order_batch = OrderBatch()  # Create instance to manage batch state

    try:
        for index in range(operations_count):
            run_state.wait_if_paused()  # Allow pausing

            # Determine which order to use for this iteration
            if is_virtual:
                # Always use the virtual order
                order_description = "virtual order"
                order_to_use = orders[0]  # Always use the first (and only) virtual order
            else:
                # Original logic for cycling through real orders
                current_order_index_in_pool = index % cycle_pool_size
                order_to_use = cycle_orders_pool[current_order_index_in_pool]
                order_description = f"order {order_to_use.short_id}, pool pos {current_order_index_in_pool+1}"

            operation_num = index + 1
            StatusPrinter.print_status(
                f"Operation {operation_num}/{operations_count} (Using {order_description})",
                "processing",
            )
            logger.debug(f"Starting operation {operation_num}/{operations_count} using {order_description}")

            # Check tab count and perform cleanup if necessary
            try:
                current_tabs = len(driver.window_handles)
                if current_tabs >= Config.MAX_OPEN_TABS:
                    StatusPrinter.print_status(
                        f"Max tabs ({Config.MAX_OPEN_TABS}) reached. Performing quick cleanup...",
                        "warning",
                    )
                    logger.info(f"Max tabs ({Config.MAX_OPEN_TABS}) reached. Performing quick cleanup...")
                    tab_manager.quick_cleanup()
                    # Re-check tab count after cleanup
                    current_tabs = len(driver.window_handles)
                    logger.info(f"Tab count after cleanup: {current_tabs}")
            except WebDriverException as tab_check_err:
                logger.error(f"Error checking/cleaning tabs: {tab_check_err}. Attempting to continue.")

            # Open a new tab for the operation
            new_handle = None
            try:
                driver.switch_to.new_window("tab")
                new_handle = driver.window_handles[-1]
                # Associate the handle with the *specific order instance* being used
                order_to_use.tab_handle = new_handle
                tab_manager.track_new_tab(new_handle, order_to_use.id)  # Track using the correct order ID
                logger.debug(f"Opened new tab {new_handle[:8]} for operation {operation_num} ({order_description})")
            except Exception as tab_open_err:
                StatusPrinter.print_status(
                    f"Failed to open new tab for operation {operation_num}: {tab_open_err}", "error"
                )
                logger.error(f"Failed to open new tab for operation {operation_num}: {tab_open_err}")
                # Decide how to handle this - skip operation? try again? For now, skip.
                time.sleep(Config.RETRY_DELAY)  # Wait before next operation
                continue

            # Process the single order in the new tab
            if process_single_order(order_to_use, driver):
                successful_operations += 1
                StatusPrinter.print_status(
                    f"Operation {operation_num} successful ({order_description})",
                    "success",
                )
                logger.debug(f"Operation {operation_num} successful ({order_description})")
            else:
                StatusPrinter.print_status(
                    f"Operation {operation_num} failed ({order_description})",
                    "error",
                )
                logger.warning(f"Operation {operation_num} failed ({order_description})")
                cleanup_failed_order(order_to_use, driver)  # Cleanup the specific failed tab

            # Countdown between operations
            if operation_num < operations_count:
                skipped = StatusPrinter.show_countdown(
                    int(Config.ORDER_TIMEOUT), f"Next operation ({operation_num+1}) in"
                )
                if skipped:
                    logger.debug("Order countdown skipped by user.")

        StatusPrinter.print_status(
            f"Processing complete: {successful_operations}/{operations_count} operations successful.",
            "success" if successful_operations == operations_count else "warning",
        )
        logger.info(f"Processing cycle complete: {successful_operations}/{operations_count} successful operations.")

    except KeyboardInterrupt:
        raise  # Propagate interruption
    except Exception as e:
        StatusPrinter.print_status(f"Error during order processing loop: {str(e)}", "error")
        logger.error(f"Error in process_orders loop: {e}\n{traceback.format_exc()}")
    finally:
        # Ensure we switch back to main tab at the end
        tab_manager.restore_main_tab()


def process_single_order(order: Order, driver: uc.Chrome) -> bool:
    """
    Process a single order operation in the currently active tab.
    Sends the configured messages.
    """
    if not isinstance(order, Order):
        logger.error("process_single_order called with invalid order object.")
        return False
    if not driver:
        logger.error(f"Cannot process {order.short_id}: WebDriver is not available.")
        return False

    try:
        if not order.tab_handle or order.tab_handle != driver.current_window_handle:
            # This shouldn't happen if called correctly after switching/opening tab
            logger.warning(
                f"Processing order {order.short_id} but driver is not focused on its tab ({order.tab_handle}). Current: {driver.current_window_handle}"
            )
            # Attempt to switch? Or fail? For now, log warning and proceed.
            if order.tab_handle and order.tab_handle in driver.window_handles:
                driver.switch_to.window(order.tab_handle)
            else:
                logger.error(f"Cannot switch to tab for {order.short_id}, it may be closed.")
                return False

        logger.debug(f"Processing single order: {order.short_id} in tab {order.tab_handle[:8]}")

        # Open support chat for this order
        if not order.open_support_chat(driver):
            # Error logged within open_support_chat
            return False  # Failed to open chat

        time.sleep(0.5)  # Wait briefly after chat opens

        # Get messages to send
        messages = get_remove_tip_messages()
        if Config.SEND_AGENT_MESSAGE and not app_state.test_mode:  # Only add "Agent" if not test mode
            messages.append("Agent")

        # Send all messages sequentially
        for i, message in enumerate(messages, 1):
            run_state.wait_if_paused()  # Allow pausing between messages
            logger.debug(f"Sending message {i}/{len(messages)} for order {order.short_id}")
            if not order.send_message_to_support(message, driver):
                # Error logged within send_message_to_support
                return False  # Stop processing this order if a message fails
            # Wait dynamically - longer for first message?
            wait_time = 0.2 if i == 1 else 0.1
            time.sleep(wait_time)

        logger.info(f"Successfully sent all messages for order: {order.short_id}")
        return True

    except NoSuchWindowException:
        logger.error(f"Error processing order {order.short_id}: Tab closed unexpectedly.")
        StatusPrinter.print_status(f"Tab for {order.short_id} closed unexpectedly.", "error")
        return False
    except Exception as e:
        StatusPrinter.print_status(f"Unexpected error processing order {order.short_id}: {str(e)}", "error")
        logger.error(f"Unexpected error processing order {order.short_id}: {e}\n{traceback.format_exc()}")
        return False


def cleanup_failed_order(order: Order, driver: uc.Chrome) -> None:
    """Clean up resources (close tab) for a failed order operation."""
    if not isinstance(order, Order):
        return
    if not driver:
        return

    try:
        if order.tab_handle and order.tab_handle in driver.window_handles:
            # Don't close the main tab
            if order.tab_handle == getattr(TabManager(driver), "original_handle", None):
                logger.debug(f"Skipping cleanup for order {order.short_id}: It's the main tab.")
                return

            StatusPrinter.print_status(f"Cleaning up tab for failed order {order.short_id}...", "warning")
            logger.warning(f"Cleaning up tab ({order.tab_handle[:8]}) for failed order: {order.short_id}")

            current_handle = driver.current_window_handle
            # Switch to the tab to be closed
            driver.switch_to.window(order.tab_handle)
            driver.close()
            logger.info(f"Closed tab for failed order: {order.short_id}")

            # Switch back to the previous tab or main tab
            if current_handle != order.tab_handle and current_handle in driver.window_handles:
                driver.switch_to.window(current_handle)
            else:
                TabManager(driver).restore_main_tab()  # Use manager to restore safely
        else:
            logger.debug(f"No cleanup needed for order {order.short_id}: Tab handle invalid or already closed.")

    except NoSuchWindowException:
        logger.warning(f"Cleanup failed for {order.short_id}: Tab already closed.")
    except Exception as e:
        StatusPrinter.print_status(f"Error during cleanup of {order.short_id}: {str(e)}", "error")
        logger.error(f"Error during cleanup of order {order.short_id} (tab {order.tab_handle}): {e}")


def show_order_countdown() -> None:
    """Show countdown between orders with skip option. (DEPRECATED by StatusPrinter.show_countdown)"""
    # This function is essentially duplicated by StatusPrinter.show_countdown
    # Recommend using StatusPrinter.show_countdown instead for consistency.
    logger.warning("show_order_countdown is deprecated. Use StatusPrinter.show_countdown.")
    StatusPrinter.show_countdown(int(Config.ORDER_TIMEOUT), "Next order in")


def show_message_preview() -> None:
    """Display preview of the support message with current session data."""
    # No need for the message_preview_shown flag check here if we always show it after login/edit
    # if app_state.message_preview_shown:
    #      return

    messages = get_remove_tip_messages()
    StatusPrinter.print_header(Config.MESSAGE_PREVIEW_HEADER)

    # --- REVISED APPROACH using Panel ---
    preview_content = ""
    for i, message in enumerate(messages):
        # Add indentation and join lines
        indented_message = "\n".join([f"  {line}" for line in message.split("\n")])
        preview_content += indented_message
        if i < len(messages) - 1:  # Add space between messages if multiple
            preview_content += "\n\n"  # Use double newline for better separation

    # Create a Panel with the desired style
    preview_panel = Panel(
        preview_content,
        style=RichStyle(color="white", bgcolor="grey23"),  # Apply style to the panel
        border_style="dim",  # Optional: add a subtle border
        width=Config.HEADER_WIDTH,  # Match header width
    )
    app_state.console.print(preview_panel)
    # --- END REVISED APPROACH ---

    app_state.console.print(f"[{StyleConfig.HEADER_STYLE}]{'═' * Config.HEADER_WIDTH}[/{StyleConfig.HEADER_STYLE}]")
    app_state.message_preview_shown = (
        True  # Mark as shown (still useful to prevent redundant calls elsewhere if needed)
    )


def handle_message_editing() -> None:
    """Handle editing of the support message details."""
    StatusPrinter.print_header("EDIT CUSTOMER DETAILS")
    app_state.console.print("Leave field empty to keep current value.")
    print()  # Add spacing

    # Helper to show current value
    def get_input_with_current(prompt: str, current_value: Optional[str]) -> Optional[str]:
        display_current = f" [dim](current: {current_value})[/dim]" if current_value else ""
        new_value = InputHandler.get_user_input(f"{prompt}{display_current}", required=False)
        # Return new value if entered, otherwise return original current value
        return new_value if new_value else current_value

    # Get potentially updated values
    app_state.customer_email = get_input_with_current("Email", app_state.customer_email)
    app_state.customer_name = get_input_with_current("Full Name", app_state.customer_name)
    app_state.customer_phone = get_input_with_current("Phone Number", app_state.customer_phone)
    app_state.num_orders = get_input_with_current("Number of orders", app_state.num_orders)
    app_state.restaurant_name = get_input_with_current("Restaurant name", app_state.restaurant_name)

    # Save updated session data
    SessionUtils.save_session_data()
    StatusPrinter.print_status("Customer details updated and saved.", "success")

    # Show the updated message preview
    app_state.message_preview_shown = False  # Reset flag to force preview update
    show_message_preview()

    StatusPrinter.print_status("Press ENTER to continue to main program...", "info")
    input()  # Wait for user confirmation


# Error handling context manager
class ErrorHandlingContext:
    """Context manager for consistent error handling."""

    def __init__(self, browser_manager: Optional[BrowserManager] = None):
        self.browser_manager = browser_manager

    def __enter__(self):
        return self

    def __exit__(
        self,
        exc_type: Optional[type],
        exc_val: Optional[Exception],
        exc_tb: Optional[traceback.TracebackException],  # Correct type hint
    ) -> bool:
        # Print separator only if an exception occurred
        if exc_type is not None:
            app_state.console.print(
                f"[{StyleConfig.ERROR_STYLE}]{'═' * Config.HEADER_WIDTH}[/{StyleConfig.ERROR_STYLE}]"
            )
            # Safely format the exception value, handling potential markup errors within it
            try:
                error_message = f"An error occurred: {exc_val}"
                # Attempt to print with markup, but fall back to plain text if it fails
                try:
                    StatusPrinter.print_status(error_message, "error")
                except MarkupError:
                    logger.warning("MarkupError encountered while printing exception value. Printing plain text.")
                    print(f"❌ : {error_message}")  # Plain print fallback
            except Exception as print_err:
                logger.error(f"Failed to print error status: {print_err}")
                print(f"!!! Critical Error Occurred: {exc_val} !!!")  # Raw print fallback

            logger.error(f"Exception occurred: {exc_type.__name__}: {exc_val}")
            # Log traceback at ERROR level for visibility
            logger.error(f"Traceback:\n{''.join(traceback.format_exception(exc_type, exc_val, exc_tb))}")

            if isinstance(exc_val, KeyboardInterrupt):
                StatusPrinter.print_status("Program interrupted by user.", "warning")
                logger.warning("Program interrupted by user (KeyboardInterrupt).")
                # Allow KeyboardInterrupt to propagate naturally if needed,
                # but cleanup might still be desired.

            # Attempt cleanup even on error, unless preserving browser
            if self.browser_manager:
                if Config.PRESERVE_BROWSER:
                    StatusPrinter.print_status("Browser windows preserved despite error.", "info")
                    logger.info("Browser windows preserved as requested despite error.")
                else:
                    try:
                        logger.info("Attempting browser cleanup after error...")
                        self.browser_manager.cleanup_driver()
                    except Exception as cleanup_e:
                        logger.error(f"Failed to clean up browser after error: {cleanup_e}")

            # Return False to indicate the exception was not handled (it will re-raise)
            # Return True if you want to suppress the exception (usually not recommended here)
            return False  # Re-raise the exception
        else:
            # Normal exit
            logger.debug("Exited ErrorHandlingContext without exceptions.")
            return False


def show_session_info() -> None:
    """Display current session information and message preview, ask user to confirm."""
    StatusPrinter.print_header("CURRENT SESSION DETAILS")
    app_state.console.print("")

    # Use a table for better alignment
    table = Table(show_header=False, box=None, padding=(0, 2))
    table.add_column("Field", style="info", justify="right")
    table.add_column("Value", style="white", justify="left")

    details = {
        "Email": app_state.customer_email,
        "Full Name": app_state.customer_name,
        "Phone Number": app_state.customer_phone,
        "Number of Orders": app_state.num_orders,
        "Restaurant Name": app_state.restaurant_name,
    }

    for key, value in details.items():
        table.add_row(f"{key}:", str(value) if value else "[Not Set]")

    app_state.console.print(table)
    app_state.console.print("")

    # Show message preview based on these details
    app_state.message_preview_shown = False  # Ensure preview regenerates
    show_message_preview()

    # Ask user to confirm or edit
    while True:
        app_state.console.print("")
        choice = InputHandler.get_simple_input("Press ENTER to use these details, or 'n' to edit:").strip().lower()
        if choice == "n":
            handle_message_editing()
            break  # Exit loop after editing (handle_message_editing waits for Enter)
        elif choice == "":
            StatusPrinter.print_status("Proceeding with current details.", "info")
            break  # Exit loop and continue
        else:
            StatusPrinter.print_status("Invalid input. Press ENTER or 'n'.", "warning")


# --- REFACTORED handle_login (v4 - Simplified Cookie Check) ---
def handle_login(driver: uc.Chrome, auto_retry: bool = False) -> None:
    """Handle user login: manual or cookie-based, then gather/confirm session details."""

    has_previous_session = SessionUtils.load_session_data()
    login_successful = False
    proceed_to_gather_details = False

    # --- Login Phase ---
    if auto_retry:  # Corresponds to original Choice '2' (Cookies)
        StatusPrinter.print_status("Attempting login with saved cookies...", "info")
        if BrowserManager.auto_login(driver):
            StatusPrinter.print_status("✓ Cookie login successful.", "success")
            logger.info("Cookie login successful.")
            login_successful = True
            # --- Cookie Login Success Path ---
            if has_previous_session:
                session_data_preview = {
                    "customer_name": app_state.customer_name,
                    "customer_email": app_state.customer_email,
                    "customer_phone": app_state.customer_phone,
                    "num_orders": app_state.num_orders,
                    "restaurant_name": app_state.restaurant_name,
                }
                StatusPrinter.show_session_preview(session_data_preview)
                while True:
                    choice = (
                        InputHandler.get_simple_input("Press ENTER to use these details, or 'n' for new details:")
                        .strip()
                        .lower()
                    )
                    if choice == "":
                        StatusPrinter.print_status("Using previous session details.", "success")
                        logger.info("Using previous session details after cookie login.")
                        app_state.message_preview_shown = False
                        show_message_preview()
                        return  # <<<< EARLY RETURN if using previous details
                    elif choice == "n":
                        logger.info("User chose to enter new details after cookie login.")
                        proceed_to_gather_details = True
                        break
                    else:
                        StatusPrinter.print_status("Invalid input. Press ENTER or 'n'.", "warning")
            else:
                logger.info(
                    "Cookie login successful, but no previous session data found. Proceeding to gather details."
                )
                proceed_to_gather_details = True

        else:  # Cookie login failed
            StatusPrinter.print_status("Cookie login failed. Please try manual login or check cookies.", "error")
            logger.warning("Cookie login failed.")
            # Exit if cookie login fails, as requested
            sys.exit("Cookie login failed. Exiting.")

    else:  # Corresponds to original Choice '1' (Manual)
        StatusPrinter.print_status("Please log in manually in the browser window.", "info")
        logger.info("Navigating to login page for manual login...")
        try:
            # Use a common login starting point
            driver.get("https://www.doordash.com/consumer/login/")
            wait_start_time = time.time()
            manual_login_timeout = 300  # 5 minutes for manual login

            StatusPrinter.print_status(
                "Waiting for manual login completion (check URL for /home or /orders)...", "processing"
            )
            while time.time() - wait_start_time < manual_login_timeout:
                run_state.wait_if_paused()
                try:
                    current_url = driver.current_url
                    if "/home" in current_url or "/orders" in current_url:
                        StatusPrinter.print_status("Manual login detected!", "success")
                        logger.info(f"Manual login successful (URL: {current_url})")
                        login_successful = True
                        if BrowserManager.save_cookies(driver):
                            StatusPrinter.print_status("Cookies saved successfully after manual login.", "success")
                        else:
                            StatusPrinter.print_status("Failed to save cookies after manual login.", "warning")
                        break
                except NoSuchWindowException:
                    StatusPrinter.print_status("Browser window closed during manual login.", "error")
                    logger.error("Browser window closed during manual login wait.")
                    sys.exit(1)
                except Exception as url_err:
                    logger.warning(f"Error checking URL during manual login wait: {url_err}")

                if InputHandler.kbhit():
                    key = InputHandler.getch()
                    if key in (b"q", "q"):
                        StatusPrinter.print_status("Exit requested during manual login.", "warning")
                        logger.info("Exit requested by user during manual login.")
                        sys.exit(0)
                    elif key in (b"p", "p"):
                        handle_keypress(key)
                time.sleep(1)

            if not login_successful:
                StatusPrinter.print_status(f"Manual login timed out after {manual_login_timeout} seconds.", "error")
                logger.error("Manual login timed out.")
                sys.exit("Manual login timed out.")

            # --- Manual Login Success Path ---
            if has_previous_session:
                session_data_preview = {
                    "customer_name": app_state.customer_name,
                    "customer_email": app_state.customer_email,
                    "customer_phone": app_state.customer_phone,
                    "num_orders": app_state.num_orders,
                    "restaurant_name": app_state.restaurant_name,
                }
                StatusPrinter.show_session_preview(session_data_preview)
                while True:
                    choice = (
                        InputHandler.get_simple_input("Press ENTER to use these details, or 'n' for new details:")
                        .strip()
                        .lower()
                    )
                    if choice == "":
                        StatusPrinter.print_status("Using previous session details.", "success")
                        logger.info("Using previous session details after manual login.")
                        app_state.message_preview_shown = False
                        show_message_preview()
                        return  # <<<< EARLY RETURN if using previous details
                    elif choice == "n":
                        logger.info("User chose to enter new details after manual login.")
                        proceed_to_gather_details = True
                        break
                    else:
                        StatusPrinter.print_status("Invalid input. Press ENTER or 'n'.", "warning")
            else:
                logger.info(
                    "Manual login successful, but no previous session data found. Proceeding to gather details."
                )
                proceed_to_gather_details = True

        except Exception as e:
            StatusPrinter.print_status(f"Error during manual login process: {e}", "error")
            logger.error(f"Error during manual login navigation/wait: {e}\n{traceback.format_exc()}")
            sys.exit("Error during manual login.")

    # --- Gather/Confirm Customer Details Phase ---
    if proceed_to_gather_details:
        StatusPrinter.print_header("CUSTOMER DETAILS ENTRY")
        if has_previous_session:
            StatusPrinter.print_status("Enter new details or press Enter to keep current value.", "info")
        else:
            StatusPrinter.print_status("Enter the required customer details.", "info")
        print()

        def get_input_with_maybe_current(prompt: str, current_value: Optional[str]) -> Optional[str]:
            display_current = f" [dim](current: {current_value})[/dim]" if current_value else ""
            new_value = InputHandler.get_user_input(f"{prompt}{display_current}", required=False)
            return new_value if new_value else current_value

        app_state.customer_email = get_input_with_maybe_current("Email", app_state.customer_email)
        app_state.customer_name = get_input_with_maybe_current("Full Name", app_state.customer_name)
        app_state.customer_phone = get_input_with_maybe_current("Phone Number", app_state.customer_phone)
        app_state.num_orders = get_input_with_maybe_current("Number of orders", app_state.num_orders) or "10"
        app_state.restaurant_name = get_input_with_maybe_current("Restaurant name", app_state.restaurant_name)

        logger.info(
            f"Final customer details: Email={app_state.customer_email}, Name={app_state.customer_name}, "
            f"Phone={app_state.customer_phone}, Orders={app_state.num_orders}, Restaurant={app_state.restaurant_name}"
        )

        SessionUtils.save_session_data()
        StatusPrinter.print_status("Session details saved.", "success")

        app_state.message_preview_shown = False
        show_message_preview()
        StatusPrinter.print_status("Press ENTER to continue to main program...", "info")
        input()


def key_listener() -> None:
    """Background thread to listen for keyboard input."""
    if platform.system() == "Windows" and win32api_available:
        try:
            import win32api
            import win32process

            win32process.SetThreadPriority(win32api.GetCurrentThread(), win32process.THREAD_PRIORITY_HIGHEST)
            logger.debug("Key listener thread priority set to highest.")
        except ImportError:
            logger.warning("Could not set thread priority (win32api not available or failed import)")
        except Exception as e:
            logger.warning(f"Failed to set thread priority: {e}")

    logger.info("Key listener thread started.")
    while True:
        try:
            if InputHandler.kbhit():
                key = InputHandler.getch()
                # Process relevant keys immediately
                if key in (b"p", "p", b"q", "q"):
                    handle_keypress(key)
                # Ignore other keys or handle them if needed
            # Use configured interval for checking
            time.sleep(Config.KEY_CHECK_INTERVAL)
        except OSError as e:
            # Ignore specific OSError related to file descriptor on exit in non-Windows
            if platform.system() != "Windows" and e.errno == 9:  # Bad file descriptor
                logger.warning("Key listener exiting due to closed stdin (likely program termination).")
                break  # Exit thread gracefully
            else:
                logger.error(f"Error in key listener: {e}")
                time.sleep(0.5)  # Longer sleep on error
        except Exception as e:
            logger.error(f"Unexpected error in key listener: {e}")
            time.sleep(0.5)


def handle_keypress(key: Union[bytes, str]) -> None:
    """Handle a keypress event for pause/quit."""
    logger.debug(f"Handling keypress: {key}")
    if key in (b"p", "p"):
        is_paused = run_state.toggle_pause()
        # Status messages are now handled within wait_if_paused and toggle_pause caller
        logger.info(f"Pause toggled by user. New state: {'Paused' if is_paused else 'Running'}")

    elif key in (b"q", "q"):
        logger.info("Exit requested by user via 'q' key")
        StatusPrinter.print_status("Exit requested. Shutting down...", "warning")
        # Use os._exit for a more immediate shutdown, especially if threads are stuck
        threading.Timer(0.1, lambda: os._exit(0)).start()


def handle_processing_cycle(driver: uc.Chrome) -> bool:
    """Handle processing cycle with agent checks but no timed cleanup."""
    if not driver:
        logger.error("Cannot start processing cycle: WebDriver is not available.")
        return False  # Cannot continue

    try:
        logger.info("Starting new processing cycle...")
        while True:
            run_state.wait_if_paused()  # Check if paused at start of cycle

            # --- Reload Config ---
            try:
                # Import here to ensure latest module version is used if possible
                from config_module import get_config

                banned_config = get_config(for_banned=True)

                # Update Config class attributes directly
                Config.CYCLE_SIZE = banned_config.get("CYCLE_SIZE", Config.CYCLE_SIZE)
                Config.MAX_BATCH_SIZE = banned_config.get("MAX_ORDERS_PER_BATCH", Config.MAX_BATCH_SIZE)
                Config.ORDERS_PROCESSED = banned_config.get("ORDERS_PROCESSED", Config.ORDERS_PROCESSED)
                Config.MAX_OPEN_TABS = banned_config.get("MAX_OPEN_TABS", Config.MAX_OPEN_TABS)
                # Update relevant AppState attributes too
                app_state.max_orders_per_batch = Config.MAX_BATCH_SIZE

                logger.info(
                    f"Hot-swapped config: CYCLE_SIZE={Config.CYCLE_SIZE}, "
                    f"ORDERS_PROCESSED={Config.ORDERS_PROCESSED}, "
                    f"MAX_BATCH_SIZE={Config.MAX_BATCH_SIZE} (AppState MAX_ORDERS={app_state.max_orders_per_batch}), "
                    f"MAX_OPEN_TABS={Config.MAX_OPEN_TABS}"
                )
            except ImportError:
                logger.debug(
                    "config_module not found, using existing static config."
                )  # Debug level if module isn't used
            except Exception as config_e:
                logger.warning(f"Failed to hot-swap configuration: {config_e}. Using existing values.")

            # --- Get Virtual Order instead of real orders ---
            orders = Order.get_virtual_order()
            if not orders:
                StatusPrinter.print_status("❌ CRITICAL ERROR: Failed to create virtual order", "error")
                StatusPrinter.print_status("🔧 This usually means:", "warning")
                StatusPrinter.print_status("   • The chat URL path is incorrect", "warning")
                StatusPrinter.print_status("   • DoorDash changed their URL structure", "warning")
                StatusPrinter.print_status("   • Network connectivity issues", "warning")
                StatusPrinter.print_status("📝 Check the URL in get_virtual_order() method", "info")
                logger.error("CRITICAL ERROR: Failed to create virtual order. Check URL configuration.")
                # Wait before potentially restarting cycle or exiting
                StatusPrinter.show_countdown(30, "Waiting before retry/exit")
                continue  # Restart the while loop

            # --- Process Orders ---
            # Force cycle_size to 1 for virtual order processing
            original_cycle_size = Config.CYCLE_SIZE
            Config.CYCLE_SIZE = 1  # Override temporarily
            process_orders(orders, driver)
            Config.CYCLE_SIZE = original_cycle_size  # Restore after processing

            # --- Agent Check ---
            StatusPrinter.show_countdown(Config.AGENT_CHECK_COUNTDOWN, "Waiting before checking agents")
            run_state.wait_if_paused()  # Allow pause before agent check
            check_and_message_agents(driver)

            # --- Cycle End ---
            current_tab_count = len(driver.window_handles) - 1  # Exclude main tab
            StatusPrinter.print_status(
                f"Cycle complete. Currently have {current_tab_count}/{Config.MAX_OPEN_TABS} tabs open.",
                "info",
            )
            logger.info(f"Processing cycle finished. {current_tab_count} tabs open (excluding main).")

            # Loop continues indefinitely until error or user interrupt

    except KeyboardInterrupt:
        logger.info("Processing cycle interrupted by user.")
        raise  # Propagate to main loop's error handler
    except NoSuchWindowException:
        logger.error("Main browser window closed during processing cycle. Cannot continue.")
        StatusPrinter.print_status("Browser window closed unexpectedly. Exiting.", "error")
        return False  # Signal failure to exit main loop
    except Exception as e:
        StatusPrinter.print_status(f"Critical error in processing cycle: {str(e)}", "error")
        logger.error(f"Critical error in processing cycle: {e}\n{traceback.format_exc()}")
        return False  # Signal failure to exit main loop


@contextmanager
def error_handling() -> Iterator[None]:
    """Context manager for consistent error handling."""
    browser_manager_local = None  # Keep track of manager if created within context
    try:
        # Pass the browser manager instance if available globally or create locally
        # This example assumes browser_manager is created outside and passed if needed
        yield
    except KeyboardInterrupt:
        StatusPrinter.print_status("Program interrupted by user (Ctrl+C)", "warning")
        logger.warning("Program interrupted by user (KeyboardInterrupt).")
        # Perform necessary cleanup here if needed before exit
    except SystemExit as e:
        # Handle sys.exit() calls gracefully
        logger.info(f"Program exited with code: {e.code}")
        # Perform cleanup if needed
    except Exception as e:
        # Log and print detailed error
        print_error(e, "An unexpected error occurred")
        logger.critical(f"An unexpected critical error occurred: {e}")
        logger.critical(f"Traceback:\n{traceback.format_exc()}")
        # Ensure console is aware
        app_state.console.print_exception(show_locals=True)
    finally:
        # --- Cleanup ---
        # This block executes whether an exception occurred or not.
        # Ensure browser cleanup happens if browser_manager was involved.
        # Note: The ErrorHandlingContext might need access to the browser_manager instance.
        # This example assumes cleanup is handled in the main function's finally block.
        logger.info("Exiting error handling context.")


# =============================================================================
#  MAIN EXECUTION
# =============================================================================
def main() -> None:
    """Main application entry point."""
    global app_state, run_state  # Allow modification of global state objects

    parser = argparse.ArgumentParser(description="DoorDash Tip Removal Script v3.1 (Manual Login Fix v6 - Faster Send)")
    # Keep existing arguments
    parser.add_argument("--skip-dependency-check", action="store_true", help="Skip dependency check")
    parser.add_argument("--headless", action="store_true", help="Force headless mode")
    parser.add_argument("--no-headless", action="store_true", help="Force disable headless mode")
    parser.add_argument("--test-mode", action="store_true", help="Enable test mode")
    parser.add_argument(
        "--log-level",
        choices=["OFF", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],  # Added CRITICAL
        default=None,
        help="Set console logging level (overrides config)",
    )
    args = parser.parse_args()

    # --- Initial Setup ---
    # Initialize run_state here before logger setup might use it
    run_state = RunState()
    # Initial logger setup to capture early messages
    setup_logger()  # Basic setup first
    logger.info("Starting DoorDash Tip Removal Script...")

    # --- Configuration Loading ---
    # Load config from file/module first
    saved_config = load_config()
    # Apply saved config values to AppState and Config class
    if saved_config:
        app_state.test_mode = saved_config.get("TEST_MODE", app_state.test_mode)
        app_state.headless_mode = saved_config.get("HEADLESS_MODE", app_state.headless_mode)
        app_state.max_orders_per_batch = saved_config.get("MAX_ORDERS_PER_BATCH", app_state.max_orders_per_batch)
        app_state.log_level = saved_config.get("LOG_LEVEL", app_state.log_level)
        Config.MAX_BATCH_SIZE = app_state.max_orders_per_batch  # Sync Config class
        Config.CYCLE_SIZE = saved_config.get("CYCLE_SIZE", Config.CYCLE_SIZE)
        Config.ORDERS_PROCESSED = saved_config.get("ORDERS_PROCESSED", Config.ORDERS_PROCESSED)
        Config.MAX_OPEN_TABS = saved_config.get("MAX_OPEN_TABS", Config.MAX_OPEN_TABS)
        logger.info("Loaded configuration from module/file.")

    # Apply command-line overrides (highest priority)
    if args.headless:
        app_state.headless_mode = True
        logger.info("Headless mode forced via command line.")
    elif args.no_headless:
        app_state.headless_mode = False
        logger.info("Headless mode disabled via command line.")
    if args.test_mode:
        app_state.test_mode = True
        logger.info("Test mode enabled via command line.")
    if args.log_level:
        app_state.log_level = args.log_level.upper()
        logger.info(f"Log level set via command line: {app_state.log_level}")

    # --- Final Logger & UI Setup ---
    # Apply the final log level (considering config and cmd line)
    set_log_level(app_state.log_level)
    os.system("cls" if os.name == "nt" else "clear")  # Clear screen
    StatusPrinter.print_large_title()

    # --- Menu System ---
    # Run menu system to potentially modify config again
    menu_choice_action = run_menu_system()  # e.g., 'start', 'login_manual', 'login_cookies', 'exit'

    if not menu_choice_action or menu_choice_action == "exit":
        logger.info("Exiting program based on menu choice.")
        StatusPrinter.print_status("Exiting program.", "info")
        return

    # --- Reload Config Post-Menu ---
    # CRITICAL: Reload config *after* menu system in case settings were changed
    saved_config = load_config()
    if saved_config:
        app_state.test_mode = saved_config.get("TEST_MODE", app_state.test_mode)
        app_state.headless_mode = saved_config.get("HEADLESS_MODE", app_state.headless_mode)
        app_state.max_orders_per_batch = saved_config.get("MAX_ORDERS_PER_BATCH", app_state.max_orders_per_batch)
        new_log_level = saved_config.get("LOG_LEVEL", app_state.log_level)
        Config.MAX_BATCH_SIZE = app_state.max_orders_per_batch
        Config.CYCLE_SIZE = saved_config.get("CYCLE_SIZE", Config.CYCLE_SIZE)
        Config.ORDERS_PROCESSED = saved_config.get("ORDERS_PROCESSED", Config.ORDERS_PROCESSED)
        Config.MAX_OPEN_TABS = saved_config.get("MAX_OPEN_TABS", Config.MAX_OPEN_TABS)
        # Re-apply log level ONLY IF it changed and wasn't set by cmd line
        if not args.log_level and new_log_level.upper() != app_state.log_level:
            app_state.log_level = new_log_level.upper()
            set_log_level(app_state.log_level)
            logger.info(f"Log level updated from menu: {app_state.log_level}")
        logger.info("Configuration reloaded after menu.")

    logger.info(
        f"Final config: Headless={app_state.headless_mode}, Test={app_state.test_mode}, "
        f"LogLevel={app_state.log_level}, CycleSize={Config.CYCLE_SIZE}, "
        f"OrdersProcessed={Config.ORDERS_PROCESSED}, MaxBatch={Config.MAX_BATCH_SIZE}, MaxTabs={Config.MAX_OPEN_TABS}"
    )

    # --- Start Background Threads ---
    key_thread = threading.Thread(target=key_listener, daemon=True)
    key_thread.start()
    logger.debug("Key listener thread started.")

    # --- Main Application Logic ---
    browser_manager = BrowserManager()
    driver = None

    with ErrorHandlingContext(browser_manager):  # Use context manager for error handling
        try:
            # --- Browser Creation ---
            StatusPrinter.print_status("Initializing browser...", "processing")
            driver = browser_manager.create_driver(force_headless=app_state.headless_mode)
            driver.implicitly_wait(3)  # Set implicit wait

            # --- Login ---
            # Determine login type based on menu choice
            use_cookie_login = menu_choice_action == "login_cookies"
            handle_login(driver, auto_retry=use_cookie_login)
            # Confirmation/editing is now handled within handle_login

            # --- Processing Loop ---
            StatusPrinter.print_header("STARTING PROCESSING CYCLE")
            if not handle_processing_cycle(driver):
                StatusPrinter.print_status("Processing cycle failed or exited.", "warning")
                logger.warning("Processing cycle did not complete successfully.")

            StatusPrinter.print_status("Processing finished.", "success")
            logger.info("Main processing loop finished.")

        except Exception as main_err:
            # Errors should be caught by ErrorHandlingContext, but this is a fallback
            StatusPrinter.print_status(f"A critical error occurred in main execution: {main_err}", "error")
            logger.critical(f"Critical error in main: {main_err}\n{traceback.format_exc()}")
            # Ensure cleanup is attempted even if context manager fails
            if browser_manager:
                browser_manager.cleanup_driver()
        finally:
            # --- Final Cleanup ---
            logger.info("Executing final cleanup...")
            if browser_manager:
                browser_manager.cleanup_driver()
            StatusPrinter.print_status("Script finished.", "info")
            logger.info("Program completed.")


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
