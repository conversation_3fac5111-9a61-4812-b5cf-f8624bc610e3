@echo off
cls
title ProxySeller Firefox Container Setup Tool v1.7

REM Create a log file for batch script operations from the start
set BATCH_LOG_FILE=launcher_setup.log
echo Starting ProxySeller Firefox Container Setup Tool v1.7 at %TIME% on %DATE% > %BATCH_LOG_FILE%
echo. >> %BATCH_LOG_FILE%

echo.
echo  ***********************************************
echo  *  ProxySeller Firefox Container Setup Tool   *
echo  ***********************************************
echo.
echo  This tool will guide you through setting up
echo  Firefox containers with ProxySeller proxies.
echo.
echo =========================================
echo  Step 1: Checking Prerequisites
echo =========================================
echo. >> %BATCH_LOG_FILE%
echo Step 1: Checking Prerequisites >> %BATCH_LOG_FILE%

REM Check if Python is installed
echo Checking for Python installation...
echo Checking for Python installation... >> %BATCH_LOG_FILE%
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Python is not installed or not found in your system's PATH.
    echo [ERROR] Python is not installed or not found in your system's PATH. >> %BATCH_LOG_FILE%
    echo         Please install Python 3.7+ and ensure it's added to PATH.
    echo         Download from: https://www.python.org/downloads/
    goto end_error
)
echo   [OK] Python found.
echo   [OK] Python found. >> %BATCH_LOG_FILE%
echo.

REM Ensure necessary Python packages are installed
echo Installing/Updating required Python packages (selenium, webdriver-manager, requests)...
echo Installing/Updating required Python packages (selenium, webdriver-manager, requests)... >> %BATCH_LOG_FILE%
echo   (This may take a moment. Output logged to pip_install.log)
python -m pip install --upgrade pip > pip_install.log 2>&1
echo Pip upgrade output (pip_install.log): >> %BATCH_LOG_FILE%
type pip_install.log >> %BATCH_LOG_FILE% 2>&1
python -m pip install --upgrade selenium webdriver-manager requests >> pip_install.log 2>&1
echo Requirements install output (pip_install.log): >> %BATCH_LOG_FILE%
type pip_install.log >> %BATCH_LOG_FILE% 2>&1

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install required Python packages.
    echo [ERROR] Failed to install required Python packages. >> %BATCH_LOG_FILE%
    echo         Please check your internet connection and permissions.
    echo         See pip_install.log for details.
    goto end_error
)
echo   [OK] Packages installed/updated.
echo   [OK] Packages installed/updated. >> %BATCH_LOG_FILE%
echo.
echo =========================================
echo  Step 2: Gathering Information
echo =========================================
echo. >> %BATCH_LOG_FILE%
echo Step 2: Gathering Information >> %BATCH_LOG_FILE%
echo.

REM --- API Key ---
set API_KEY=
:ask_api_key
set /p API_KEY="Enter your ProxySeller API Key: "
echo User input for API Key: [HIDDEN] >> %BATCH_LOG_FILE%
if "%API_KEY%"=="" (
    echo [ERROR] API Key cannot be empty. Please try again.
    echo [ERROR] API Key cannot be empty. >> %BATCH_LOG_FILE%
    goto ask_api_key
)
echo.

REM --- Number of Containers ---
set "NUM_CONTAINERS=5" REM Default value
:ask_num_containers_loop
set "INPUT_NUM_CONTAINERS="
set /p INPUT_NUM_CONTAINERS="How many containers/proxies do you need? (1-99) [Default: 5]: "
echo User input for NUM_CONTAINERS: "%INPUT_NUM_CONTAINERS%" >> %BATCH_LOG_FILE%

REM Handle default if input is empty
if "%INPUT_NUM_CONTAINERS%"=="" (
    echo   Using default: %NUM_CONTAINERS% containers.
    echo   Using default: %NUM_CONTAINERS% containers. >> %BATCH_LOG_FILE%
    goto num_containers_valid
)

REM Basic check: Try to convert to a number.
REM This is the simplest check. If it's not numeric, %NUMBER_CHECK% will be 0 (unless input is "0").
REM If input is "abc", %NUMBER_CHECK% becomes 0.
REM If input is "0", %NUMBER_CHECK% becomes 0.
REM If input is "-5", %NUMBER_CHECK% becomes -5.
set /A NUMBER_CHECK = %INPUT_NUM_CONTAINERS% 2>nul

REM If set /a failed (truly non-numeric like "abc" or just symbols, errorlevel will be 1)
REM OR if the result is less than 1
if errorlevel 1 (
    echo [ERROR] Invalid input: '%INPUT_NUM_CONTAINERS%' is not a valid whole number. Please try again.
    echo [ERROR] Invalid input (set /a failed or produced non-numeric): '%INPUT_NUM_CONTAINERS%' >> %BATCH_LOG_FILE%
    goto ask_num_containers_loop
)
if %NUMBER_CHECK% LSS 1 (
    echo [ERROR] Number of containers must be at least 1. You entered: '%INPUT_NUM_CONTAINERS%'
    echo [ERROR] Invalid input (not positive): '%INPUT_NUM_CONTAINERS%' >> %BATCH_LOG_FILE%
    goto ask_num_containers_loop
)

REM At this point, NUMBER_CHECK should hold a valid positive integer.
set NUM_CONTAINERS=%NUMBER_CHECK%

:num_containers_valid
echo   Number selected: %NUM_CONTAINERS%
echo   Number selected: %NUM_CONTAINERS% >> %BATCH_LOG_FILE%
echo.

REM --- Proxy Type ---
set PROXY_TYPE=
:ask_proxy_type
set /p PROXY_TYPE="Enter proxy type (http or socks5) [http]: "
echo User input for PROXY_TYPE: "%PROXY_TYPE%" >> %BATCH_LOG_FILE%
if /i "%PROXY_TYPE%"=="" set PROXY_TYPE=http
if /i not "%PROXY_TYPE%"=="http" if /i not "%PROXY_TYPE%"=="socks5" (
    echo [ERROR] Invalid proxy type. Please enter 'http' or 'socks5'.
    echo [ERROR] Invalid proxy type: "%PROXY_TYPE%". >> %BATCH_LOG_FILE%
    goto ask_proxy_type
)
echo.

REM --- Log Level ---
set LOG_LEVEL=
:ask_log_level
set /p LOG_LEVEL="Enter desired console log level (DEBUG, INFO, WARNING, ERROR, CRITICAL) [INFO]: "
echo User input for LOG_LEVEL: "%LOG_LEVEL%" >> %BATCH_LOG_FILE%
if /i "%LOG_LEVEL%"=="" set LOG_LEVEL=INFO
REM Simple validation - check if it's one of the allowed values
echo ;DEBUG;INFO;WARNING;ERROR;CRITICAL; | findstr /i /c:";%LOG_LEVEL%;" > nul
if errorlevel 1 (
    echo [ERROR] Invalid log level. Please choose from DEBUG, INFO, WARNING, ERROR, CRITICAL.
    echo [ERROR] Invalid log level: "%LOG_LEVEL%". >> %BATCH_LOG_FILE%
    goto ask_log_level
)
echo.

REM --- Skip Import Option ---
set SKIP_IMPORT_FLAG=
set SKIP_IMPORT_ARG=
:ask_skip_import
set /p SKIP_IMPORT_FLAG="Skip automated FoxyProxy import (requires manual import)? (yes/no) [no]: "
echo User input for SKIP_IMPORT_FLAG: "%SKIP_IMPORT_FLAG%" >> %BATCH_LOG_FILE%
if /i "%SKIP_IMPORT_FLAG%"=="" set SKIP_IMPORT_FLAG=no
if /i "%SKIP_IMPORT_FLAG%"=="yes" set SKIP_IMPORT_ARG=--skip-import
if /i "%SKIP_IMPORT_FLAG%"=="no" set SKIP_IMPORT_ARG=
if /i not "%SKIP_IMPORT_FLAG%"=="yes" if /i not "%SKIP_IMPORT_FLAG%"=="no" (
    echo [ERROR] Please enter 'yes' or 'no'.
    echo [ERROR] Invalid input for SKIP_IMPORT_FLAG: "%SKIP_IMPORT_FLAG%". >> %BATCH_LOG_FILE%
    goto ask_skip_import
)
echo.


echo =========================================
echo  Step 3: Running the Setup Script
echo =========================================
echo. >> %BATCH_LOG_FILE%
echo Step 3: Running the Setup Script >> %BATCH_LOG_FILE%
echo.
echo Configuration:
echo   - API Key:          ***%API_KEY:~-5% (Hidden)
echo   - Containers:       %NUM_CONTAINERS%
echo   - Proxy Type:       %PROXY_TYPE%
echo   - Log Level:        %LOG_LEVEL%
echo   - Skip FoxyImport:  %SKIP_IMPORT_FLAG%
echo.
echo Configuration: >> %BATCH_LOG_FILE%
echo   - API Key:          ***%API_KEY:~-5% (Hidden) >> %BATCH_LOG_FILE%
echo   - Containers:       %NUM_CONTAINERS% >> %BATCH_LOG_FILE%
echo   - Proxy Type:       %PROXY_TYPE% >> %BATCH_LOG_FILE%
echo   - Log Level:        %LOG_LEVEL% >> %BATCH_LOG_FILE%
echo   - Skip FoxyImport:  %SKIP_IMPORT_FLAG% >> %BATCH_LOG_FILE%
echo. >> %BATCH_LOG_FILE%
echo Starting Python script (main.py)...
echo Starting Python script (main.py)... >> %BATCH_LOG_FILE%
echo Log files: proxy_setup.log (Python Debug), pip_install.log, launcher_setup.log (This file)
echo --- Script Output Starts Below ---
echo.

REM Run the main Python script directly, passing arguments
python main.py -k "%API_KEY%" -n %NUM_CONTAINERS% -t %PROXY_TYPE% --log-level %LOG_LEVEL% %SKIP_IMPORT_ARG%

REM Capture the exit code from Python
set PY_EXITCODE=%errorlevel%
echo Python script exited with code: %PY_EXITCODE% >> %BATCH_LOG_FILE%

echo.
echo --- Script Output Ends Above ---
echo =========================================
echo  Step 4: Finished
echo =========================================
echo. >> %BATCH_LOG_FILE%
echo Step 4: Finished >> %BATCH_LOG_FILE%
echo.
if %PY_EXITCODE% neq 0 (
    echo [WARNING] Python script finished with ERROR code %PY_EXITCODE%.
    echo [WARNING] Python script finished with ERROR code %PY_EXITCODE%. >> %BATCH_LOG_FILE%
    echo           Please review the script output above and check the detailed logs in:
    echo           - proxy_setup.log (Python script log)
    echo           - launcher_setup.log (Launcher script log)
) else (
    echo [SUCCESS] Python script completed successfully.
    echo [SUCCESS] Python script completed successfully. >> %BATCH_LOG_FILE%
    echo           Check summary file: proxy_container_summary.txt
    echo           Check FoxyProxy config: foxyproxy_config.json
    if defined SKIP_IMPORT_ARG (
      echo.
      echo           NOTE: Automated FoxyProxy import was skipped. Please import manually.
    )
)
echo.
goto end_pause

:end_error
echo.
echo [CRITICAL] Setup cannot continue due to errors. Check launcher_setup.log for details.
echo [CRITICAL] Setup cannot continue due to errors. >> %BATCH_LOG_FILE%
echo.

:end_pause
echo =========================================
echo Press any key to close this window...
pause > nul
exit /b %PY_EXITCODE%
