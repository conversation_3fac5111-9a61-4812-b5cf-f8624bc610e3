INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-14 00:09:42,766 - INFO - [__main__:load_config_and_args:2491] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 00:09:42,766 - INFO - [__main__:load_config_and_args:2493] - Config sections: []
2025-05-14 00:09:42,767 - INFO - [__main__:load_config_and_args:2494] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-14 00:09:42,768 - INFO - [__main__:load_config_and_args:2628] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-14 00:09:42,769 - INFO - [__main__:load_config_and_args:2491] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 00:09:42,769 - INFO - [__main__:load_config_and_args:2493] - Config sections: []
2025-05-14 00:09:42,769 - INFO - [__main__:load_config_and_args:2494] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-14 00:09:42,770 - INFO - [__main__:load_config_and_args:2628] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-14 00:09:42,770 - INFO - [__main__:<module>:2767] - Console logging level set to: INFO
2025-05-14 00:09:42,771 - INFO - [__main__:<module>:2768] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 00:09:42,771 - INFO - [__main__:<module>:2769] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-14 00:09:42,772 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-14 00:09:42,772 - INFO - [__main__:__init__:203] - Attempting to determine Firefox profile path...
2025-05-14 00:09:42,773 - INFO - [__main__:_find_firefox_profile:296] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-14 00:09:42,773 - INFO - [__main__:_find_firefox_profile:311] - Found default profile section in profiles.ini: Profile1
2025-05-14 00:09:42,774 - INFO - [__main__:_find_firefox_profile:359] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 00:09:42,774 - INFO - [__main__:__init__:232] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 00:09:43,031 - INFO - [__main__:run:1474] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-14 00:09:43,032 - INFO - [__main__:run:1480] - Starting proxy container setup process...
2025-05-14 00:09:43,874 - INFO - [__main__:init_api_client:433] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-14 00:09:44,059 - WARNING - [__main__:init_api_client:445] - Could not parse balance from response: 11
2025-05-14 00:09:44,060 - INFO - [__main__:fetch_proxies:469] - Fetching proxies from Proxy-Seller...
2025-05-14 00:09:44,061 - INFO - [__main__:fetch_proxies:483] - Calling proxyList(type='ipv4') API method...
2025-05-14 00:09:44,262 - INFO - [__main__:fetch_proxies:508] - proxyList returned legacy/empty response with 'items': [], treating as no proxies.
2025-05-14 00:09:44,263 - INFO - [__main__:fetch_proxies:562] - Found 0 existing, active, matching USA HTTP proxies via API call and local filtering.
2025-05-14 00:09:44,263 - INFO - [__main__:fetch_proxies:573] - Attempting to create 5 new USA HTTP proxies.
2025-05-14 00:09:44,453 - ERROR - [__main__:create_usa_proxies:798] - referenceList('ipv4') items section: []
2025-05-14 00:09:44,454 - ERROR - [__main__:create_usa_proxies:803] - No countries returned from referenceList('ipv4'). Check your API key/account permissions.
2025-05-14 00:09:44,454 - ERROR - [__main__:create_usa_proxies:807] - No periods returned from referenceList('ipv4'). Check your API key/account permissions.
2025-05-14 00:09:44,455 - WARNING - [__main__:fetch_proxies:640] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-14 00:09:44,455 - WARNING - [__main__:fetch_proxies:738] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-14 00:09:44,455 - ERROR - [__main__:fetch_proxies:747] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-14 00:09:44,456 - ERROR - [__main__:run:1492] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-14 00:09:44,456 - ERROR - [__main__:<module>:2783] - Main setup process reported failure or incomplete execution for core operations.
2025-05-14 00:09:44,456 - ERROR - [__main__:<module>:2863] - ==================================================
2025-05-14 00:09:44,457 - ERROR - [__main__:<module>:2864] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-14 00:09:44,457 - ERROR - [__main__:<module>:2865] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-14 00:09:44,458 - ERROR - [__main__:<module>:2868] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-14 00:09:44,458 - ERROR - [__main__:<module>:2871] - ==================================================
2025-05-14 00:09:44,458 - INFO - [__main__:<module>:2877] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
