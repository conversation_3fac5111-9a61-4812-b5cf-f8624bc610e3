#Requires -Version 5.1

<#
.SYNOPSIS
    Launches the ProxySeller Firefox Container Setup Python script.
.DESCRIPTION
    Handles Python environment checks, dependency installation, and orchestrates
    the execution of the main Python application (main.py) by passing user-provided overrides.
    The Python script is responsible for reading 'config.ini' and its internal defaults.
.NOTES
    Version: 3.2.1
    Author: AI Assistant (Enhanced by User)
    Last Modified: [Current Date]
#>

[CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
param ()

# --- Script Configuration ---
$ScriptVersion = "3.2.1"
$RunTimestamp = Get-Date -Format 'yyyy-MM-dd_HH-mm-ss'
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$LauncherLogFile = Join-Path $ScriptDir "launcher_setup_$RunTimestamp.log"
$PythonLogFile = Join-Path $ScriptDir "proxy_setup.log"
$PythonStdoutLog = Join-Path $ScriptDir "python_stdout_$RunTimestamp.log"
$PythonStderrLog = Join-Path $ScriptDir "python_stderr_$RunTimestamp.log"

$OldErrorActionPreference = $ErrorActionPreference
$ErrorActionPreference = 'Stop'

# --- Helper Functions ---
function Log-Message {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,
        [ValidateSet('INFO', 'WARNING', 'ERROR', 'DEBUG', 'FATAL')]
        [string]$Level = 'INFO',
        [switch]$NoConsole
    )
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "$timestamp [$Level] $Message"
    try { Add-Content -Path $LauncherLogFile -Value $logEntry -ErrorAction SilentlyContinue }
    catch { Write-Warning "Failed to write to launcher log file '$LauncherLogFile': $($_.Exception.Message)" }

    if (-not $NoConsole) {
        $colorMap = @{ INFO = 'Green'; WARNING = 'Yellow'; ERROR = 'Red'; DEBUG = 'Gray'; FATAL = 'Magenta' }
        $consoleColor = $colorMap[$Level]
        if (-not ($consoleColor -as [System.ConsoleColor])) { $consoleColor = [System.ConsoleColor]::White }
        Write-ColoredHost "[$Level] $Message" -Color $consoleColor
    }
}

function Write-ColoredHost {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,
        [System.ConsoleColor]$Color = ([System.ConsoleColor]::Gray),
        [switch]$NoNewline
    )
    $oldFgColor = $Host.UI.RawUI.ForegroundColor
    try {
        $Host.UI.RawUI.ForegroundColor = $Color
        if ($NoNewline) { Write-Host $Message -NoNewline } else { Write-Host $Message }
    }
    finally { $Host.UI.RawUI.ForegroundColor = $oldFgColor }
}

function Test-IsAdmin {
    return ([System.Security.Principal.WindowsPrincipal][System.Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Check-PythonInstallation {
    Log-Message "Checking for Python installation..." -NoConsole -Level DEBUG
    Write-ColoredHost "Step 1: Checking for Python installation..." -Color Cyan -NoNewline
    $pythonExePath = Get-Command python -ErrorAction SilentlyContinue

    if (-not $pythonExePath) {
        Log-Message "Python is not installed or not found in PATH." -Level FATAL
        Write-ColoredHost "`nPlease install Python 3.7+ and ensure it's added to your system PATH." -Color Red
        try { Start-Process "https://www.python.org/downloads/" -ErrorAction SilentlyContinue } catch {}
        return $null
    }

    $pyVersionOutput = try { & $pythonExePath.Source --version 2>&1 | Out-String } catch { "" }
    if ($pyVersionOutput -match 'Python (\d+)\.(\d+)') {
        $major = [int]$Matches[1]; $minor = [int]$Matches[2]
        if (($major -lt 3) -or ($major -eq 3 -and $minor -lt 7)) {
            Log-Message "Python version $major.$minor is older than required 3.7+. Path: $($pythonExePath.Source)" -Level FATAL
            Write-ColoredHost "`nPython version $major.$minor found, but 3.7+ is required. Please upgrade." -Color Red
            return $null
        }
        Log-Message "Python version $major.$minor found at: $($pythonExePath.Source)" -Level INFO -NoConsole
        Write-ColoredHost "  [OK] Python $major.$minor found at: $($pythonExePath.Source)" -Color Green
        return $pythonExePath.Source
    }
    else {
        Log-Message "Could not determine Python version. Output: $pyVersionOutput. Assuming OK if Python found." -Level WARNING -NoConsole
        Write-ColoredHost "  [WARNING] Could not determine Python version. Assuming OK if Python command was found." -Color Yellow
        return $pythonExePath.Source
    }
}

function Check-And-Install-PythonPackages {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)] [string]$PythonExecutable,
        [Parameter(Mandatory=$true)] [string[]]$Packages
    )
    Log-Message "Checking/Installing required Python packages: $($Packages -join ', ')" -Level DEBUG -NoConsole
    Write-ColoredHost "Step 2: Checking/Installing required Python packages..." -Color Cyan
    $missingPkgs = [System.Collections.Generic.List[string]]::new()

    foreach ($pkg_item in $Packages) {
        Log-Message "Checking for package: $pkg_item" -Level DEBUG -NoConsole
        $processArgs = @{
            FilePath = $PythonExecutable
            ArgumentList = "-c `"try: import $pkg_item`nexcept ImportError: import sys; sys.exit(1)`""
            PassThru = $true; Wait = $true; WindowStyle = 'Hidden'
        }
        $checkProcess = Start-Process @processArgs -ErrorAction SilentlyContinue
        if ($null -eq $checkProcess -or $checkProcess.ExitCode -ne 0) {
            $missingPkgs.Add($pkg_item)
            Log-Message "Package '$pkg_item' is missing or not importable." -Level WARNING -NoConsole
        } else { Log-Message "Package '$pkg_item' found." -Level DEBUG -NoConsole }
    }

    if ($missingPkgs.Count -gt 0) {
        Log-Message "Missing packages: $($missingPkgs -join ', '). Attempting installation." -Level INFO
        Write-ColoredHost "The following Python packages are missing or not importable: $($missingPkgs -join ', ')" -Color Yellow
        Write-ColoredHost "Attempting to install/upgrade them using pip..." -Color Yellow

        $pipInstallLogPath = Join-Path $ScriptDir "pip_install_$RunTimestamp.log"
        $pipArgsToUse = "-m pip install --upgrade --user $($missingPkgs -join ' ')"
        if (Test-IsAdmin) { $pipArgsToUse = "-m pip install --upgrade $($missingPkgs -join ' ')" }

        Log-Message "Running pip command: $PythonExecutable $pipArgsToUse (Log: $pipInstallLogPath)" -Level INFO -NoConsole
        Write-Host "Running: $PythonExecutable $pipArgsToUse (Log: $pipInstallLogPath)"

        $pipProcessInfo = New-Object System.Diagnostics.ProcessStartInfo -Property @{
            FileName = $PythonExecutable; Arguments = $pipArgsToUse
            RedirectStandardError = $true; RedirectStandardOutput = $true
            UseShellExecute = $false; CreateNoWindow = $true
        }
        $pipProcess = New-Object System.Diagnostics.Process -Property @{ StartInfo = $pipProcessInfo }
        try {
            $pipProcess.Start() | Out-Null
            $pipOutput = $pipProcess.StandardOutput.ReadToEnd()
            $pipErrors = $pipProcess.StandardError.ReadToEnd()
            $pipProcess.WaitForExit()
            "$pipOutput`n$pipErrors" | Set-Content -Path $pipInstallLogPath -Encoding utf8 -Force
            Log-Message "Pip execution output logged to $pipInstallLogPath" -Level DEBUG -NoConsole
            if ($pipProcess.ExitCode -ne 0) {
                Log-Message "Failed to install Python packages via pip. Exit code: $($pipProcess.ExitCode)" -Level ERROR
                Write-ColoredHost "Please check the pip installation log: $pipInstallLogPath" -Color Red
                return $false
            } else {
                Log-Message "Python packages installed/updated successfully via pip." -Level INFO
                return $true
            }
        } catch {
            Log-Message "Exception during pip execution: $($_.Exception.Message)" -Level ERROR
            Write-ColoredHost "An error occurred during pip execution. Check $pipInstallLogPath and launcher log." -Color Red
            return $false
        }
    } else { Log-Message "All required Python packages seem to be installed." -Level INFO; return $true }
}

function Load-Configuration-DefaultsOnly {
    Log-Message "Initializing default configuration parameters for launcher prompts..." -Level DEBUG -NoConsole
    Write-ColoredHost "Step 3: Preparing script parameters (Python script handles 'config.ini' itself)..." -Color Cyan
    $configFilePath = Join-Path $ScriptDir "config.ini"
    $launcherConfig = @{ ApiKey = $null; NumContainers = $null; ProxyType = $null; LogLevel = $null; SkipImport = $null; FirefoxProfilePath = $null }
    if (Test-Path $configFilePath) { Log-Message "config.ini found at $configFilePath. Python script will read it." -Level INFO }
    else { Log-Message "config.ini not found. Python script will use internal defaults if no CLI args." -Level WARNING }
    return $launcherConfig
}

function Prompt-For-Launcher-Overrides {
    [CmdletBinding()] param([Parameter(Mandatory=$true)] [hashtable]$LauncherBaseConfig)
    if (-not $LauncherBaseConfig) {
        Write-ColoredHost "ERROR: LauncherBaseConfig is null in Prompt-For-Launcher-Overrides." -Color Red
        throw "LauncherBaseConfig is null. Cannot prompt for overrides."
    }
    Write-Host "DEBUG: LauncherBaseConfig = $($LauncherBaseConfig | Out-String)"
    Log-Message "Prompting user for parameters to pass as CLI overrides to Python script." -Level DEBUG -NoConsole
    # Manual clone for compatibility with all PowerShell versions
    $overrides = @{}
    foreach ($key in $LauncherBaseConfig.Keys) { $overrides[$key] = $LauncherBaseConfig[$key] }
    Write-ColoredHost ("`nPython script will use 'config.ini' and its internal defaults. " +
                       "You can provide overrides below. Press Enter to skip an override.") -Color DarkCyan

    $apiKeyInput = Read-Host -Prompt "API Key (optional override, e.g., 32 hex chars)"
    if ($null -ne $apiKeyInput -and -not [string]::IsNullOrWhiteSpace($apiKeyInput)) {
        $overrides.ApiKey = $apiKeyInput # Pass as is, Python validates
        Log-Message "API Key override: Will be passed." -Level INFO -NoConsole
        if ($apiKeyInput -notmatch '^[a-fA-F0-9]{32}$') { Write-ColoredHost "Warning: API Key format from prompt seems incorrect. Passing it as is." -Color Yellow }
    }

    $numContainersInput = Read-Host -Prompt "Number of Containers (1-99) (optional override)"
    if ($null -ne $numContainersInput -and -not [string]::IsNullOrWhiteSpace($numContainersInput)) {
        if ($numContainersInput -as [int] -and ($num = [int]$numContainersInput) -ge 1 -and $num -le 99) {
            $overrides.NumContainers = $num; Log-Message "Num Containers override: $num" -Level INFO -NoConsole
        } else { Write-ColoredHost "Invalid Number of Containers. No override." -Color Yellow }
    }

    $proxyTypeInput = Read-Host -Prompt "Proxy Type ('http' or 'socks5') (optional override)"
    if ($null -ne $proxyTypeInput -and -not [string]::IsNullOrWhiteSpace($proxyTypeInput) -and $proxyTypeInput.ToLower() -in @('http', 'socks5')) {
        $overrides.ProxyType = $proxyTypeInput.ToLower(); Log-Message "Proxy Type override: $($overrides.ProxyType)" -Level INFO -NoConsole
    } elseif ($null -ne $proxyTypeInput -and -not [string]::IsNullOrWhiteSpace($proxyTypeInput)) { Write-ColoredHost "Invalid Proxy Type. No override." -Color Yellow }

    $logLevelInput = Read-Host -Prompt "Console Log Level (DEBUG/INFO/WARNING/ERROR/CRITICAL) (optional override)"
    if ($null -ne $logLevelInput -and -not [string]::IsNullOrWhiteSpace($logLevelInput) -and $logLevelInput.ToUpper() -in @('DEBUG','INFO','WARNING','ERROR','CRITICAL')) {
        $overrides.LogLevel = $logLevelInput.ToUpper(); Log-Message "Log Level override: $($overrides.LogLevel)" -Level INFO -NoConsole
    } elseif ($null -ne $logLevelInput -and -not [string]::IsNullOrWhiteSpace($logLevelInput)) { Write-ColoredHost "Invalid Log Level. No override." -Color Yellow }

    $skipImportInput = Read-Host -Prompt "Skip FoxyProxy Import (yes/no) (optional override)"
    if ($null -ne $skipImportInput -and $skipImportInput.ToLower() -eq 'yes') { $overrides.SkipImport = $true; Log-Message "Skip Import override: True" -Level INFO -NoConsole }
    elseif ($null -ne $skipImportInput -and $skipImportInput.ToLower() -eq 'no') { $overrides.SkipImport = $false; Log-Message "Skip Import override: False" -Level INFO -NoConsole }
    elseif ($null -ne $skipImportInput -and -not [string]::IsNullOrWhiteSpace($skipImportInput)) { Write-ColoredHost "Invalid Skip Import. No override." -Color Yellow }

    $profilePathInput = Read-Host -Prompt "Firefox Profile Path (optional override)"
    if ($null -ne $profilePathInput -and -not [string]::IsNullOrWhiteSpace($profilePathInput)) {
        if (Test-Path $profilePathInput -PathType Container) {
             $overrides.FirefoxProfilePath = $profilePathInput; Log-Message "Profile Path override: '$($overrides.FirefoxProfilePath)'" -Level INFO -NoConsole
        } else { Write-ColoredHost "Profile Path '$profilePathInput' not found/not dir. No override." -Color Yellow }
    }
    return $overrides
}

function Execute-PythonScript {
    [CmdletBinding()] param([Parameter(Mandatory=$true)] [string]$PythonExecutable, [Parameter(Mandatory=$true)] [hashtable]$Overrides)
    $pythonScriptPath = Join-Path $ScriptDir "main.py"
    if (-not (Test-Path $pythonScriptPath)) { Log-Message "main.py not found in '$ScriptDir'." -Level FATAL; return 254 }

    $pyArgs = [System.Collections.Generic.List[string]]::new(); $pyArgs.Add("`"$pythonScriptPath`"")
    if ($null -ne $Overrides.ApiKey -and -not [string]::IsNullOrWhiteSpace($Overrides.ApiKey)) { $pyArgs.Add("--api-key `"$($Overrides.ApiKey)`"") }
    if ($null -ne $Overrides.NumContainers) { $pyArgs.Add("--num-containers $($Overrides.NumContainers)") }
    if ($null -ne $Overrides.ProxyType -and -not [string]::IsNullOrWhiteSpace($Overrides.ProxyType)) { $pyArgs.Add("--proxy-type `"$($Overrides.ProxyType)`"") }
    if ($null -ne $Overrides.LogLevel -and -not [string]::IsNullOrWhiteSpace($Overrides.LogLevel)) { $pyArgs.Add("--log-level `"$($Overrides.LogLevel)`"") }
    if ($null -ne $Overrides.SkipImport -and $Overrides.SkipImport -eq $true) { $pyArgs.Add("--skip-import") } # Only add if true
    if ($null -ne $Overrides.FirefoxProfilePath -and -not [string]::IsNullOrWhiteSpace($Overrides.FirefoxProfilePath)) { $pyArgs.Add("--profile `"$($Overrides.FirefoxProfilePath)`"") }

    $fullCommand = "$PythonExecutable $($pyArgs -join ' ')"
    Log-Message "Launching main Python script..." -Level INFO
    Log-Message "Full command: $fullCommand" -Level DEBUG -NoConsole
    Write-ColoredHost "`nStep 4: Launching main Python script..." -Color Cyan; Write-Host "Command: $fullCommand"

    $processArgs = @{ FilePath = $PythonExecutable; ArgumentList = $pyArgs; PassThru = $true; Wait = $true
                      RedirectStandardOutput = $PythonStdoutLog; RedirectStandardError  = $PythonStderrLog; WindowStyle = 'Normal' }
    $pythonProcess = Start-Process @processArgs -ErrorAction SilentlyContinue
    if ($null -eq $pythonProcess) {
        Log-Message "Failed to start Python process. PythonExecutable: $PythonExecutable. Check paths/permissions." -Level FATAL
        Write-ColoredHost "Failed to start Python process. Check logs." -Color Red; return 255
    }
    return $pythonProcess.ExitCode
}

function Display-LogContents {
    [CmdletBinding()] param([string]$LogPath, [string]$LogDisplayName, [System.ConsoleColor]$HeaderColor = 'Gray')
    if (Test-Path $LogPath) {
        $fileInfo = Get-Item $LogPath; if ($fileInfo.Length -gt 0) {
            Write-ColoredHost "`n--- $LogDisplayName (Content from: $LogPath) ---" -Color $HeaderColor
            Get-Content $LogPath -Raw | Write-Host
        } else { Log-Message "$LogDisplayName file is empty: $LogPath" -Level DEBUG }
    } else { Log-Message "$LogDisplayName file not found: $LogPath" -Level DEBUG }
}

# --- Main Script Logic ---
$Global:ExitCode = 1
try {
    Clear-Host
    Write-ColoredHost "***********************************************" -Color Cyan
    Write-ColoredHost "*  ProxySeller Firefox Container Setup Tool   *" -Color Cyan
    Write-ColoredHost "***********************************************" -Color Cyan
    Write-ColoredHost "*         Version $ScriptVersion - $RunTimestamp         *" -Color Cyan
    Write-ColoredHost "***********************************************" -Color Cyan; Write-Host ""

    Log-Message "--- Script Start: ProxySeller Firefox Container Setup Tool v$ScriptVersion ---" -Level INFO
    if (-not (Test-IsAdmin)) { Log-Message "Not running as Admin. Pip install might need --user or admin rights." -Level WARNING }

    $pythonPath = Check-PythonInstallation; if (-not $pythonPath) { throw "Python installation check failed." }
    $requiredPkgs = @("selenium", "webdriver_manager", "requests", "configparser")
    if (-not (Check-And-Install-PythonPackages -PythonExecutable $pythonPath -Packages $requiredPkgs)) {
        throw "Python package check/installation failed."
    }

    $launcherBaseConfig = Load-Configuration-DefaultsOnly
    $launcherOverrides = Prompt-For-Launcher-Overrides -LauncherBaseConfig $launcherBaseConfig

    $scriptExitCode = Execute-PythonScript -PythonExecutable $pythonPath -Overrides $launcherOverrides # Corrected param name

    Display-LogContents -LogPath $PythonStdoutLog -LogDisplayName "Python Script Standard Output" -HeaderColor White
    Display-LogContents -LogPath $PythonStderrLog -LogDisplayName "Python Script Standard Error" -HeaderColor Yellow

    if ($scriptExitCode -ne 0) { Log-Message "Python script exited with code $scriptExitCode." -Level ERROR }
    else { Log-Message "Python script completed successfully (Exit Code 0)." -Level INFO }
    $Global:ExitCode = $scriptExitCode
} catch {
    Log-Message "A critical error occurred in the PowerShell launcher: $($_.Exception.Message)" -Level FATAL
    if ($_.ScriptStackTrace) { Log-Message "Stack Trace: $($_.ScriptStackTrace)" -Level DEBUG -NoConsole }
    $Global:ExitCode = if ($Global:ExitCode -eq 0 -or $Global:ExitCode -eq 1) { 255 } else { $Global:ExitCode }
} finally {
    Write-Host "`n--------------------------------------------------"
    $finalMessage = "Launcher script execution finished. Final Exit Code: $Global:ExitCode"
    Log-Message $finalMessage -Level INFO -NoConsole

    $finalMessageColor = if ($Global:ExitCode -eq 0) { [System.ConsoleColor]::Green } else { [System.ConsoleColor]::Red } # Corrected if
    Write-ColoredHost $finalMessage -Color $finalMessageColor

    Write-Host "`nLogs available in the script directory '$ScriptDir':"
    Write-Host " - This Launcher Log:      $LauncherLogFile"
    Write-Host " - Python Main App Log:    $PythonLogFile"
    Write-Host " - Python Script Stdout:   $PythonStdoutLog"
    Write-Host " - Python Script Stderr:   $PythonStderrLog"
    $pipLogPattern = Join-Path $ScriptDir "pip_install_*.log"
    $actualPipLog = Get-ChildItem -Path $pipLogPattern -File -ErrorAction SilentlyContinue |
                    Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($actualPipLog) { Write-Host " - Pip Install Log:        $($actualPipLog.Name)" }

    Write-Host "`nPress 'L' (Launcher), 'P' (Python App), 'O' (Stdout), 'E' (Stderr) to view logs, or any other key to exit."
    try {
        if ($Host.UI.RawUI.KeyAvailable) { # Check if key is available to prevent blocking in non-interactive
            $keyInput = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            Switch ($keyInput.Character.ToString().ToUpper()) {
                'L' { if (Test-Path $LauncherLogFile) { try { Start-Process notepad.exe $LauncherLogFile -EA SilentlyContinue } catch {} } }
                'P' { if (Test-Path $PythonLogFile)   { try { Start-Process notepad.exe $PythonLogFile -EA SilentlyContinue } catch {} } }
                'O' { if (Test-Path $PythonStdoutLog) { try { Start-Process notepad.exe $PythonStdoutLog -EA SilentlyContinue } catch {} } }
                'E' { if (Test-Path $PythonStderrLog) { try { Start-Process notepad.exe $PythonStderrLog -EA SilentlyContinue } catch {} } }
            }
        } else { Log-Message "No key press detected for log viewing options (possibly non-interactive)." -Level DEBUG }
    } catch { Log-Message "Error during log view option: $($_.Exception.Message)" -Level WARNING }
    $ErrorActionPreference = $OldErrorActionPreference
}
exit $Global:ExitCode
