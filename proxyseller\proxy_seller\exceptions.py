"""
Custom exceptions for the ProxySeller API client.
"""

class ProxySellerAPIException(Exception):
    """
    Exception raised for errors in the ProxySeller API.
    
    Attributes:
        message -- explanation of the error
        status_code -- HTTP status code (if applicable)
        response -- the full API response (if available)
    """
    
    def __init__(self, message, status_code=None, response=None):
        self.message = message
        self.status_code = status_code
        self.response = response
        super().__init__(self.message)
        
    def __str__(self):
        error_msg = self.message
        if self.status_code:
            error_msg = f"{error_msg} (Status code: {self.status_code})"
        return error_msg
