#!/usr/bin/env python3
"""
A minimal Proxy-Seller API diagnostic script. Prints public IP, and raw responses from /proxy/list/ipv4, /reference/list/ipv4, and /balance/get.
"""
import sys
import json
import requests
from pathlib import Path

# --- Read API key from config.ini if present, else prompt ---
def get_api_key():
    config_path = Path(__file__).parent / 'config.ini'
    if config_path.exists():
        import configparser
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        if 'DEFAULT' in config and 'api_key' in config['DEFAULT'] and config['DEFAULT']['api_key'].strip():
            return config['DEFAULT']['api_key'].strip()
    # Fallback: prompt
    print("Enter your Proxy-Seller API key:", file=sys.stderr)
    return input().strip()

api_key = get_api_key()
if not api_key:
    print("ERROR: No API key provided.")
    sys.exit(1)

# --- Print public IP ---
try:
    public_ip = requests.get('https://api.ipify.org').text.strip()
    print(f"[DIAGNOSTIC] This script's public IP: {public_ip}")
except Exception as e:
    print(f"[DIAGNOSTIC] Could not determine public IP: {e}")

base_url = f"https://proxy-seller.com/personal/api/v1/{api_key}"
endpoints = {
    "proxy_list_ipv4": f"{base_url}/proxy/list/ipv4",
    "reference_list_ipv4": f"{base_url}/reference/list/ipv4",
    "balance_get": f"{base_url}/balance/get",
}

for name, url in endpoints.items():
    print(f"\n--- {name} ---")
    try:
        resp = requests.get(url, timeout=15)
        print(f"Status code: {resp.status_code}")
        try:
            data = resp.json()
            print(json.dumps(data, indent=2, ensure_ascii=False))
            if name == "reference_list_ipv4":
                # --- Extract and print available countries and periods ---
                items = data.get("data", {}).get("items", [])
                if items and isinstance(items[0], dict):
                    countries = items[0].get("country", [])
                    periods = items[0].get("period", [])
                    print("\nAvailable countries:")
                    for c in countries:
                        print(f"  ID: {c.get('id')} | Name: {c.get('name')} | Alpha3: {c.get('alpha3')}")
                    print("\nAvailable periods:")
                    for p in periods:
                        print(f"  ID: {p.get('id')} | Name: {p.get('name')}")
                    # Try to find USA country and a valid period
                    usa = next((c for c in countries if str(c.get('alpha3', '')).upper() == 'USA' or str(c.get('name', '')).upper() == 'UNITED STATES'), None)
                    period = periods[0] if periods else None
                    if usa and period:
                        print(f"\n[INFO] Will attempt to order 1 USA IPv4 proxy with countryId={usa['id']} and periodId={period['id']}")
                        # --- Attempt to order a proxy ---
                        order_url = f"{base_url}/order/make"
                        payload = {
                            "countryId": usa["id"],
                            "periodId": period["id"],
                            "quantity": 1,
                            "paymentId": 1,  # 1=balance
                            "protocol": "HTTP"
                        }
                        print(f"\nPOST {order_url}\nPayload: {json.dumps(payload)}")
                        try:
                            order_resp = requests.post(order_url, json=payload, timeout=20)
                            print(f"Order status code: {order_resp.status_code}")
                            try:
                                order_data = order_resp.json()
                                print(json.dumps(order_data, indent=2, ensure_ascii=False))
                            except Exception:
                                print("Non-JSON order response:")
                                print(order_resp.text)
                        except Exception as e:
                            print(f"ERROR posting order: {e}")
                    else:
                        print("[WARN] USA country or valid period not found. Skipping order attempt.")
        except Exception:
            print("Non-JSON response:")
            print(resp.text)
    except Exception as e:
        print(f"ERROR fetching {name}: {e}")
