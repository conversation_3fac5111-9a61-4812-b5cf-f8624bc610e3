INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:45:36,827 - INFO - [__main__:load_config_and_args:2457] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 22:45:36,827 - INFO - [__main__:load_config_and_args:2459] - Config sections: []
2025-05-13 22:45:36,829 - INFO - [__main__:load_config_and_args:2460] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 22:45:36,830 - INFO - [__main__:load_config_and_args:2594] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 22:45:36,830 - INFO - [__main__:<module>:2638] - Console logging level set to: INFO
2025-05-13 22:45:36,831 - INFO - [__main__:<module>:2639] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:45:36,831 - INFO - [__main__:<module>:2640] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 22:45:36,831 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 22:45:36,831 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 22:45:36,833 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 22:45:36,834 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 22:45:36,835 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:45:36,835 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:45:37,008 - INFO - [__main__:run:1450] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-13 22:45:37,008 - INFO - [__main__:run:1456] - Starting proxy container setup process...
2025-05-13 22:45:37,577 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 22:45:37,778 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 22:45:37,778 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 22:45:37,779 - ERROR - [__main__:fetch_proxies:749] - Unexpected error fetching or formatting proxies: 'ProxyContainerSetup' object has no attribute 'api_proxy_list_key'
2025-05-13 22:45:37,782 - ERROR - [__main__:run:1468] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 22:45:37,782 - ERROR - [__main__:<module>:2654] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 22:45:37,782 - ERROR - [__main__:<module>:2734] - ==================================================
2025-05-13 22:45:37,783 - ERROR - [__main__:<module>:2735] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 22:45:37,784 - ERROR - [__main__:<module>:2736] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 22:45:37,784 - ERROR - [__main__:<module>:2739] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 22:45:37,785 - ERROR - [__main__:<module>:2742] - ==================================================
2025-05-13 22:45:37,785 - INFO - [__main__:<module>:2748] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
