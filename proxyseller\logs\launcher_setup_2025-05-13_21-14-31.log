2025-05-13 21:14:31 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:14:31 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:14:31 [DEBUG] Checking for Python installation...
2025-05-13 21:14:31 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-13 21:14:31 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:14:31 [DEBUG] Checking for package: selenium
2025-05-13 21:14:32 [DEBUG] Package 'selenium' found.
2025-05-13 21:14:32 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:14:33 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:14:33 [DEBUG] Checking for package: requests
2025-05-13 21:14:34 [DEBUG] Package 'requests' found.
2025-05-13 21:14:34 [DEBUG] Checking for package: configparser
2025-05-13 21:14:35 [DEBUG] Package 'configparser' found.
2025-05-13 21:14:35 [INFO] All required Python packages seem to be installed.
2025-05-13 21:14:35 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:14:35 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:14:35 [FATAL] A critical error occurred in the PowerShell launcher: A parameter cannot be found that matches parameter name 'CurrentLauncherConfig'.
2025-05-13 21:14:35 [DEBUG] Stack Trace: at <ScriptBlock>, C:\main\proxyseller\start_proxy_setup.ps1: line 283
2025-05-13 21:14:35 [INFO] Launcher script execution finished. Final Exit Code: 255
2025-05-13 21:14:35 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
