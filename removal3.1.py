import concurrent.futures  # <--- Added for parallel processing
import importlib.machinery  # Add import for ModuleSpec
import importlib.util
import json
import logging
import os
import os.path
import pickle
import platform
import random
import re
import select
import shutil
import subprocess
import sys
import threading
import time
import traceback
from datetime import datetime
from pathlib import Path
from queue import Queue
from threading import Lock
from typing import Any, Dict, List, Optional, Sequence, Set, Union

import undetected_chromedriver as uc  # Renamed to match actual import
from loguru import logger
from rich.panel import Panel
from rich.text import Text
from selenium.common.exceptions import (
    ElementClickInterceptedException,
    ElementNotInteractableException,
    NoSuchElementException,
    NoSuchWindowException,
    StaleElementReferenceException,
    TimeoutException,
    WebDriverException,
)
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

if platform.system() == "Windows":
    import msvcrt
else:
    try:
        import termios
        import tty
    except ImportError:
        pass

# Suppress urllib3 connection pool warnings
logging.getLogger("urllib3.connectionpool").setLevel(logging.ERROR)

# Check if we should skip dependency checks (when run from launcher)
SKIP_DEPENDENCY_CHECK: bool = "--skip-dependency-check" in sys.argv


# Import handler - place this before any third-party imports
def check_and_install_dependencies() -> bool:
    """Check if required packages are installed and install them if needed."""
    # Skip the check if run from the launcher with the flag
    if SKIP_DEPENDENCY_CHECK:
        return True

    required_packages: Dict[str, str] = {
        "undetected-chromedriver": ">=3.5.0",
        "rich": ">=12.0.0",
        "selenium": ">=4.1.0",
    }

    missing_packages: List[str] = []

    # Check Python version
    if sys.version_info < (3, 8):
        print(f"Python 3.8+ is required, but you're using {platform.python_version()}")
        return False

    # Special handling for packages with hyphens in their names
    package_import_names: Dict[str, str] = {
        "undetected-chromedriver": "undetected_chromedriver",
        "rich": "rich",
        "selenium": "selenium",
    }

    for package, version in required_packages.items():
        package_import_name: str = package_import_names.get(package, package)
        spec: Optional[importlib.machinery.ModuleSpec] = importlib.util.find_spec(package_import_name)
        if spec is None:
            missing_packages.append(f"{package}{version}")

    # If all packages are available, return True
    if not missing_packages:
        return True

    print("\n" + "=" * 60)
    print(f"Missing required packages: {', '.join(p.split('>=')[0] for p in missing_packages)}")
    print("=" * 60)

    choice: str = input("\nDo you want to install the missing packages? (y/n): ").strip().lower()

    if choice != "y":
        print("Cannot continue without required packages. Exiting.")
        return False

    use_venv: bool = input("Create a virtual environment? Recommended! (y/n): ").strip().lower() == "y"

    if use_venv:
        venv_path: str = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".venv")

        print(f"\nCreating virtual environment at: {venv_path}")
        try:
            subprocess.check_call([sys.executable, "-m", "venv", venv_path])

            if platform.system() == "Windows":
                pip_path: str = os.path.join(venv_path, "Scripts", "pip.exe")
                python_path: str = os.path.join(venv_path, "Scripts", "python.exe")
            else:
                pip_path = os.path.join(venv_path, "bin", "pip")
                python_path = os.path.join(venv_path, "bin", "python")

            print("\nInstalling packages in virtual environment...")
            for package in missing_packages:
                print(f"Installing {package}...")
                subprocess.check_call([pip_path, "install", package])

            print("\nAll packages installed successfully!")
            print("\nRestarting script in virtual environment...")

            if platform.system() == "Windows":
                with open("run_script.bat", "w", encoding="utf-8") as f:
                    f.write(f'@echo off\n"{python_path}" "{os.path.abspath(__file__)}"\npause')
                subprocess.Popen(["run_script.bat"])
            else:
                with open("run_script.sh", "w", encoding="utf-8") as f:
                    f.write(f'#!/bin/bash\n"{python_path}" "{os.path.abspath(__file__)}"')
                os.chmod("run_script.sh", 0o755)
                subprocess.Popen(["./run_script.sh"])

            sys.exit(0)

        except Exception as e:
            print(f"Error creating virtual environment: {e}")
            choice = input("\nTry installing globally instead? (y/n): ").strip().lower()
            if choice != "y":
                return False
            use_venv = False

    if not use_venv:
        try:
            print("\nInstalling packages globally...")
            for package in missing_packages:
                print(f"Installing {package}...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print("\nAll packages installed successfully!")
            print("\nRestarting script...")

            os.execv(sys.executable, ["python"] + sys.argv)

        except Exception as e:
            print(f"Error installing packages: {e}")
            return False

    return True


if not check_and_install_dependencies():
    sys.exit(1)

try:
    # Rich terminal display library
    from rich.console import Console
    from rich.panel import Panel
    from rich.progress import BarColumn, Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
    from rich.style import Style as RichStyle
    from rich.theme import Theme
    from rich.traceback import install as install_rich_traceback

    # We already imported selenium exceptions above, no need to reimport
except ImportError as e:
    print(f"Error importing third-party modules: {e}")
    print("Please run the script again to install dependencies.")
    sys.exit(1)

# Configuration file paths
CONFIG_FILE = Path("config.json")
CONFIG_BANNED_FILE = Path("config_banned.json")


# Add config module loading functions
def load_from_json_fallback(config_file: Path) -> Dict:
    """Load configuration from JSON file as fallback."""
    try:
        if config_file.exists():
            with open(config_file, "r", encoding="utf-8") as f:
                return json.load(f)
        else:
            logger.warning(f"Configuration file {config_file} not found. Using defaults.")
            return {}
    except Exception as e:
        logger.error(f"Error loading configuration from {config_file}: {e}")
        return {}


def load_from_config_module() -> Dict:
    """Load configuration from config_module if available."""
    try:
        # Try to import the config module and use the get_config function
        from config_module import get_config

        # Get the config manager instance for standard config
        config_manager = get_config(for_banned=False)

        # Create a dictionary with all our needed values
        return {
            "TEST_MODE": config_manager.get("TEST_MODE"),
            "HEADLESS_MODE": config_manager.get("HEADLESS_MODE"),
            "MAX_ORDERS_PER_BATCH": config_manager.get("MAX_ORDERS_PER_BATCH"),
            "MAX_TIP_THRESHOLD": config_manager.get("MAX_TIP_THRESHOLD"),
            "RECONNECT_TIMEOUT1": config_manager.get("RECONNECT_TIMEOUT1"),
            "RECONNECT_TIMEOUT2": config_manager.get("RECONNECT_TIMEOUT2"),
            "CLOSE_ALL_TIMEOUT": config_manager.get("CLOSE_ALL_TIMEOUT"),
            "CYCLE_SIZE": config_manager.get("CYCLE_SIZE"),
            "CANCELLED_ORDERS_ELIGIBLE": config_manager.get("CANCELLED_ORDERS_ELIGIBLE"),
            "REFUNDED_ORDERS_ELIGIBLE": config_manager.get("REFUNDED_ORDERS_ELIGIBLE"),
        }
    except ImportError:
        logger.warning("config_module not found, falling back to JSON")
        return {}
    except Exception as e:
        logger.error(f"Error loading from config_module: {e}")
        return {}


# Add save_config function that was referenced but not defined
def save_config(config: Dict) -> None:
    """Save configuration to the config file."""
    try:
        # First try to use config_module if available
        try:
            from config_module import SimpleConfigManager

            config_manager = SimpleConfigManager(CONFIG_FILE)
            for key, value in config.items():
                config_manager.update_value(key, value)
            logger.info("Configuration saved via config_module")
            return
        except (ImportError, ModuleNotFoundError):
            logger.warning("config_module not found, falling back to direct JSON save")

        # Fallback to direct file save
        with open(CONFIG_FILE, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=4)
        logger.info(f"Configuration saved to {CONFIG_FILE}")
    except Exception as config_err:
        logger.error(f"Error saving configuration: {config_err}")


# -------------------------------------------------------------------
# GLOBAL CONFIGURATION
# -------------------------------------------------------------------

# Define the default configuration here for fallback scenarios
default_config: Dict[str, Any] = {
    "TEST_MODE": True,
    "HEADLESS_MODE": False,
    "MAX_ORDERS_PER_BATCH": 30,
    "MAX_TIP_THRESHOLD": 4.0,
    "RECONNECT_TIMEOUT1": 30,
    "RECONNECT_TIMEOUT2": 60,
    "CLOSE_ALL_TIMEOUT": 60,
    "CYCLE_SIZE": 3,
    "CANCELLED_ORDERS_ELIGIBLE": False,
    "REFUNDED_ORDERS_ELIGIBLE": False,
}

# Global variables initialization with default values
TEST_MODE: bool = default_config["TEST_MODE"]
HEADLESS_MODE: bool = default_config["HEADLESS_MODE"]
MAX_ORDERS_PER_BATCH: int = default_config["MAX_ORDERS_PER_BATCH"]
MAX_TIP_THRESHOLD: float = default_config["MAX_TIP_THRESHOLD"]
RECONNECT_TIMEOUT1: int = default_config["RECONNECT_TIMEOUT1"]
RECONNECT_TIMEOUT2: int = default_config["RECONNECT_TIMEOUT2"]
CLOSE_ALL_TIMEOUT: int = default_config["CLOSE_ALL_TIMEOUT"]
CYCLE_SIZE: int = default_config["CYCLE_SIZE"]
CANCELLED_ORDERS_ELIGIBLE: bool = default_config["CANCELLED_ORDERS_ELIGIBLE"]
REFUNDED_ORDERS_ELIGIBLE: bool = default_config["REFUNDED_ORDERS_ELIGIBLE"]

# Additional global constants used throughout the script
MAX_PAGES_TO_LOAD: int = 30
MESSAGE_RETRY_DELAY: int = 5
MAX_RETRIES: int = 3
WINDOW_WIDTH: int = 400
WINDOW_HEIGHT: int = 711
STARTUP_DELAY: int = 5
MIN_AGENT_WAIT: int = 5
MAX_SCROLL_ATTEMPTS: int = 50
PARALLEL_TIMEOUT: int = 6
CHUNK_SIZE: int = 6
MAX_WORKERS: int = 6  # Used in parallel processing
CHAT_BATCH_SIZE: int = 5
MESSAGE_DEDUPE_DELAY: float = 1.0
MAX_CHAT_RETRIES: int = 3
CHAT_RETRY_DELAY: float = 2.0
TAB_POOL_SIZE: int = 20
TAB_WAIT: float = 0.3

FAST_MESSAGE_WAIT: float = 0.5
FAST_BUTTON_WAIT: float = 0.5
FAST_ACTION_WAIT: float = 0.5

NORMAL_MESSAGE_WAIT: int = 1
NORMAL_BUTTON_WAIT: int = 1
NORMAL_ACTION_WAIT: int = 1

RETRY_DELAY: float = 5.0

SUCCESS_STYLE: RichStyle = RichStyle(color="green", bold=True)
ERROR_STYLE: RichStyle = RichStyle(color="red", bold=True)
INFO_STYLE: RichStyle = RichStyle(color="blue", bold=True)
WARNING_STYLE: RichStyle = RichStyle(color="yellow", bold=True)

IMPLICIT_WAIT: int = 4
PAGE_LOAD_TIMEOUT: int = 5
GLOBAL_BUTTON_WAIT: float = 0.5

SCROLL_WAIT: float = 1
SHORT_ACTION_WAIT: float = 0.4
BETWEEN_ELEMENT_RETRIES_WAIT: float = 0.5
RETRY_OPERATION_WAIT: int = 5

customer_name: Optional[str] = None
customer_email: Optional[str] = None


def load_config() -> Dict:
    """Load configuration values from the configuration file."""
    global TEST_MODE, HEADLESS_MODE, MAX_ORDERS_PER_BATCH, MAX_TIP_THRESHOLD
    global RECONNECT_TIMEOUT1, RECONNECT_TIMEOUT2, CLOSE_ALL_TIMEOUT, CYCLE_SIZE
    global CANCELLED_ORDERS_ELIGIBLE, REFUNDED_ORDERS_ELIGIBLE

    logger.info("Loading configuration...")

    # First try to load from config_module
    standard_config = load_from_config_module()

    # If that fails, fall back to JSON
    if not standard_config:
        standard_config = load_from_json_fallback(CONFIG_FILE)

    # Assign global variables from config with defaults
    TEST_MODE = standard_config.get("TEST_MODE", TEST_MODE)
    HEADLESS_MODE = standard_config.get("HEADLESS_MODE", HEADLESS_MODE)
    MAX_ORDERS_PER_BATCH = standard_config.get("MAX_ORDERS_PER_BATCH", MAX_ORDERS_PER_BATCH)
    MAX_TIP_THRESHOLD = standard_config.get("MAX_TIP_THRESHOLD", MAX_TIP_THRESHOLD)
    RECONNECT_TIMEOUT1 = standard_config.get("RECONNECT_TIMEOUT1", RECONNECT_TIMEOUT1)
    RECONNECT_TIMEOUT2 = standard_config.get("RECONNECT_TIMEOUT2", RECONNECT_TIMEOUT2)
    CLOSE_ALL_TIMEOUT = standard_config.get("CLOSE_ALL_TIMEOUT", CLOSE_ALL_TIMEOUT)
    CYCLE_SIZE = standard_config.get("CYCLE_SIZE", CYCLE_SIZE)
    CANCELLED_ORDERS_ELIGIBLE = standard_config.get("CANCELLED_ORDERS_ELIGIBLE", CANCELLED_ORDERS_ELIGIBLE)
    REFUNDED_ORDERS_ELIGIBLE = standard_config.get("REFUNDED_ORDERS_ELIGIBLE", REFUNDED_ORDERS_ELIGIBLE)

    # Log configuration
    logger.info(f"Loaded configuration: TEST_MODE={TEST_MODE}, HEADLESS_MODE={HEADLESS_MODE}")

    return standard_config


# Create initial config file if it doesn't exist
if not CONFIG_FILE.exists():
    save_config(default_config)

# Import menu system once
try:
    from menu_system import run_menu_system
except ImportError:

    def run_menu_system():
        return None


BROWSER_PATHS: List[str] = [
    # Brave paths - Windows
    "C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe",
    "C:/Program Files (x86)/BraveSoftware/Brave-Browser/Application/brave.exe",
    os.path.expandvars("%LOCALAPPDATA%/BraveSoftware/Brave-Browser/Application/brave.exe"),
    # Chrome paths - Windows
    "C:/Program Files/Google/Chrome/Application/chrome.exe",
    "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe",
    os.path.expandvars("%LOCALAPPDATA%/Google/Chrome/Application/chrome.exe"),
    # macOS paths
    "/Applications/Brave Browser.app/Contents/MacOS/Brave Browser",
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
    # Linux paths
    "/usr/bin/brave-browser",
    "/usr/bin/brave",
    "/usr/bin/google-chrome",
    "/usr/bin/chromium",
]


# Define consistent colors
MAIN_COLOR = "#D91400"
ACCENT_COLOR = "#FFD700"

# Setup custom theme
custom_theme = Theme(
    {
        "info": "bold white",
        "warning": "yellow",
        "error": "bold red",
        "success": "green",
        "border": MAIN_COLOR,
        "accent": ACCENT_COLOR,
    }
)
console: Console = Console(theme=custom_theme)

install_rich_traceback(show_locals=False, max_frames=3)


def get_logs_dir() -> Path:
    """Get the logs directory path."""
    # Create logs in the current directory
    return Path("logs")


def add_log_handler() -> None:
    """Add file handler for logging with rotation.

    This function adds a file handler to the logger with rotation
    settings and proper session markers in the log file. It ensures
    that all debug information is captured to files regardless of
    the console logging level.

    Features:
    - Creates logs in the application data directory
    - Uses 'removal3.1log' as the log file name
    - Rotates logs at 1MB with 5 backup files
    - Adds detailed session markers with system information
    - Captures all DEBUG level logs regardless of console settings
    - Handles file permission errors gracefully
    - Console logging is disabled - logs only to file
    """
    # Add file handler always at DEBUG level for complete logs
    logs_dir = get_logs_dir()

    try:
        # Ensure logs directory exists and is writable
        if not logs_dir.exists():
            logs_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created logs directory at: {logs_dir}")

        # Use removal3.1log as the log file name
        log_file_name = "removal3.1log"
        app_log_path = logs_dir / log_file_name

        # Collect system information for detailed logging
        system_info = {
            "Python Version": platform.python_version(),
            "OS": f"{platform.system()} {platform.release()} ({platform.architecture()[0]})",
            "Machine": platform.machine(),
            "Processor": platform.processor() or "Unknown",
            "User": os.environ.get("USERNAME", os.environ.get("USER", "Unknown")),
            "Hostname": platform.node(),
        }

        # Generate detailed session boundary separator
        script_name = Path(sys.argv[0]).name
        cmd_args = " ".join(sys.argv[1:]) if len(sys.argv) > 1 else "None"

        session_marker = (
            f"\n{'=' * 80}\n"
            f"SESSION START: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {script_name}\n"
            f"Command Arguments: {cmd_args}\n"
            f"System Information:\n"
        )

        # Add system info to session marker
        for key, value in system_info.items():
            session_marker += f"  - {key}: {value}\n"

        # Add configuration info
        session_marker += (
            f"Configuration:\n"
            f"  - Headless Mode: {HEADLESS_MODE}\n"
            f"  - Test Mode: {TEST_MODE}\n"
            f"  - Max Orders Per Batch: {MAX_ORDERS_PER_BATCH}\n"
            f"  - Cycle Size: {CYCLE_SIZE}\n"
            f"  - Log Level: {logging.getLevelName(logging.getLogger().level)}\n"
            f"{'=' * 80}\n"
        )

        # Write session marker directly to log file with UTF-8 encoding
        # This ensures the marker appears even if logging initialization fails
        try:
            with open(app_log_path, "a", encoding="utf-8") as f:
                f.write(session_marker)
        except PermissionError:
            # Can't write to log file, no point continuing with function
            return
        except Exception as e:
            # Can't write to log file, no point continuing with function
            return

        # Remove the default console handler
        logger.remove()

        # Add file handler with enhanced rotation settings (5 files, 1MB each)
        # This ensures we keep more history while still managing disk space
        log_id = logger.add(
            str(app_log_path),
            rotation="1 MB",  # Rotate at 1MB file size
            retention=5,  # Keep 5 rotation files instead of 3
            compression="zip",  # Compress rotated logs to save space
            format="{time:YYYY-MM-DD HH:mm:ss,SSS} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",  # Always log everything to file regardless of console level
            encoding="utf-8",  # Ensure proper encoding for all characters
            backtrace=True,  # Include backtrace for errors
            diagnose=True,  # Include variables in stack trace
        )

        # Define a current log file variable in the global scope
        global current_log_file
        current_log_file = str(app_log_path)

        # Log detailed confirmation that file logging is enabled - to file only
        logger.info(f"Log file initialized at: {app_log_path} (Sink ID: {log_id})")
        logger.debug("Log rotation settings: 1MB max size, 5 backup files, zip compression")
        logger.debug(f"Full logging path: {app_log_path.absolute()}")
        logger.info("Console logging disabled - all logs will only go to file")

    except PermissionError:
        # Can't do anything if we can't write to the log file
        pass
    except OSError:
        # Handle OS errors like disk full or invalid file path
        pass
    except Exception:
        # Catch-all for other exceptions
        pass


# Create a global variable to store the current log file path
current_log_file = None


# Global state for pausing
class RunState:
    """Track global run state."""

    def __init__(self):
        self.is_paused = False
        self.pause_lock = threading.Lock()

    def toggle_pause(self) -> bool:
        """Toggle pause state and return new state."""
        with self.pause_lock:
            self.is_paused = not self.is_paused
            return self.is_paused

    def is_running(self) -> bool:
        """Check if script is running (not paused)."""
        with self.pause_lock:
            return not self.is_paused

    def wait_if_paused(self) -> None:
        """Wait if script is paused."""
        with self.pause_lock:
            if not self.is_paused:
                # Not paused, no need to wait or show messages
                return

        # If we get here, the script is paused
        StatusPrinter.print_status("Script paused ⏸️ Press 'p' to resume...", "warning")

        while True:
            with self.pause_lock:
                if not self.is_paused:
                    # No longer paused
                    break

            if InputHandler.kbhit():
                key = InputHandler.getch()
                if key in (b"p", "p"):
                    self.toggle_pause()
                    break
                elif key in (b"q", "q"):
                    logger.info("Exit requested by user via 'q' key while paused")
                    StatusPrinter.print_status("Exit requested. Shutting down...", "warning")
                    os._exit(0)  # Force exit
            time.sleep(0.1)

        StatusPrinter.print_status("Script resumed ▶️", "success")


class AppState:
    """Manage application state."""

    def __init__(self):
        self.console: Console = Console(theme=custom_theme)
        self.print_lock: threading.Lock = threading.Lock()
        self.customer_name: Optional[str] = None
        self.customer_email: Optional[str] = None
        self.customer_phone: Optional[str] = None
        self.num_orders: Optional[str] = None
        self.restaurant_name: Optional[str] = None
        self.test_mode: bool = False
        self.headless_mode: bool = False
        self.max_orders_per_batch: int = MAX_ORDERS_PER_BATCH
        self.log_level: str = "INFO"
        self.current_log_file: Optional[str] = None
        self.message_preview_shown: bool = False


# Create global app state
app_state = AppState()


class InputHandler:
    """Cross-platform input handling."""

    @staticmethod
    def kbhit() -> bool:
        """Check if a keyboard key has been pressed (cross-platform)."""
        if platform.system() == "Windows":
            return msvcrt.kbhit()
        else:
            dr, _, _ = select.select([sys.stdin], [], [], 0)
            return bool(dr)

    @staticmethod
    def getch() -> Union[bytes, str]:
        """Get a single character from the keyboard (cross-platform)."""
        if platform.system() == "Windows":
            return msvcrt.getch()
        else:
            try:
                fd = sys.stdin.fileno()
                old_settings = termios.tcgetattr(fd)
                try:
                    tty.setraw(fd)
                    ch = sys.stdin.read(1)
                finally:
                    termios.tcsetattr(fd, termios.TCSADRAIN, old_settings)
            except (ImportError, AttributeError):
                ch = sys.stdin.read(1)
                if ch == "\n":
                    ch = "\r"
            return ch

    @staticmethod
    def get_user_input(prompt: str, required: bool = True, allow_empty: bool = False) -> str:
        """Get user input with validation."""
        while True:
            value = app_state.console.input(f"[yellow]{Config.INPUT_SYMBOL} {prompt}: [/yellow]").strip()

            if allow_empty and value == "":
                return value

            if not required:
                return value

            if value in ["1", "2", "3", "4", "5"]:
                return value

            if required and not value:
                StatusPrinter.print_status("This field is required", "error")
                continue

            return value

    @staticmethod
    def countdown_timer(seconds: float, message: str = "Waiting") -> None:
        """Display a countdown timer that can be skipped."""
        if seconds < 1:
            time.sleep(seconds)
            return

        for remaining in range(int(seconds), 0, -1):
            sys.stdout.write(f"\r{' ' * Config.HEADER_WIDTH}")
            sys.stdout.write(f"\r➜➜➜ {message}: {remaining}s")
            sys.stdout.flush()

            if InputHandler.kbhit():
                key = InputHandler.getch()
                if key in (b"\r", b"\n"):
                    break
                elif key in (b"5", "5"):
                    StatusPrinter.print_status("User requested exit", "info")
                    sys.exit(0)
            time.sleep(0.05)

        sys.stdout.write(f"\r{' ' * Config.HEADER_WIDTH}\r")
        sys.stdout.flush()

    @staticmethod
    def print_menu_option(key: str, text: str, selected: bool = False) -> None:
        """Print a menu option with styling."""
        bg_style = StyleConfig.HIGHLIGHT_STYLE if selected else RichStyle(color="white")
        # Escape any existing square brackets in the text to prevent Rich markup issues
        escaped_text = text.replace("[", "\\[").replace("]", "\\]")
        app_state.console.print(f" [{bg_style}][{key}][/{bg_style}] {escaped_text}")

    @staticmethod
    def get_simple_input(prompt: str) -> str:
        """Get user input with consistent styling and spacing."""
        app_state.console.print("")
        return app_state.console.input(f"{Config.INPUT_SYMBOL} {prompt}: ").strip()


class StyleConfig:
    """Style configuration for UI elements."""

    HIGHLIGHT_STYLE = RichStyle(color="cyan", bold=True)


class Config:
    """Configuration settings for the application."""

    KEY_CHECK_INTERVAL: float = 0.05
    HEADER_WIDTH: int = 80
    INPUT_SYMBOL: str = "➜"  # Added missing symbol used in input prompts


def ensure_required_directories() -> None:
    """Create all required directories for the application."""
    required_dirs: List[str] = [
        "logs",
        "cookiesBAK",
        "cookie_editor",
    ]
    for directory in required_dirs:
        dir_path: Path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(exist_ok=True)
                logging.info(f"Created directory: {directory}")
            except Exception as e:
                logging.error(f"Failed to create directory {directory}: {str(e)}")
                console.print(f"[red]Error: Failed to create required directory {directory}[/red]")


ensure_required_directories()


def print_error(e: Exception, message: str) -> None:
    """Prints error messages with consistent formatting."""
    console.print(f"[red]{message}: {e}[/red]")


class StatusPrinter:
    """Prints status messages with consistent formatting."""

    @staticmethod
    def print_status(message: str, style: str = "default") -> None:
        """Prints status messages with consistent formatting."""
        # Clear previous line first to avoid overwriting issues with progress bars etc.
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[{style}]{message}[/{style}]")


def shorten_order_id(order_id: str) -> str:
    """Shorten the order ID to the last 6 characters."""
    return order_id[-6:]


def clear_transient_line() -> None:
    """Clear the current line without using ANSI escape codes."""
    # Use console width for better clearing
    console.print("\r" + " " * console.width, end="\r")


def log_clean_success(message: str, max_length: int = 60) -> None:
    """Log success messages with line clearing and truncation."""
    truncated: str = (message[:max_length] + "..") if len(message) > max_length else message
    clear_transient_line()
    console.print(f"[green bold][+][/] {truncated}")


# --- Modified Logging Functions ---


def log_info(message: str, transient: bool = True, add_newline: bool = True) -> None:
    """Log info messages, ensuring proper line clearing."""
    logging.info(message)
    if transient:
        # Clear the line first
        console.print("\r" + " " * console.width, end="\r")
        # Print the message with carriage return
        console.print(f"[blue bold][+][/] [blue bold]{message}[/]", end="\r")
    else:
        # Clear potential previous transient message before printing permanently
        console.print("\r" + " " * console.width, end="\r")
        end_char: str = "\n" if add_newline else ""  # Ensure newline by default
        console.print(f"[blue bold][+][/] [blue bold]{message}[/]", end=end_char)
        if add_newline:  # If newline was added, print it explicitly
            console.print("")


def log_success(message: str, transient: bool = True, add_newline: bool = True) -> None:
    """Log success messages, ensuring proper line clearing."""
    logging.info(message)
    if transient:
        # Clear the line first
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[green bold][+][/] [green bold]{message}[/]", end="\r")
    else:
        # Clear potential previous transient message before printing permanently
        console.print("\r" + " " * console.width, end="\r")
        end_char: str = "\n" if add_newline else ""  # Ensure newline by default
        console.print(f"[green bold][+][/] [green bold]{message}[/]", end=end_char)
        if add_newline:  # If newline was added, print it explicitly
            console.print("")


def log_error(message: str) -> None:
    """Log an error message to the console in red."""
    clear_transient_line()  # Clear any transient line before showing an error

    # Escape any Rich markup in the message (square brackets)
    # This prevents Rich from trying to interpret markup in exception messages
    escaped_message = str(message).replace("[", r"\[").replace("]", r"\]")

    console.print(f"[bold red]ERROR:[/bold red] {escaped_message}")
    logging.error(message)  # Log original message to file without markup


def log_warning(message: str, transient: bool = False) -> None:
    """Log warning messages, ensuring proper line clearing."""
    logging.warning(message)
    if transient:
        # Clear the line first
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[yellow bold][!][/] [yellow bold]{message}[/]", end="\r")  # Changed [+] to [!]
    else:
        # Clear potential previous transient message before printing permanently
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[yellow bold][!][/] [yellow bold]{message}[/]")  # Changed [+], add newline implicitly


def log_debug(message: str, transient: bool = True) -> None:
    """Log debug messages, ensuring proper line clearing."""
    logging.debug(message)
    if transient:
        # Clear the line first
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[dim][+][/] [dim]{message}[/]", end="\r")
    else:
        # Clear potential previous transient message before printing permanently
        console.print("\r" + " " * console.width, end="\r")
        console.print(f"[dim][+][/] [dim]{message}[/]")


# --- End of Modified Logging Functions ---


def close_all_tabs_except_main(driver: uc.Chrome) -> None:
    """Close all tabs except the main order list tab."""
    try:
        main_window: str = driver.window_handles[0]
        current_handles: List[str] = driver.window_handles

        if len(current_handles) == 1:
            return

        log_info(f"Closing {len(current_handles) - 1} extra tabs...", transient=True)
        # Loop through tabs in reverse order (to avoid index issues when closing)
        for handle in reversed(current_handles):
            if handle != main_window:
                try:
                    driver.switch_to.window(handle)
                    driver.close()
                except Exception as e:
                    log_warning(f"Failed to close tab {handle[:8]}: {e}", transient=True)  # Make warning transient

        driver.switch_to.window(main_window)
        log_success("Extra tabs closed.", transient=True)
    except Exception as e:
        log_error(f"Error during tab cleanup: {e}")
        # Attempt to switch back to main window if possible
        try:
            if driver.window_handles:
                driver.switch_to.window(driver.window_handles[0])
        except Exception:
            pass


def create_driver(force_headless: bool = False) -> uc.Chrome:
    """Create and configure Chrome WebDriver with undetected-chromedriver."""
    # Get latest configuration values
    try:
        from config_module import get_config

        global HEADLESS_MODE
        HEADLESS_MODE = get_config(for_banned=False).get("HEADLESS_MODE", HEADLESS_MODE)
    except Exception as e:
        logging.error(f"Failed to refresh configuration for driver: {e}")
        # Continue with existing values

    options: uc.ChromeOptions = uc.ChromeOptions()

    # --- PASSWORD SAVE DISABLE (RE-VERIFIED & ENHANCED) ---
    prefs = {
        "credentials_enable_service": False,  # Disable credential saving prompts.
        "profile.password_manager_enabled": False,  # Disable password manager.
    }
    options.add_experimental_option("prefs", prefs)
    options.add_argument("--disable-save-password-bubble")  # Chrome argument to disable password bubble
    logger.debug("Password save prompts DISABLED via Chrome Preferences and Arguments.")
    # --- END PASSWORD SAVE DISABLE ---

    browser_path: Optional[str] = None
    for path in BROWSER_PATHS:
        if os.path.exists(path):
            browser_path = path
            log_info(f"Using browser at: {path}", transient=True)
            break

    if browser_path:
        options.binary_location = browser_path
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-infobars")
    options.add_argument("--mute-audio")
    options.add_argument(
        "--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) "
        "AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1"
    )
    cookies_ext_path: str = os.path.join(os.path.dirname(__file__), "cookie_editor")
    options.add_argument(f"--load-extension={cookies_ext_path}")
    if force_headless or HEADLESS_MODE:
        options.add_argument("--headless=new")
        log_info(
            f"Running in headless mode: force={force_headless}, config={HEADLESS_MODE}",
            transient=False,  # Keep this non-transient for startup info
        )
    try:
        driver: uc.Chrome = uc.Chrome(options=options)
        driver.set_window_size(WINDOW_WIDTH, WINDOW_HEIGHT)
        return driver
    except Exception as e:
        log_error(f"Failed to create driver: {str(e)}")
        raise


def cleanup_driver(driver: uc.Chrome) -> None:
    """Safely quit the browser."""
    try:
        if driver:
            driver.quit()
            log_info("Safely quit browser.")  # Keep non-transient
    except Exception:
        log_error("Did not safely quit browser.")
    finally:
        sys.exit(0)


def _get_elements_by_tag(
    parent: Union[uc.Chrome, uc.WebElement],
    tag_name: str,
    timeout: int = PAGE_LOAD_TIMEOUT,
) -> List[WebElement]:
    """Get all elements with the specified tag."""
    try:
        if isinstance(parent, uc.Chrome):
            return parent.find_elements(By.TAG_NAME, tag_name)
        else:
            return parent.find_elements(By.TAG_NAME, tag_name)
    except Exception as e:
        log_warning(f"Error finding elements by tag: {e}", transient=True)  # Make transient
        return []


def get_element_from_text(
    parent: Union[uc.Chrome, WebElement],
    tag_name: str,
    text: str,
    exact: bool = True,
    timeout: int = PAGE_LOAD_TIMEOUT,
) -> Optional[WebElement]:
    """Find element by tag and text, with a timeout and error handling."""
    try:
        wait: WebDriverWait = WebDriverWait(parent, timeout)
        all_tag_elements: List[WebElement] = wait.until(
            lambda driver: (
                parent.find_elements(By.TAG_NAME, tag_name)
                if isinstance(parent, WebElement)
                else driver.find_elements(By.TAG_NAME, tag_name)
            )
        )
        for element in all_tag_elements:
            try:
                if (exact and element.text == text) or (not exact and text in element.text):
                    return element
            except StaleElementReferenceException:
                continue
        return None
    except Exception as e:
        log_warning(f"Error while trying to get element with text: {e}", transient=True)  # Make transient
        return None


def get_element_from_attribute(
    parent: Union[uc.Chrome, WebElement],
    tag_name: str,
    attribute_name: str,
    attribute_value: str,
    timeout: int = PAGE_LOAD_TIMEOUT,
) -> Optional[WebElement]:
    """Find an element by tag name and attribute."""
    try:
        wait: WebDriverWait = WebDriverWait(parent, timeout)
        element: WebElement = wait.until(
            EC.presence_of_element_located((By.CSS_SELECTOR, f"{tag_name}[{attribute_name}='{attribute_value}']"))
        )
        return element
    except TimeoutException:
        log_warning(
            f"Timeout: Could not find element with {attribute_name}='{attribute_value}'",
            transient=True,
        )  # Make transient
        return None
    except Exception as e:
        log_warning(f"Error finding element by attribute: {e}", transient=True)  # Make transient
        return None


def get_element_value(element: Optional[WebElement], attribute_name: str) -> Optional[str]:
    """Helper to get an element's attribute value or text."""
    try:
        return element.get_attribute(attribute_name) if element else None
    except Exception as e:
        log_warning(f"Error reading attribute {attribute_name}: {e}", transient=True)  # Make transient
        return None


class Order:
    """
    Represents a DoorDash order with methods for processing and tip management.
    """

    def __init__(self, order_element: uc.WebElement):
        self.id: str = "unknown"
        self.receipt_url: Optional[str] = None
        self.amount: float = 0.0
        self.is_cancelled: bool = False  # Specifically for "Order Cancelled" text
        self.is_refunded: bool = False  # Specifically for "Refund" text
        self.cancelled: bool = False  # Combined flag for backward compatibility
        self.url: Optional[str] = None

        # Initialization logic moved inside the try-except block
        try:
            # Check for cancelled/refunded orders first - crucial for eligibility
            # Be precise with the text search for better accuracy
            cancellation_texts = ["Order Cancelled", "Canceled", "Cancel"]
            for text in cancellation_texts:
                if get_element_from_text(order_element, "span", text, exact=False) is not None:
                    self.is_cancelled = True
                    break

            # Use a more comprehensive check for refunds - look in all text content of the order element
            refund_texts = ["Refund", "refund"]
            order_text = order_element.text.lower()
            self.is_refunded = any(refund_text.lower() in order_text for refund_text in refund_texts)

            # Double-check with specific element searches
            if not self.is_refunded:
                for text in refund_texts:
                    if (
                        get_element_from_text(order_element, "span", text, exact=False) is not None
                        or get_element_from_text(order_element, "div", text, exact=False) is not None
                    ):
                        self.is_refunded = True
                        break

            # Set the combined flag for backward compatibility
            self.cancelled = self.is_cancelled or self.is_refunded
        except Exception as e:
            # Handle exceptions from the cancellation/refund check
            log_error(f"Error checking order status: {str(e)}")
            self.is_cancelled = False
            self.is_refunded = False
            self.cancelled = False

        # Initialization logic moved inside a new try-except block
        try:
            for attempt in range(MAX_RETRIES):
                try:
                    links: List[WebElement] = order_element.find_elements(By.TAG_NAME, "a")
                    if not links:
                        raise ValueError("No links found in order element")

                    self.receipt_url = links[-1].get_attribute("href")
                    if not self.receipt_url:
                        raise ValueError("Empty receipt URL")

                    self.id = self.receipt_url.split("/orders/")[-1].replace("/receipt/", "").split("?")[0]

                    amount_element: Optional[WebElement] = get_element_from_text(
                        order_element, "span", " item", exact=False
                    )
                    if not amount_element:
                        raise ValueError("Amount element not found")

                    amount_text: str = amount_element.text.split(" • ")[1].replace("$", "")
                    self.amount = float(amount_text)

                    self.cancelled = any(
                        bool(get_element_from_text(order_element, "span", text, exact=False))
                        for text in ["Order Cancelled", "Refund"]
                    )

                    self.url = f"https://doordash.com/orders/{self.id}/help/"
                    break  # Exit loop if successful
                except (StaleElementReferenceException, ValueError, IndexError) as e:
                    if attempt == MAX_RETRIES - 1:
                        raise  # Re-raise exception if max retries reached
                    log_warning(
                        f"Retry {attempt + 1}/{MAX_RETRIES} for order {self.id}: {str(e)}", transient=True
                    )  # Make transient
                    # Refresh the parent of the order_element, not the order_element itself
                    order_element.parent.refresh()
                    time.sleep(RETRY_OPERATION_WAIT)
        except Exception as e:
            # Removed self.id from log since it might not be initialized
            log_error(f"Failed to initialize order: {str(e)}")

    def has_tip(self) -> bool:
        return self.amount > MAX_TIP_THRESHOLD if self.amount else False

    def __str__(self) -> str:
        cancelled_status: str = "❌" if self.cancelled else "✅"
        return f"Order #{shorten_order_id(self.id)} | 💰 ${self.amount:.2f} | Status: {cancelled_status}"

    def remove_tip(
        self,
        driver: uc.Chrome,
        index: int,
        total: int,
        test_mode: bool = None,  # Keep parameter for backward compatibility
        create_new_tab: bool = True,
    ) -> bool:  # Changed return type to bool for consistency with process_single_order
        """Process an order to remove the tip with improved error handling."""
        try:
            original_window: str = driver.current_window_handle
            if create_new_tab:
                driver.switch_to.new_window("tab")
                logger.debug(f"Opened new tab for order {shorten_order_id(self.id)}")

            # Open the support chat with retry logic
            logger.info(f"Opening support chat for order {shorten_order_id(self.id)}")
            if not self.open_support_chat(driver):
                logger.error(f"Failed to open support chat for order {shorten_order_id(self.id)}")
                log_error(f"Failed to open support chat for order {shorten_order_id(self.id)}")
                return False

            # Wait after opening chat before sending message
            time.sleep(SHORT_ACTION_WAIT * 2)  # Double the wait time for slow connections

            # Send the initial message
            message: str = self.get_remove_tip_message()
            logger.info(f"Sending initial message for order {shorten_order_id(self.id)}")
            if not self.send_message_to_support(message, driver):
                logger.error(f"Failed to send initial message for order {shorten_order_id(self.id)}")
                log_error(f"Failed to send initial message for order {shorten_order_id(self.id)}")
                return False

            # Wait between messages
            time.sleep(NORMAL_ACTION_WAIT * 2)  # Double the wait time for slow connections

            # Send the agent trigger message
            agent_message: str = "assadasfsfbafascascadae" if TEST_MODE else "Agent"
            logger.info(f"Sending agent trigger message for order {shorten_order_id(self.id)}")
            if not self.send_message_to_support(agent_message, driver):
                logger.warning(f"Failed to send agent trigger for order {shorten_order_id(self.id)}")
                log_warning(f"Failed to send agent trigger for order {shorten_order_id(self.id)}")
                # Consider this non-critical - continue processing

            # Wait after sending all messages
            time.sleep(NORMAL_ACTION_WAIT * 2)  # Double the wait time for slow connections

            # Switch back to original window
            try:
                driver.switch_to.window(original_window)
            except Exception as switch_error:
                logger.error(f"Error switching back to original window: {switch_error}")
                logger.exception("Full traceback:")
                # Try to switch to any available window
                if driver.window_handles:
                    driver.switch_to.window(driver.window_handles[0])

            # Log success
            log_success(
                f"Successfully processed order #{shorten_order_id(self.id)} ({index + 1}/{total})",
                transient=False,  # Keep this non-transient
                add_newline=True,
            )

            return True

        except TimeoutException as timeout_err:
            logger.error(f"Timeout processing order {shorten_order_id(self.id)}: {str(timeout_err)}")
            logger.exception("Timeout traceback:")
            log_error(f"Timeout processing order {shorten_order_id(self.id)}: {str(timeout_err)}")

            # Try to switch back to original window
            try:
                if "original_window" in locals() and original_window in driver.window_handles:
                    driver.switch_to.window(original_window)
            except Exception:
                pass

            return False

        except (ElementNotInteractableException, NoSuchElementException) as elem_err:
            logger.error(f"Element error processing order {shorten_order_id(self.id)}: {str(elem_err)}")
            logger.exception("Element error traceback:")
            log_error(f"Element error processing order {shorten_order_id(self.id)}: {str(elem_err)}")

            # Try to switch back to original window
            try:
                if "original_window" in locals() and original_window in driver.window_handles:
                    driver.switch_to.window(original_window)
            except Exception:
                pass

            return False

        except WebDriverException as driver_err:
            logger.error(f"WebDriver error processing order {shorten_order_id(self.id)}: {str(driver_err)}")
            logger.exception("WebDriver error traceback:")
            log_error(f"WebDriver error processing order {shorten_order_id(self.id)}: {str(driver_err)}")

            # Try to switch back to original window
            try:
                if "original_window" in locals() and original_window in driver.window_handles:
                    driver.switch_to.window(original_window)
            except Exception:
                pass

            return False

        except Exception as e:
            logger.error(f"Error during remove_tip for order {shorten_order_id(self.id)}: {str(e)}")
            logger.exception("Full exception traceback:")
            log_error(f"Error during remove_tip for order {shorten_order_id(self.id)}: {str(e)}")

            # Try to switch back to original window
            try:
                if "original_window" in locals() and original_window in driver.window_handles:
                    driver.switch_to.window(original_window)
            except Exception:
                pass

            return False

    def scroll_and_prepare_element(self, driver: uc.Chrome, element: WebElement, wait_duration: float) -> None:
        """Helper method to scroll to an element and wait, avoiding variable shadowing issues."""
        driver.execute_script(
            "arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});",
            element,
        )
        time.sleep(wait_duration)

    def open_support_chat(self, driver: uc.Chrome) -> bool:  # Changed return type to bool for consistency
        """Open the support chat window for this order."""
        chat_wait_time = 1.0  # Increased from 0.5 to allow more time for page load

        try:
            if self.url is None:
                log_error(f"Cannot open support chat for order {self.id}: URL is None")
                return False

            driver.get(self.url)
            time.sleep(chat_wait_time)

            # Create a local function to find and click buttons to avoid code duplication
            def find_and_click_button(button_label, xpath_aria_label, xpath_text_contains):
                """Helper function to find and click buttons by aria-label or text content."""
                for attempt in range(MAX_RETRIES):
                    try:
                        # Try finding by aria-label first (using double quotes in XPath to handle single quotes)
                        buttons = driver.find_elements(By.XPATH, f'//button[@aria-label="{xpath_aria_label}"]')

                        # If not found, try by text content
                        if not buttons:
                            buttons = driver.find_elements(
                                By.XPATH,
                                f'//button[contains(text(), "{xpath_text_contains}")]',
                            )

                        # Also look for buttons with span children containing the text
                        if not buttons:
                            buttons = driver.find_elements(
                                By.XPATH,
                                f'//button[.//span[contains(text(), "{xpath_text_contains}")]]',
                            )

                        for button in buttons:
                            if button.is_displayed():
                                # First scroll the button into view
                                driver.execute_script(
                                    "arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});",
                                    button,
                                )
                                # Wait after scrolling but before clicking
                                time.sleep(1.0)  # Increased from 0.5

                                # Try multiple click methods in sequence for better reliability
                                try:
                                    # First try JavaScript click (most reliable)
                                    driver.execute_script("arguments[0].click();", button)
                                except Exception:
                                    try:
                                        # Then try regular click
                                        button.click()
                                    except Exception:
                                        # Finally try Actions click
                                        ActionChains(driver).move_to_element(button).click().perform()

                                # Wait after clicking to allow page to respond
                                time.sleep(2.0)  # Increased from 1.0
                                log_info(f"Clicked {button_label} button", transient=True)
                                return True

                        # No visible button found in this attempt
                        # log_warning(
                        #     f"No visible {button_label} button found (Attempt {attempt + 1}/{MAX_RETRIES})",
                        #     transient=True,
                        # )

                        # Use progressive backoff for retries
                        retry_wait = 2.0 + (attempt * 1.5)  # Increases with each attempt
                        time.sleep(retry_wait)

                    except Exception as e:
                        log_warning(
                            f"Attempt {attempt + 1}/{MAX_RETRIES} to click {button_label} button failed: {str(e)}",
                            transient=True,
                        )
                        # Use progressive backoff for retries
                        retry_wait = 2.0 + (attempt * 1.5)  # Increases with each attempt
                        time.sleep(retry_wait)

                log_error(f"Failed to click {button_label} button after {MAX_RETRIES} attempts")
                return False

            # Click "Something else" button
            if not find_and_click_button("Something else", "It's something else", "something else"):
                return False

            # Click "Contact support" button
            if not find_and_click_button("Contact support", "Contact support", "Contact support"):
                return False

            return True  # Successfully opened support chat

        except Exception as error:
            log_error(f"Error in open_support_chat for order {shorten_order_id(self.id)}: {str(error)}")
            # Log the full exception details to the log file for debugging
            logger.exception(f"Full traceback for order {shorten_order_id(self.id)} support chat error:")
            return False

    def get_remove_tip_message(self) -> str:
        global customer_name, customer_email
        messages: List[str] = [
            "Please remove the dasher tip to $0",
            "Hey, pls remove the tip and adjust it to $0",
            "Hi, i want you to remove whole dasher tip and make it $0",
            "Hey, remove full dasher tip and make it $0 pls. Application is glitching and it charged my card twice for the tip idk what is happening",
            "hey remove dasher's tip and adjust to $0",
            "Can you change the dasher tip to $0? Thanks so much!",
            "Hi there! Need to remove the dasher tip completely - $0 please",
            "Hey, the app glitched - could you zero out the dasher tip for me?",
            "Oops, wrong tip amount! Can you make dasher tip $0 instead?",
            "Need to fix my order - set the dasher tip to $0 please & thx!",
        ]
        message: str = random.choice(messages)
        # No logging needed here, logged when sent
        return f"{customer_name}\n{customer_email}\n\n{message}"

    def send_message_to_support(self, message: str, driver: uc.Chrome) -> bool:
        text_input_locator: tuple = (By.TAG_NAME, "textarea")

        # Progressive backoff for retries
        retry_delays = [1, 3, 7]  # Increasing delays between attempts

        for attempt in range(MAX_RETRIES):
            try:
                # Use longer timeout for finding the text input element
                text_input_element: WebElement = WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(
                    EC.presence_of_element_located(text_input_locator)
                )
                WebDriverWait(driver, PAGE_LOAD_TIMEOUT * 2).until(EC.element_to_be_clickable(text_input_locator))

                # Scroll to ensure the text area is in view
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", text_input_element)
                time.sleep(0.5)  # Wait after scrolling

                # Clear the input field and wait
                text_input_element.clear()
                time.sleep(SHORT_ACTION_WAIT)

                # Use a more reliable approach to enter the message
                lines: List[str] = message.split("\n")
                for i, line in enumerate(lines):
                    # First try standard send_keys
                    try:
                        text_input_element.send_keys(line)
                    except Exception as e:
                        # If that fails, try JavaScript fallback
                        log_warning(f"Standard input failed, using JS fallback: {str(e)}", transient=True)
                        current_value = text_input_element.get_attribute("value") or ""
                        driver.execute_script(
                            f"arguments[0].value = '{current_value + line.replace("'", "\\'")}'", text_input_element
                        )

                    # Add newline between lines (not for the last line)
                    if i < len(lines) - 1:
                        try:
                            ActionChains(driver).key_down(Keys.SHIFT).send_keys(Keys.ENTER).key_up(Keys.SHIFT).perform()
                        except Exception:
                            # Fallback to JS newline insertion if ActionChains fails
                            current_value = text_input_element.get_attribute("value") or ""
                            driver.execute_script(f"arguments[0].value = '{current_value}\\n'", text_input_element)

                    # Brief pause between lines for stability
                    time.sleep(0.1)

                # Send the message with Enter key - try multiple methods
                try:
                    # First try standard send_keys
                    text_input_element.send_keys(Keys.RETURN)
                except Exception:
                    try:
                        # Then try ActionChains
                        ActionChains(driver).key_down(Keys.RETURN).key_up(Keys.RETURN).perform()
                    except Exception:
                        # Finally try JavaScript submit event
                        driver.execute_script(
                            "arguments[0].dispatchEvent(new KeyboardEvent('keydown', {'key': 'Enter'}));",
                            text_input_element,
                        )

                # log_debug(f"Sent message: {message[:50]}...", transient=True)  # Use debug + transient
                time.sleep(NORMAL_MESSAGE_WAIT * 2)  # Double the wait time after sending
                return True

            except StaleElementReferenceException:
                log_warning(f"Text area became stale (Attempt {attempt + 1}/{MAX_RETRIES})", transient=True)
                # Refresh the page on stale element
                try:
                    driver.refresh()
                except Exception as refresh_error:
                    log_warning(f"Error refreshing page: {str(refresh_error)}", transient=True)

                # Use progressive backoff
                retry_delay = retry_delays[min(attempt, len(retry_delays) - 1)]
                log_warning(f"Waiting {retry_delay}s before retry {attempt + 1}", transient=True)
                time.sleep(retry_delay)

            except TimeoutException:
                log_warning(f"Attempt {attempt + 1}/{MAX_RETRIES} to send message timed out", transient=True)
                # Log the full traceback to the logger but not to console
                logger.error(f"Timeout exception details for attempt {attempt + 1}:")
                logger.exception("Exception details:")

                # Use progressive backoff
                retry_delay = retry_delays[min(attempt, len(retry_delays) - 1)]
                log_warning(f"Waiting {retry_delay}s before retry {attempt + 1}", transient=True)
                time.sleep(retry_delay)

                if attempt == MAX_RETRIES - 1:
                    log_error(f"Failed to send message after {MAX_RETRIES} attempts")
                    return False

                # Try to refresh the page
                try:
                    driver.refresh()
                    time.sleep(RETRY_OPERATION_WAIT)
                except Exception as refresh_error:
                    log_warning(f"Error refreshing page: {str(refresh_error)}", transient=True)

            except Exception as e:
                log_warning(f"Attempt {attempt + 1}/{MAX_RETRIES} to send message failed: {str(e)}", transient=True)
                # Log the full traceback to the logger but not to console
                logger.error(f"Exception details for attempt {attempt + 1}:")
                logger.exception("Exception details:")

                # Use progressive backoff
                retry_delay = retry_delays[min(attempt, len(retry_delays) - 1)]
                log_warning(f"Waiting {retry_delay}s before retry {attempt + 1}", transient=True)
                time.sleep(retry_delay)

                if attempt == MAX_RETRIES - 1:
                    log_error(f"Failed to send message after {MAX_RETRIES} attempts")
                    return False

                # Try to refresh the page
                try:
                    driver.refresh()
                    time.sleep(RETRY_OPERATION_WAIT)
                except Exception as refresh_error:
                    log_warning(f"Error refreshing page: {str(refresh_error)}", transient=True)

        return False


def get_orders(driver: uc.Chrome, max_orders: int = MAX_ORDERS_PER_BATCH) -> List[Order]:
    """Retrieve orders with improved loading and error handling."""
    console.print("")  # Add space before starting
    log_info("Starting order collection...", transient=True)  # Make transient

    try:
        driver.get(url="https://www.doordash.com/orders")
        time.sleep(SCROLL_WAIT)  # Give more initial load time

        scroll_count = 0
        last_height = driver.execute_script("return document.body.scrollHeight")
        no_change_count = 0
        MAX_NO_CHANGE = 5  # Increased from 3 to be more patient
        consecutive_errors = 0
        start_time = time.time()
        MAX_LOAD_TIME = 60  # Maximum time to spend loading orders (in seconds)

        # Use a single Progress instance for this function call
        with Progress(
            *get_progress_bar_style_no_percentage(),
            refresh_per_second=10,
            transient=True,  # Use no percentage style for loading orders
        ) as progress:
            scroll_task = progress.add_task("[cyan]Loading orders...", total=None)

            while scroll_count < MAX_SCROLL_ATTEMPTS and scroll_count < MAX_PAGES_TO_LOAD:
                # Check if we've been loading for too long
                if time.time() - start_time > MAX_LOAD_TIME:
                    log_info(
                        f"Order loading time limit reached ({MAX_LOAD_TIME}s).",
                        transient=True,  # Make transient
                    )
                    break

                try:
                    # Force scroll to bottom with JavaScript
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(SCROLL_WAIT)

                    # Try to find and click "Load More"
                    try:
                        load_more = WebDriverWait(driver, 2).until(
                            EC.presence_of_element_located((By.XPATH, "//span[contains(text(), 'Load More')]"))
                        )
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more)
                        load_more.click()
                        scroll_count += 1
                        no_change_count = 0  # Reset counter on successful click
                        progress.update(
                            scroll_task,
                            description=f"[cyan]Loading orders (page {scroll_count})...",
                        )
                        continue
                    except TimeoutException:
                        # Check if we've reached the end
                        new_height = driver.execute_script("return document.body.scrollHeight")
                        if new_height == last_height:
                            no_change_count += 1
                            if no_change_count >= MAX_NO_CHANGE:
                                log_info("Reached end of order list", transient=True)  # Make transient
                                break
                        else:
                            no_change_count = 0
                            last_height = new_height
                            continue  # Continue scrolling/checking

                except Exception as e:
                    consecutive_errors += 1
                    log_warning(f"Scroll error: {str(e)}", transient=True)  # Make transient
                    break  # Stop scrolling on error

        # --- Removed progress.stop() here, context manager handles it ---

        # Verify orders were loaded
        try:
            wait = WebDriverWait(driver, PAGE_LOAD_TIMEOUT)
            completed_span = wait.until(EC.presence_of_element_located((By.XPATH, "//span[text()='Completed']")))

            orders_container = completed_span.find_element(By.XPATH, "..")
            orders_div = orders_container.find_element(By.XPATH, "./div[last()]")
            all_order_elements = orders_div.find_elements(By.XPATH, "./*")

            if not all_order_elements:
                log_warning("No order elements found on page.", transient=True)  # Make transient
                # driver.refresh() # Avoid refresh/recursion here
                # time.sleep(2)
                # return get_orders(driver, max_orders) # REMOVED Recursive retry
                return []  # Return empty, let main loop handle retries

            # Stop the scroll progress bar explicitly *before* starting the next one
            # (Though context manager should handle this)
            progress.stop()
            clear_transient_line()  # Ensure the loading bar line is cleared

            orders = process_orders_in_parallel(all_order_elements)
            return orders[:max_orders]

        except Exception as e:
            log_error(f"Error getting order elements: {str(e)}")
            return []

    except Exception as e:
        log_error(f"Error in order collection: {str(e)}")
        return []


class OrderProcessor:
    """Handles order processing with progress tracking."""

    def __init__(self) -> None:
        """Initialize OrderProcessor with progress bar and console."""
        # Make this progress bar transient as well
        self.progress = Progress(*get_progress_bar_style(), refresh_per_second=10, transient=True)
        self._lock = Lock()
        self.console = Console()

    def print_status(self, message: str, style: str = "default") -> None:
        """Print status message above the progress bar."""
        # This might interfere with transient progress bars, use standard logging instead
        # with self._lock:
        #     print("\033[s", end="")
        #     print("\033[2A", end="")
        #     print(f"\033[K{message}")
        #     print("\033[u", end="")
        pass  # Use log_info/log_success etc.

    @staticmethod
    def should_continue_loading(current_page: int) -> bool:
        """Determine if more pages should be loaded based on MAX_PAGES_TO_LOAD."""
        if current_page >= MAX_PAGES_TO_LOAD:
            log_info(
                f"Reached maximum page limit ({MAX_PAGES_TO_LOAD}). Stopping page loading.",
                transient=False,  # Keep non-transient
            )
            return False
        return True


class OrderStats:
    """Tracks and displays order statistics."""

    def __init__(self) -> None:
        self.total_orders: int = 0
        self.eligible_orders: int = 0

    def print_summary(self) -> None:
        clear_transient_line()  # Clear before printing panel

        # Create the two-column layout using a Table with invisible borders
        from rich.table import Table

        table = Table(show_header=True, box=None, padding=(0, 2))

        # Add headers to better distinguish the columns
        table.add_column("[bold cyan]Order Counts[/bold cyan]", style="white", justify="left")
        table.add_column("[bold yellow]Processing Settings[/bold yellow]", style="white", justify="left")

        # Add the data to the table with improved styling
        table.add_row(
            f"[white]Orders Found:[/white] {self.total_orders}\n"
            f"[white]Eligible Orders:[/white] {self.eligible_orders}",
            f"[white]Minimum Order $:[/white] ${MAX_TIP_THRESHOLD:.2f}\n"
            f"[white]Cancelled Eligible:[/white] {'Yes' if CANCELLED_ORDERS_ELIGIBLE else 'No'}\n"
            f"[white]Refunded Eligible:[/white] {'Yes' if REFUNDED_ORDERS_ELIGIBLE else 'No'}",
        )

        # Create panel with the table
        console.print()  # Add a newline before the panel
        console.print(
            Panel(
                table,
                title="[bold]Order Summary[/bold]",
                border_style=MAIN_COLOR,
                padding=(1, 2),
                width=60,
            )
        )
        console.print()


def process_orders_in_parallel(elements: Sequence[WebElement]) -> List[Order]:
    """
    Create Order objects in parallel, then filter for those that have a tip
    and are not cancelled.
    """
    processor: OrderProcessor = OrderProcessor()
    stats: OrderStats = OrderStats()

    # We'll track completed creations via a progress bar
    # This progress bar is now transient=True via OrderProcessor init
    with processor.progress as progress:
        task_id: int = progress.add_task("[bold cyan]Initializing orders...", total=len(elements))

        results: List[Order] = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_element = {executor.submit(Order, elem): elem for elem in elements}
            for future in concurrent.futures.as_completed(future_to_element):
                try:
                    order = future.result()
                    results.append(order)
                except Exception as e:
                    log_error(f"Error processing order element: {e}")
                progress.update(task_id, advance=1)

    # --- Progress bar stops here via context manager ---
    clear_transient_line()  # Ensure init bar line is cleared

    # Filter down to eligible orders
    eligible_orders: List[Order] = [
        o
        for o in results
        if o
        and o.has_tip()
        and (CANCELLED_ORDERS_ELIGIBLE or not o.is_cancelled)
        and (REFUNDED_ORDERS_ELIGIBLE or not o.is_refunded)
    ]

    stats.total_orders = len(elements)
    stats.eligible_orders = len(eligible_orders)
    stats.print_summary()  # Prints panel

    for idx, order in enumerate(eligible_orders, 1):
        # Create status indicators with checkmarks/crosses
        cancelled_status = "❌" if order.is_cancelled else "✅"
        refunded_status = "❌" if order.is_refunded else "✅"

        # Keep this non-transient
        console.print(
            f"✅ Found eligible order ({idx}/{len(eligible_orders)}): "
            f"Order #{shorten_order_id(order.id)} | 💰 ${order.amount:.2f} | "
            f"Not Cancelled: {cancelled_status} | Not Refunded: {refunded_status}"
        )

    console.print("")  # Add newline after list
    return eligible_orders


class DriverContextManager:
    """Context manager for WebDriver lifecycle."""

    def __init__(self) -> None:
        self.driver: Optional[uc.Chrome] = None

    def __enter__(self) -> uc.Chrome:
        self.driver = create_driver()
        return self.driver

    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                log_error(f"Error closing driver: {str(e)}")
        return False


class TabManager:
    """Manages browser tabs for reuse and cleanup."""

    def __init__(self) -> None:
        self.pending_tabs: List[str] = []
        self.main_window: Optional[str] = None
        self._lock: Lock = Lock()

    def set_pending_tabs(self, tabs: List[str], main_window: str) -> None:
        with self._lock:
            self.pending_tabs = tabs
            self.main_window = main_window

    def cleanup_tabs(self, driver: uc.Chrome) -> None:
        with self._lock:
            if not self.pending_tabs:
                return
            original_window: str = driver.current_window_handle
            for tab in self.pending_tabs:
                try:
                    driver.switch_to.window(tab)
                    driver.close()
                except Exception:
                    continue
            try:
                driver.switch_to.window(self.main_window or original_window)
            except Exception:
                try:
                    driver.switch_to.window(driver.window_handles[0])
                except IndexError:
                    pass
            self.pending_tabs = []
            self.main_window = None


tab_manager: TabManager = TabManager()


def get_agent_name(driver: uc.Chrome) -> Optional[str]:
    try:
        # Try the "connected to our agent NAME" pattern first
        agent_spans: List[WebElement] = driver.find_elements(
            By.XPATH, "//span[contains(text(), 'You are now connected to our')]"
        )
        if agent_spans:
            for span in agent_spans:
                try:
                    if span.is_displayed():
                        text: str = span.text
                        if "agent" in text.lower():
                            name_match = re.search(r"agent\s+([A-Za-z]+)", text, re.IGNORECASE)
                            if name_match:
                                name = name_match.group(1).strip()
                                if len(name) > 1:  # Avoid single letters
                                    log_debug(
                                        f"Extracted agent name (pattern 1): {name}",
                                        transient=True,
                                    )
                                    return name
                except StaleElementReferenceException:
                    continue

        # Try "This is NAME from DoorDash Support"
        support_messages: List[WebElement] = driver.find_elements(
            By.XPATH,
            "//span[starts-with(text(), 'This is') and contains(text(), 'from DoorDash Support')]",
        )
        if support_messages:
            for msg in support_messages:
                try:
                    if msg.is_displayed():
                        text = msg.text
                        name_match = re.search(r"This is\s+([A-Za-z]+)\s+from", text, re.IGNORECASE)
                        if name_match:
                            name = name_match.group(1).strip()
                            if len(name) > 1:
                                log_debug(
                                    f"Extracted agent name (pattern 2): {name}",
                                    transient=True,
                                )
                                return name
                except StaleElementReferenceException:
                    continue

        # Try "Hey/Hi/Hello..., this is NAME from..."
        greeting_messages: List[WebElement] = driver.find_elements(
            By.XPATH,
            "//span[(starts-with(text(), 'Hey') or starts-with(text(), 'Hi') or starts-with(text(), 'Hello')) and contains(text(), 'this is') and contains(text(), 'DoorDash Support')]",
        )
        if greeting_messages:
            for msg in greeting_messages:
                try:
                    if msg.is_displayed():
                        text: str = msg.text
                        name_match = re.search(r"this is\s+([A-Za-z]+)\s+from", text, re.IGNORECASE)
                        if name_match:
                            name = name_match.group(1).strip()
                            if len(name) > 1:
                                log_debug(
                                    f"Extracted agent name (pattern 3): {name}",
                                    transient=True,
                                )
                                return name
                except StaleElementReferenceException:
                    continue

    except Exception as e:
        log_debug(f"Failed to extract agent name: {e}", transient=True)

    return None


def get_agent_message(agent_name: str) -> str:
    global customer_name, customer_email
    if TEST_MODE:
        return get_agent_message_testmode(agent_name)

    message: str = (
        f"Hi {agent_name},\n"
        f"Yeah, please\n"
        f"change the tip to $0.\n\n"
        f"My account is:\n"
        f"{customer_name}\n"
        f"{customer_email}\n\n"
        f"Thx, {customer_name}!\n"
    )
    # log_info(f"Generated agent message for {agent_name}: {message[:10]}...", transient=True)  # Keep transient
    return message


def get_agent_message2(agent_name: str) -> str:
    if TEST_MODE:
        return get_agent_message_testmode(agent_name)

    message: str = (
        f"Account info:\n"
        f"{customer_name}\n"
        f"{customer_email}\n\n"
        f"Yeah, please\n"
        f"Tip to $0.\n\n"
        f"Thx, {agent_name}!\n"
        f"5* service!\n"
    )
    # log_info(f"Generated agent message for {agent_name}: {message[:10]}...", transient=True)  # Keep transient
    return message


def get_agent_message_testmode(agent_name: str) -> str:
    import random as agent_random

    messages: List[str] = [
        f"Hi {agent_name},\n\n could you help me understand the process for tip adjustments?\n\nYou can END the chat now..",
        f"Hello {agent_name},\n\n I'm researching how support handles tip-related inquiries for an article I'm writing.\n\nYou can END the chat now...",
        f"Thanks {agent_name}!\n\n I'm just curious about the policy details, no specific actions needed.\n\nYou can END the chat now....",
    ]
    selected_message: str = agent_random.choice(messages)
    log_debug(
        f"Generated test mode agent message for {agent_name}: {selected_message[:10]}...",
        transient=True,
    )  # Keep transient
    return selected_message


def send_agent_message(
    driver: uc.Chrome, text_area: Union[uc.WebElement, WebElement], message: str, agent_name: Optional[str] = None
) -> bool:
    try:
        text_area.clear()
        time.sleep(0.1)
        text_area.click()
        time.sleep(0.1)

        driver.execute_script("arguments[0].value = '';", text_area)
        for line in message.split("\n"):
            text_area.send_keys(line)
            text_area.send_keys(Keys.SHIFT, Keys.ENTER)
            time.sleep(0.05)

        time.sleep(0.1)
        text_area.send_keys(Keys.RETURN)
        agent_display = agent_name if agent_name else "agent"
        log_info(f"Sent message to {agent_display}", transient=True)  # Make this transient info log
        return True
    except Exception as e:
        # print_error(e, "Failed to send agent message") # Redundant
        log_error(f"Failed to send agent message: {e}")  # log_error clears line and is non-transient
        return False


def check_and_message_agents(driver: uc.Chrome, use_alternate_message: bool = False) -> None:
    original_handle: str = driver.current_window_handle
    stats: Dict[str, int] = {"checked": 0, "sent": 0, "errors": 0, "reconnected": 0}

    try:
        handles: List[str] = driver.window_handles[1:]  # Exclude main tab
        total_tabs: int = len(handles)

        if total_tabs == 0:
            log_info("No secondary tabs open to check for agents.", transient=False)
            return

        # Use log_info for the initial status, but make it transient
        log_info(f"Checking {total_tabs} tabs for agents/reconnect...", transient=True)
        time.sleep(0.5)  # Small delay to let the message show

        for idx, handle in enumerate(handles, 1):
            stats["checked"] = idx
            current_tab_processed = False  # Flag to prevent multiple logs per tab
            try:
                driver.switch_to.window(handle)
                # log_debug(f"Switched to tab: {handle[:8]}...", transient=True) # Debug level maybe too noisy

                progress: str = f"[{idx}/{total_tabs}]"
                # Transient log for current tab check
                log_info(f"{progress} Checking tab {handle[:8]}...", transient=True)

                combined_button_xpath: str = (
                    "//button[.//span[normalize-space(text())='Reconnect with an Agent' "
                    "or normalize-space(text())='Chat with an agent']]"
                )
                buttons: List[WebElement] = driver.find_elements(By.XPATH, combined_button_xpath)

                if buttons and any(btn.is_displayed() for btn in buttons):
                    for button in buttons:
                        if button.is_displayed():
                            try:
                                log_info(
                                    f"{progress} Found reconnect button in tab {handle[:8]}...",
                                    transient=True,
                                )
                                scroll_and_wait(driver, button, SHORT_ACTION_WAIT)
                                try:
                                    driver.execute_script("arguments[0].click();", button)
                                except Exception as e:
                                    log_debug(
                                        f"JavaScript click failed: {e}, trying regular click",
                                        transient=True,
                                    )
                                    button.click()
                                stats["reconnected"] += 1
                                log_success(
                                    f"{progress} Reconnected in tab {handle[:8]}",
                                    transient=True,  # Make transient
                                )
                                current_tab_processed = True
                                time.sleep(1)  # Shorter wait after reconnect
                                break  # Processed this tab
                            except Exception as e:
                                log_error(f"{progress} Failed to click reconnect button in tab {handle[:8]}: {e}")
                    if current_tab_processed:
                        continue  # Move to next tab

                agent_name: Optional[str] = get_agent_name(driver)
                if agent_name and not current_tab_processed:
                    log_info(
                        f"{progress} Found agent: {agent_name} in tab {handle[:8]}",
                        transient=True,
                    )
                    message: str = (
                        get_agent_message2(agent_name) if use_alternate_message else get_agent_message(agent_name)
                    )  # Logs transiently

                    text_area: Optional[WebElement] = None
                    try:
                        text_area_selectors: List[tuple] = [
                            (By.TAG_NAME, "textarea"),
                            (By.CSS_SELECTOR, "textarea[placeholder]"),
                            (By.CSS_SELECTOR, "textarea.text-input"),
                            (By.XPATH, "//textarea[contains(@placeholder, 'Type')]"),
                        ]
                        for selector in text_area_selectors:
                            try:
                                text_area = WebDriverWait(driver, 1).until(
                                    EC.presence_of_element_located(selector)
                                )  # Short wait
                                if text_area and text_area.is_displayed():
                                    break
                            except (
                                NoSuchElementException,
                                TimeoutException,
                                StaleElementReferenceException,
                            ) as _:
                                continue
                    except Exception as e:
                        log_warning(
                            f"{progress} Error finding text area in tab {handle[:8]}: {e}",
                            transient=True,
                        )
                    if text_area:
                        if send_agent_message(driver, text_area, message, agent_name):  # Logs transiently on success
                            stats["sent"] += 1
                            # The success log is now transient within send_agent_message
                            current_tab_processed = True
                            current_tab_processed = True
                        else:
                            # Failure is logged non-transiently by send_agent_message
                            log_warning(
                                f"{progress} Failed to send message to agent {agent_name} in tab {handle[:8]}",
                                transient=False,
                            )  # Keep warning non-transient
                            current_tab_processed = True  # Still counts as processed (attempted)
                    else:
                        log_warning(
                            f"{progress} No text area found for agent {agent_name} in tab {handle[:8]}",
                            transient=True,
                        )  # Make transient

            except Exception as e:
                # Log errors non-transiently
                log_error(f"Error processing tab {handle[:8]}: {e}")
                stats["errors"] += 1
                current_tab_processed = True  # Count error as processed
                continue  # Move to next tab

            # If nothing was found/done, clear the transient "Checking tab..." message
            # if not current_tab_processed:
            #     clear_transient_line()
            # Let the next iteration's "Checking tab..." overwrite it naturally

        driver.switch_to.window(original_handle)
        # console.print("") ## Remove extra newline
        # Explicitly clear any final transient message from the loop before printing summary
        clear_transient_line()
        log_info(
            f"Tab check complete: {stats['checked']} tabs checked, {stats['reconnected']} reconnected, "
            f"{stats['sent']} message(s) sent, {stats['errors']} error(s)",
            transient=False,  # Keep summary non-transient
            add_newline=True,
        )

    except Exception as e:
        log_error(f"Critical error during tab checking: {e}")
        try:
            driver.switch_to.window(original_handle)
        except Exception as e_sw:
            log_error(f"Failed to switch back to original window: {e_sw}")


class SessionState:
    """Manages global session state and resources."""

    def __init__(self) -> None:
        self.display_lock: Lock = Lock()
        self.chat_queue: Queue[str] = Queue()
        self.active: bool = True
        self.sent_messages: Set[str] = set()

    def cleanup(self) -> None:
        self.active = False
        with self.display_lock:
            self.chat_queue = Queue()
            self.sent_messages.clear()


session: SessionState = SessionState()


def is_valid_cookie_file(file_path: str) -> bool:
    """Check if a cookie file exists and is not empty."""
    path: Path = Path(file_path)
    return path.exists() and path.stat().st_size > 0


def cookie_login_with_fallback(driver: uc.Chrome, cookies: List[Dict], timeout: int = 30) -> bool:
    """Attempt to log in using saved cookies with fallback mechanism."""
    clear_transient_line()  # Clear before starting
    log_info("Attempting login with saved cookies...", transient=True)

    try:
        driver.get("https://www.doordash.com/")
        cookies_added = 0
        for cookie in cookies:
            # Clean cookie domain if necessary
            domain = cookie.get("domain", "")
            if domain.startswith("."):
                cookie["domain"] = domain[1:]  # Remove leading dot for uc
            # Ensure secure attribute matches context if needed (might not be required for uc)
            # if 'secure' not in cookie: cookie['secure'] = False
            try:
                driver.add_cookie(cookie)
                cookies_added += 1
            except Exception as cookie_err:
                log_debug(
                    f"Could not add cookie {cookie.get('name')}: {cookie_err}",
                    transient=True,
                )

        log_info(f"Added {cookies_added} cookies", transient=True)
        driver.get("https://www.doordash.com/home")  # Navigate to a page that requires login

        with Progress(
            SpinnerColumn(),
            TextColumn("[cyan]Waiting for login confirmation...[/cyan]"),
            expand=True,
            transient=True,  # Make progress transient
        ) as progress:
            task: int = progress.add_task("login_wait", total=timeout)

            for _ in range(timeout):
                current_url = driver.current_url
                # Check for logged-in state (e.g., '/home' URL or absence of login elements)
                if "doordash.com/home" in current_url or "doordash.com/orders" in current_url:
                    # Check if login form is NOT present as extra confirmation
                    try:
                        driver.find_element(By.CSS_SELECTOR, 'input[type="email"][name="email"]')
                        # If found, we are likely still on login page
                    except NoSuchElementException:
                        # Email input not found, likely logged in
                        progress.stop()
                        clear_transient_line()
                        log_success("Cookie login successful!", transient=False)  # Keep non-transient
                        return True

                progress.update(task, advance=1)
                time.sleep(1)

        # Timeout occurred
        progress.stop()
        clear_transient_line()
        log_warning("Cookie login timed out or failed.", transient=False)  # Non-transient warning
        return False

    except Exception as error:
        clear_transient_line()
        log_error(f"Error during cookie login: {str(error)}")
        return False


def wait_for_profile_page(driver: uc.Chrome, max_attempts: int = 9999, infinite_wait: bool = False) -> bool:
    """Wait for the DoorDash profile page to load after login."""
    profile_url: str = "https://www.doordash.com/consumer/edit_profile/"
    home_url_part: str = "doordash.com/home"
    orders_url_part: str = "doordash.com/orders"
    attempts: int = 0

    # Use a progress spinner instead of repetitive text messages
    with Progress(
        SpinnerColumn(),
        TextColumn("[cyan]Waiting for DoorDash login to complete...[/cyan]"),
        TimeElapsedColumn(),
        expand=True,
        transient=True,  # Make progress transient
    ) as progress:
        task = progress.add_task("manual_login_wait", total=None)  # Indeterminate progress

        while infinite_wait or attempts < max_attempts:
            url: str = driver.current_url
            # Check against multiple logged-in URLs
            if profile_url in url or home_url_part in url or orders_url_part in url:
                # Extra check: ensure login form is gone
                try:
                    driver.find_element(By.CSS_SELECTOR, 'input[type="email"][name="email"]')
                    # Still on login page? Continue waiting.
                except NoSuchElementException:
                    # Login form not found, likely loggedin.
                    progress.stop()
                    clear_transient_line()
                    log_success("Login successful!", transient=False)  # Keep non-transient
                    return True

            # Optional: Click Sign In if it appears (sometimes needed after OTP?)
            elif "doordash.com" in url and "action=Login" in url and "/home/" not in url:
                try:
                    sign_in_button: WebElement = WebDriverWait(driver, 2).until(  # Short wait
                        EC.element_to_be_clickable((By.XPATH, "//button[.//span[contains(text(), 'Sign In')]]"))
                    )
                    log_info("Found 'Sign In' button, attempting click...", transient=True)
                    sign_in_button.click()
                    time.sleep(1)  # Wait a bit after click
                except (
                    TimeoutException,
                    ElementClickInterceptedException,
                    ElementNotInteractableException,
                ) as e:
                    log_debug(
                        f"Sign in element not found or not clickable: {str(e)}",
                        transient=True,
                    )

            # Only update the progress description occasionally to avoid flickering
            # if infinite_wait and attempts % 40 == 0 and attempts > 0:
            #     elapsed_time = progress.tasks[task].elapsed
            #     if elapsed_time:
            #          progress.update(
            #              task,
            #              description=f"[cyan]Still waiting for login... (elapsed: {elapsed_time:.0f}s)[/cyan]",
            #          )

            time.sleep(0.25)
            attempts += 1
            progress.update(task, advance=0)  # Keep spinner going

    progress.stop()
    clear_transient_line()
    log_error("Login wait timed out or failed.")
    return False


def save_cookies_after_login(driver: uc.Chrome, manual: bool = False, customer_info: Optional[dict] = None) -> None:
    """Save cookies after login and create backups as needed."""
    Path("cookiesBAK").mkdir(exist_ok=True)
    try:
        cookies: List[Dict] = driver.get_cookies()
        success: bool = False
        backup_success: bool = False
        if cookies:
            with open("cookies.pkl", "wb") as f:
                pickle.dump(cookies, f)
                success = True
            if manual:
                backup_success = create_manual_cookies_backup()
            elif (
                customer_info
                and customer_info.get("first_name")
                and customer_info.get("last_name")
                and customer_info.get("email")
            ):
                backup_success = create_cookies_backup(
                    backup_type="auto",
                    first_name=customer_info["first_name"],
                    last_name=customer_info["last_name"],
                    email=customer_info["email"],
                )
            else:
                # If auto and info missing, still try a generic auto backup
                backup_success = create_cookies_backup(backup_type="auto")

        notify_cookie_save_status(success, backup_success, manual)
    except Exception as error:
        logging.error(f"Cookie save error: {str(error)}")
        notify_cookie_save_status(False, False, manual)


def clean_string(s: str) -> str:
    """Clean a string by removing non-alphanumeric characters."""
    if not s:
        return ""
    return re.sub(r"[^a-zA-Z0-9]", "", s)


def create_cookies_backup(
    backup_type: str = "auto",
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    email: Optional[str] = None,
) -> bool:
    """Create a backup of the cookies file."""
    if not is_valid_cookie_file("cookies.pkl"):
        log_info("No valid cookies file to backup", transient=True)
        return False
    timestamp: str = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir: Path = Path("cookiesBAK")
    backup_dir.mkdir(exist_ok=True)

    # Construct backup name
    if backup_type == "manual":
        backup_name: str = f"manual_login_{timestamp}.pkl"
    elif backup_type == "auto":
        clean_fname: str = clean_string(first_name or "")
        clean_lname: str = clean_string(last_name or "")
        clean_email: str = clean_string(email or "").split("@")[0]  # Use part before @

        if clean_fname and clean_lname and clean_email:
            backup_name = f"ficookies_{clean_fname}_{clean_lname}_{clean_email}_{timestamp}.pkl"
        else:
            # Fallback name if info is incomplete/missing
            backup_name = f"auto_login_{timestamp}.pkl"
    else:
        log_error(f"Unknown backup type: {backup_type}")
        return False

    backup_path: Path = backup_dir / backup_name
    try:
        shutil.copy2("cookies.pkl", backup_path)
        # Only remove original on *successful manual* backup creation? No, keep original.
        # if backup_type == "manual":
        #     os.remove("cookies.pkl")
        log_info(f"Created {backup_type} login cookies backup: {backup_name}", transient=True)
        return True
    except Exception as error:
        log_error(f"Failed to create {backup_type} login cookies backup: {str(error)}")
        return False


def create_manual_cookies_backup() -> bool:
    """Create a manual backup of the cookies file."""
    return create_cookies_backup(backup_type="manual")


def monitor_orders_auto(driver: uc.Chrome) -> None:
    """Monitor orders with regular tab cleanup and processing."""
    try:
        clear_transient_line()
        console.print("\n[bold cyan]━━━ Order Monitoring Active ━━━[/]")
        console.print("[dim]Press 'p' to pause/resume | 'q' to exit | Enter to skip actions[/dim]\n")
        session.active = True
        while session.active:
            run_state.wait_if_paused()

            refresh_all_config_values()

            close_all_tabs_except_main(driver)
            time.sleep(0.5)
            driver.switch_to.window(driver.window_handles[0])

            orders: List[Order] = get_orders(driver)
            if orders:
                eligible_orders: List[Order] = orders

                if eligible_orders:
                    log_info(f"Processing {len(eligible_orders)} eligible orders...", transient=True)
                    for i, order in enumerate(eligible_orders):
                        run_state.wait_if_paused()
                        if not session.active:
                            break

                        if len(driver.window_handles) < TAB_POOL_SIZE + 1:
                            driver.switch_to.new_window("tab")
                        else:
                            try:
                                driver.switch_to.window(driver.window_handles[1])
                                log_debug(f"Reusing tab {driver.window_handles[1][:8]}...", transient=True)
                            except IndexError:
                                log_warning("Not enough tabs to reuse, opening new.", transient=True)
                                driver.switch_to.new_window("tab")

                        process_single_order(driver, order, index=i, total=len(eligible_orders))

                    if not session.active:
                        break

                    # --- Agent Check Section ---
                    try:
                        console.print("")
                        run_state.wait_if_paused()

                        # Check if first agent check should be performed
                        if countdown_timer(RECONNECT_TIMEOUT1, "Reconnect/agent check #1 in"):
                            if not session.active:
                                break
                            run_state.wait_if_paused()
                            check_and_message_agents(driver, use_alternate_message=False)

                        console.print("")
                        if not session.active:
                            break
                        run_state.wait_if_paused()

                        # Check if second agent check should be performed
                        if countdown_timer(RECONNECT_TIMEOUT2, "Reconnect/agent check #2 in"):
                            if not session.active:
                                break
                            run_state.wait_if_paused()
                            check_and_message_agents(driver, use_alternate_message=True)

                        console.print("")
                        if not session.active:
                            break
                        run_state.wait_if_paused()

                        # Check if tab cleanup should be performed
                        if countdown_timer(CLOSE_ALL_TIMEOUT, "Waiting before next cycle..."):
                            close_all_tabs_except_main(driver)  # Only close tabs if timer completes normally

                    except KeyboardInterrupt:
                        raise
                else:
                    clear_transient_line()
                    log_info("No eligible orders found in this batch.", transient=False)
                    run_state.wait_if_paused()
                    countdown_timer(30, "No eligible orders, waiting before next check")

            else:
                clear_transient_line()
                log_warning("No orders found on page. Retrying...", transient=False)
                run_state.wait_if_paused()
                countdown_timer(60, "No orders found, waiting before retry")

            if not session.active:
                break

    except KeyboardInterrupt:
        clear_transient_line()
        console.print("\n[bold yellow]Monitoring stopped by user (Ctrl+C / q)[/bold yellow]\n")
        session.cleanup()
    except Exception as error:
        clear_transient_line()
        log_error(f"Error in monitoring loop: {error}")
        session.cleanup()
    finally:
        try:
            if driver:
                log_info("Cleaning up tabs before exit...", transient=False)
                close_all_tabs_except_main(driver)
        except Exception as cleanup_error:
            log_error(f"Error closing tabs during final cleanup: {cleanup_error}")


def countdown_timer(seconds: int, message: str) -> None:
    """Display a countdown timer with a styled progress bar."""
    # Use the latest global values based on message type
    timeout_map = {
        "Reconnect/agent check #1 in": RECONNECT_TIMEOUT1,
        "Reconnect/agent check #2 in": RECONNECT_TIMEOUT2,
        "Waiting before next cycle...": CLOSE_ALL_TIMEOUT,
    }
    actual_seconds = timeout_map.get(message, seconds)

    if actual_seconds <= 0:
        return True  # Consider zero/negative time as completed normally

    clear_transient_line()  # Clear before starting timer

    try:
        # Determine if this is a reconnect/agent check timer and use appropriate colors
        is_reconnect_timer = "Reconnect/agent check" in message
        bar_complete_style = "cyan" if is_reconnect_timer else "magenta"
        bar_finished_style = ACCENT_COLOR if is_reconnect_timer else "green"
        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold cyan]{message}[/bold cyan] [dim](Enter to skip | p to pause)[/dim]"),
            BarColumn(
                bar_width=None,
                complete_style=bar_complete_style,
                finished_style=bar_finished_style,
            ),
            TextColumn("[red]{task.fields[remaining]}s remaining[/red]"),
            expand=True,
            transient=True,
        ) as progress:
            task = progress.add_task("countdown", total=actual_seconds, remaining=actual_seconds)
            start_time = time.monotonic()

            while not progress.finished:
                run_state.wait_if_paused()  # Allow pausing during countdown
                if not session.active:  # Allow quitting during countdown
                    raise KeyboardInterrupt("Exit requested during countdown")

                # Check for Enter key press to skip
                if InputHandler.kbhit():
                    key = InputHandler.getch()
                    if key in (b"\r", b"\n", "\r", "\n"):
                        log_info(f"Skipping '{message}' and associated action.", transient=True)
                        return False  # Indicate timer was skipped

                elapsed = time.monotonic() - start_time
                remaining = max(0, actual_seconds - int(elapsed))
                progress.update(task, completed=int(elapsed), remaining=remaining)

                if remaining <= 0:
                    break

                # Sleep for a short interval to remain responsive
                time.sleep(0.1)

            # Timer completed normally
            progress.update(task, completed=actual_seconds, remaining=0)
            progress.stop()
            clear_transient_line()
            return True  # Indicate timer completed normally

    except KeyboardInterrupt:
        clear_transient_line()
        log_warning(f"Timer '{message}' interrupted!", transient=False)
        raise
    except Exception as timer_error:
        clear_transient_line()
        log_error(f"Error in countdown timer: {str(timer_error)}")
        return False  # Consider errors as skipped


def get_progress_bar_style() -> tuple:
    """Return a standard progress bar style tuple for rich progress bars."""
    return (
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=None, complete_style=MAIN_COLOR, finished_style=ACCENT_COLOR),  # Auto width
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
    )


def get_progress_bar_style_no_percentage() -> tuple:
    """Return a progress bar style tuple without percentage indicator."""
    return (
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(bar_width=None, complete_style=MAIN_COLOR, finished_style=ACCENT_COLOR),  # Auto width
    )


_last_message: Optional[str] = None
_last_message_time: float = 0


def notify_cookie_save_status(success: bool, backup_success: bool, manual: bool = False) -> None:
    """Notify the user about cookie save status."""
    global _last_message, _last_message_time
    login_type: str = "Manual" if manual else "Automated"
    current_time: float = time.time()

    if success and backup_success:
        message: str = "Cookies saved & backup created successfully"
        style = "success"
    elif success:
        message = "Cookies saved (backup failed or skipped)"
        style = "warning"
    else:
        message = f"{login_type} Login: Failed to save cookies"
        style = "error"

    # Prevent rapid-fire duplicate messages
    if message != _last_message or (current_time - _last_message_time) > 2:
        clear_transient_line()  # Clear before notification
        if style == "success":
            log_success(message, transient=False)  # Non-transient confirmation
        elif style == "warning":
            log_warning(message, transient=False)  # Non-transient warning
        else:
            log_error(message)  # Non-transient error

        _last_message = message
        _last_message_time = current_time


def process_single_order(driver: uc.Chrome, order: Order, index: int = 0, total: int = 1) -> bool:
    """Process a single order to remove tip."""
    log_info(
        f"Processing order #{shorten_order_id(order.id)} ({index + 1}/{total})...",
        transient=True,
    )
    try:
        # remove_tip logs success/error non-transiently now
        order.remove_tip(driver, index=index, total=total, create_new_tab=False)
        return True
    except NoSuchElementException as error:
        log_error(f"Error processing order {shorten_order_id(order.id)} - element not found: {str(error)}")
        return False
    except TimeoutException as error:
        log_error(f"Error processing order {shorten_order_id(order.id)} - timeout: {str(error)}")
        return False
    except WebDriverException as error:
        log_error(f"Error processing order {shorten_order_id(order.id)} - WebDriver error: {str(error)}")
        return False
    except Exception as error:
        log_error(f"Unexpected error processing order {shorten_order_id(order.id)}: {str(error)}")
        logger.error(f"Unexpected error type {type(error).__name__}: {str(error)}")  # Log details to file
        return False


def click_element(driver: uc.Chrome, element: WebElement) -> bool:
    """Click an element with multiple fallback methods."""
    click_wait_time = GLOBAL_BUTTON_WAIT

    try:
        # Use global helper for scrolling and waiting
        scroll_and_wait(driver, element, click_wait_time / 2)

        # Try JavaScript click first
        driver.execute_script("arguments[0].click();", element)
        time.sleep(click_wait_time / 2)  # Shorter wait after JS click
        return True
    except Exception:
        log_debug("JS click failed, trying regular click", transient=True)
        try:
            # Try regular click
            element.click()
            time.sleep(click_wait_time)
            return True
        except (ElementClickInterceptedException, ElementNotInteractableException):
            log_debug("Regular click failed, trying actions click", transient=True)
            try:
                # Try actions click
                ActionChains(driver).move_to_element(element).click().perform()
                time.sleep(click_wait_time)
                return True
            except Exception as click_error:
                log_error(f"Failed to click element after all attempts: {click_error}")
                return False
        except Exception as click_error_reg:
            log_error(f"Failed regular click: {click_error_reg}")
            return False


def scroll_and_wait(driver: uc.Chrome, element: WebElement, wait_duration: float) -> None:
    """Global helper to scroll to an element and wait, avoiding variable shadowing issues."""
    try:
        driver.execute_script(
            "arguments[0].scrollIntoView({block: 'center', behavior: 'smooth'});",
            element,
        )
        time.sleep(wait_duration)
    except Exception as scroll_err:
        log_debug(f"Scroll/wait error: {scroll_err}", transient=True)


def refresh_all_config_values() -> None:
    """Refresh all configuration values from config module."""
    global TEST_MODE, HEADLESS_MODE, MAX_ORDERS_PER_BATCH, MAX_TIP_THRESHOLD
    global RECONNECT_TIMEOUT1, RECONNECT_TIMEOUT2, CLOSE_ALL_TIMEOUT, CYCLE_SIZE
    global CANCELLED_ORDERS_ELIGIBLE, REFUNDED_ORDERS_ELIGIBLE

    try:
        from config_module import get_config

        config = get_config(for_banned=False)

        TEST_MODE = config.get("TEST_MODE", TEST_MODE)
        HEADLESS_MODE = config.get("HEADLESS_MODE", HEADLESS_MODE)
        MAX_ORDERS_PER_BATCH = config.get("MAX_ORDERS_PER_BATCH", MAX_ORDERS_PER_BATCH)
        MAX_TIP_THRESHOLD = config.get("MAX_TIP_THRESHOLD", MAX_TIP_THRESHOLD)
        RECONNECT_TIMEOUT1 = config.get("RECONNECT_TIMEOUT1", RECONNECT_TIMEOUT1)
        RECONNECT_TIMEOUT2 = config.get("RECONNECT_TIMEOUT2", RECONNECT_TIMEOUT2)
        CLOSE_ALL_TIMEOUT = config.get("CLOSE_ALL_TIMEOUT", CLOSE_ALL_TIMEOUT)
        CYCLE_SIZE = config.get("CYCLE_SIZE", CYCLE_SIZE)
        CANCELLED_ORDERS_ELIGIBLE = config.get("CANCELLED_ORDERS_ELIGIBLE", CANCELLED_ORDERS_ELIGIBLE)
        REFUNDED_ORDERS_ELIGIBLE = config.get("REFUNDED_ORDERS_ELIGIBLE", REFUNDED_ORDERS_ELIGIBLE)

        # Use transient debug log for refresh confirmation
        # log_debug("Configuration values refreshed", transient=True)
    except ImportError:
        log_debug("config_module not found, using existing config values.", transient=True)
    except Exception as e:
        log_warning(f"Failed to refresh configuration: {e}", transient=True)  # Make warning transient


def handle_keypress(key) -> None:
    """Handle a keypress event for pause/quit."""
    global run_state

    if key in (b"p", "p"):
        is_paused = run_state.toggle_pause()
        clear_transient_line()  # Clear before printing status
        if is_paused:
            logger.info("Script paused by user")
            StatusPrinter.print_status("Script PAUSED ⏸️ Press 'p' to resume | 'q' to quit", "warning")
        else:
            logger.info("Script resumed by user")
            StatusPrinter.print_status("Script RESUMED ▶️", "success")
        console.print("")  # Add a newline after status change message

    elif key in (b"q", "q"):
        logger.info("Exit requested by user via 'q' key")
        clear_transient_line()
        StatusPrinter.print_status("Exit requested. Shutting down...", "warning")
        session.active = False  # Signal monitoring loop to stop
        # Use KeyboardInterrupt to break out of waits/loops gracefully
        # threading.Timer(0.1, lambda: os._exit(0)).start() # Avoid hard exit
        # Instead, let the main thread handle cleanup via session.active flag


# Add this function to listen for keyboard input in a background thread
def key_listener() -> None:
    """Background thread to listen for keyboard input."""
    # Priority setup for Windows
    if platform.system() == "Windows":
        try:
            import win32api
            import win32process

            win32process.SetThreadPriority(win32api.GetCurrentThread(), win32process.THREAD_PRIORITY_HIGHEST)
        except ImportError:
            logger.warning("Could not set thread priority (win32api not available)")

    while True:
        try:
            if InputHandler.kbhit():
                key = InputHandler.getch()
                if key in (b"p", "p", b"q", "q"):
                    # Let the main thread handle state changes via handle_keypress
                    # to avoid potential race conditions with console output
                    threading.Timer(0, handle_keypress, args=(key,)).start()
                elif key in (b"\r", "\r", b"\n", "\n"):
                    # Allow Enter to be potentially handled by countdown timer directly
                    pass  # Countdown timer handles Enter now
            time.sleep(Config.KEY_CHECK_INTERVAL if hasattr(Config, "KEY_CHECK_INTERVAL") else 0.05)
        except EOFError:  # Handle case where stdin is closed
            logger.warning("Input stream closed, stopping key listener.")
            break
        except Exception as e:
            logger.error(f"Error in key listener: {e}")
            time.sleep(0.5)


def handle_cloudflare_check(driver: uc.Chrome) -> bool:
    """Handles Cloudflare 'Verify you are human' checkbox if present."""
    try:
        # Look for the iframe containing the checkbox - using a more general selector
        iframe = WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'iframe[src*="cloudflare.com" i]'))
        )
        driver.switch_to.frame(iframe)

        # Look for the checkbox inside the iframe - using a more specific selector
        checkbox = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "input[type='checkbox']"))
        )

        # Human-like click using JavaScript (more reliable for checkboxes)
        driver.execute_script("arguments[0].click();", checkbox)
        driver.switch_to.default_content()  # Switch back out of frame
        logger.info("Cloudflare checkbox clicked.")
        time.sleep(3)  # Wait briefly after clicking checkbox
        return True  # Checkbox found and clicked

    except TimeoutException:
        logger.debug("Cloudflare checkbox not found within timeout - assuming not present.")
        driver.switch_to.default_content()  # Ensure we are out of iframe if it wasn't found
        return False  # Checkbox not found

    except Exception as e:
        logger.warning(f"Error handling Cloudflare check: {e}")
        driver.switch_to.default_content()  # Ensure we are out of iframe in case of error
        return False  # Error during check


def click_intermediary_login_button(driver: uc.Chrome) -> bool:
    """Clicks the 'Login' button on the intermediary login page (Image 2) - Enhanced Selector & Logging."""
    try:
        logger.info("Attempting to click 'Login' button on intermediary page - Enhanced Selector...")

        # --- Try Data-TestId Selector (Most Specific) ---
        data_testid_selector = 'a[data-testid="signInButton"]'
        logger.debug(f"Trying Data-TestId selector: {data_testid_selector}")
        try:
            login_button_testid = WebDriverWait(driver, 15).until(  # Increased timeout
                EC.element_to_be_clickable((By.CSS_SELECTOR, data_testid_selector))
            )
            logger.debug(f"Found Login button by Data-TestId: {login_button_testid.text}")
            driver.execute_script("arguments[0].click();", login_button_testid)
            logger.info("Clicked 'Login' button using Data-TestId selector.")
            time.sleep(3)
            return True
        except TimeoutException:
            logger.debug("Data-TestId selector failed, falling back to XPath.")  # Debug level, as fallback is next
        except Exception as e_testid:
            logger.warning(f"Error using Data-TestId selector: {e_testid}")  # Warning level for errors

        # --- Fallback to XPath Selector (More General) ---
        xpath_selector = '//a[contains(@class, "top-nav_item") and contains(text(), "Login")]'
        logger.debug(f"Trying XPath selector (fallback): {xpath_selector}")
        try:
            login_button_xpath = WebDriverWait(driver, 15).until(  # Increased timeout
                EC.element_to_be_clickable((By.XPATH, xpath_selector))
            )

            logger.debug(f"Found Login button by XPath: {login_button_xpath.text}")
            logger.debug(
                f"Button enabled: {login_button_xpath.is_enabled()}, displayed: {login_button_xpath.is_displayed()}"
            )  # Check button state

            # Scroll into view (again, for robustness)
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", login_button_xpath)
            time.sleep(0.5)

            driver.execute_script("arguments[0].click();", login_button_xpath)  # JavaScript click
            logger.info("Clicked 'Login' button using XPath selector (fallback).")
            time.sleep(3)
            return True

        except TimeoutException:
            logger.warning("Timeout: Could not find or click 'Login' button using XPath fallback after 15 seconds.")
            return False
        except Exception as e_xpath:
            logger.warning(f"Error clicking 'Login' button using XPath fallback: {e_xpath}")
            logger.warning(traceback.format_exc())  # Full traceback for errors
            return False

    except Exception as e_outer:
        logger.warning(f"Error in click_intermediary_login_button (Outer Exception): {e_outer}")
        logger.warning(traceback.format_exc())
        return False


def is_intermediary_login_page(driver: uc.Chrome) -> bool:
    """Checks if the current page is the intermediary login page (Image 2) by looking for specific elements."""
    try:
        logger.debug("Checking if on intermediary login page...")
        # Check for the "Login" button in the top right (or similar unique element on Image 2)
        login_button = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located(
                (By.XPATH, '//a[contains(@class, "top-nav_item") and contains(text(), "Login")]')
            )
        )
        if login_button.is_displayed():
            logger.debug("Intermediary login page detected (Login button visible).")
            return True
        else:
            logger.debug("Intermediary login page NOT detected (Login button NOT visible, but element present).")
            return False  # Element present but not visible

    except TimeoutException:
        logger.debug("Intermediary login page NOT detected (Login button element NOT found within timeout).")
        return False  # Button not found, not the intermediary page
    except Exception as e:
        logger.warning(f"Error checking for intermediary login page: {e}")
        return False  # Error during check, assume not intermediary page


def print_startup_info():
    """Prints a styled panel with startup configuration."""
    # Refresh config values before printing
    refresh_all_config_values()

    config_details = (
        f"  [cyan]Test Mode:[/cyan] {'[bold green]Enabled[/]' if TEST_MODE else '[bold red]Disabled[/]'}\n"
        f"  [cyan]Headless:[/cyan] {'[bold green]Enabled[/]' if HEADLESS_MODE else '[bold red]Disabled[/]'}\n"
        f"  [cyan]Max Orders/Batch:[/cyan] [bold yellow]{MAX_ORDERS_PER_BATCH}[/]\n"
        f"  [cyan]Min Order $:[/cyan] [bold yellow]${MAX_TIP_THRESHOLD:.2f}[/]\n"
        f"  [cyan]Cancelled Eligible:[/cyan] {'[bold green]Yes[/]' if CANCELLED_ORDERS_ELIGIBLE else '[bold red]No[/]'}\n"
        f"  [cyan]Refunded Eligible:[/cyan] {'[bold green]Yes[/]' if REFUNDED_ORDERS_ELIGIBLE else '[bold red]No[/]'}\n"
        f"  [cyan]Log File:[/cyan] [dim]{current_log_file or 'Not Initialized'}[/dim]"
    )

    console.print(
        Panel(
            config_details, title="[bold]Script Configuration[/]", border_style=MAIN_COLOR, padding=(1, 2), expand=False
        )
    )
    console.print()


def print_manual_login_prompt():
    """Prints instructions for manual login within a styled panel."""
    # Get the timeout value from the main function (300 seconds = 5 minutes)
    timeout_minutes = 5  # This matches the manual_login_timeout value in main()

    prompt_text = Text.assemble(
        ("Log in using the browser window.", "bold cyan"),
        "\n\n",
        (f"The script will wait for {timeout_minutes} minutes for login to complete.", "dim"),
        ("Click login/refresh if needed for login to continue"),
        "\n",
        ("Press ", "dim"),
        ("q", "bold yellow"),
        (" to quit if needed.", "dim"),
    )
    console.print(
        Panel(
            prompt_text,
            title="[bold]Manual Login Required[/]",
            border_style=ACCENT_COLOR,
            padding=(0, 2),
            expand=False,
            width=60,  # Added wrap=True to enable text wrapping
        )
    )
    console.print()


def countdown_timer(seconds: int, message: str) -> bool:
    """Display a countdown timer with a styled progress bar using MAIN_COLOR and ACCENT_COLOR."""
    timeout_map = {
        "Reconnect/agent check #1 in": RECONNECT_TIMEOUT1,
        "Reconnect/agent check #2 in": RECONNECT_TIMEOUT2,
        "Waiting before next cycle...": CLOSE_ALL_TIMEOUT,
    }
    actual_seconds = timeout_map.get(message, seconds)

    if actual_seconds <= 0:
        return True

    clear_transient_line()

    try:
        # Use defined colors for the progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn(f"[bold cyan]{message}[/bold cyan] [dim](Enter to skip | p to pause)[/dim]"),
            BarColumn(
                bar_width=None,
                complete_style=MAIN_COLOR,  # Use MAIN_COLOR
                finished_style=ACCENT_COLOR,  # Use ACCENT_COLOR
            ),
            TextColumn("[red]{task.fields[remaining]}s remaining[/red]"),
            expand=True,
            transient=True,
        ) as progress:
            task = progress.add_task("countdown", total=actual_seconds, remaining=actual_seconds)
            start_time = time.monotonic()

            while not progress.finished:
                run_state.wait_if_paused()
                if not session.active:
                    raise KeyboardInterrupt("Exit requested during countdown")

                if InputHandler.kbhit():
                    key = InputHandler.getch()
                    if key in (b"\r", b"\n", "\r", "\n"):
                        log_info(f"Skipping '{message}' and associated action.", transient=True)
                        return False

                elapsed = time.monotonic() - start_time
                remaining = max(0, actual_seconds - int(elapsed))
                progress.update(task, completed=int(elapsed), remaining=remaining)

                if remaining <= 0:
                    break

                time.sleep(0.1)

            progress.update(task, completed=actual_seconds, remaining=0)
            progress.stop()
            clear_transient_line()
            return True

    except KeyboardInterrupt:
        clear_transient_line()
        log_warning(f"Timer '{message}' interrupted!", transient=False)
        raise
    except Exception as timer_error:
        clear_transient_line()
        log_error(f"Error in countdown timer: {str(timer_error)}")
        return False


def main() -> None:
    """Main function with enhanced UI elements."""
    global customer_name, customer_email, TEST_MODE, HEADLESS_MODE
    global MAX_ORDERS_PER_BATCH, MAX_TIP_THRESHOLD
    global RECONNECT_TIMEOUT1, RECONNECT_TIMEOUT2, CLOSE_ALL_TIMEOUT
    global run_state

    logging.info("Starting new session")
    add_log_handler()
    run_state = RunState()

    key_thread = threading.Thread(target=key_listener, daemon=True)
    key_thread.start()

    # Print startup info panel
    print_startup_info()

    console.print("\n[bold cyan]Keyboard Controls:[/bold cyan]")
    console.print(" • Press [bold yellow]p[/bold yellow] to pause/resume the script")
    console.print(" • Press [bold yellow]q[/bold yellow] to quit the program")
    console.print(" • Press [bold yellow]Enter[/bold yellow] to skip actions\n")

    # Configuration is already loaded by print_startup_info
    app_state.test_mode = TEST_MODE
    app_state.headless_mode = HEADLESS_MODE
    app_state.max_orders_per_batch = MAX_ORDERS_PER_BATCH

    menu_choice: Optional[str] = run_menu_system()
    if not menu_choice or menu_choice == "exit":
        log_info("Exiting program.", transient=False)
        return

    cookies: Optional[List[Dict]] = None
    if is_valid_cookie_file("cookies.pkl"):
        try:
            with open("cookies.pkl", "rb") as file:
                cookies = pickle.load(file)
                log_info("Loaded cookies from cookies.pkl", transient=True)
        except (pickle.PickleError, EOFError) as e:
            log_warning(f"Could not load cookies.pkl: {e}", transient=False)
            cookies = None
    else:
        log_info("cookies.pkl not found or empty.", transient=True)

    choice: str = "1"
    if menu_choice == "login_cookies":
        choice = "2"
    elif menu_choice == "login_manual":
        choice = "1"

    login_successful: bool = False
    customer_info: Optional[Dict[str, str]] = None

    try:
        with DriverContextManager() as driver:
            if choice == "2" and cookies:
                login_successful = cookie_login_with_fallback(driver, cookies, timeout=45)
                if not login_successful:
                    log_warning("Cookie login failed, switching to manual login.", transient=False)

            if not login_successful:
                try:
                    create_cookies_backup(backup_type="auto")
                    driver.get("https://www.doordash.com/consumer/login/")
                    clear_transient_line()
                    # Use the new panel for manual login instructions
                    print_manual_login_prompt()
                    wait_start_time = time.time()
                    manual_login_timeout = 300

                    StatusPrinter.print_status(
                        "Waiting for manual login completion (check URL for /home or /orders)...", "processing"
                    )
                    while time.time() - wait_start_time < manual_login_timeout:
                        run_state.wait_if_paused()

                        if handle_cloudflare_check(driver):
                            StatusPrinter.print_status("Cloudflare check resolved...", "info")
                            logger.info("Cloudflare check resolved.")

                        if is_intermediary_login_page(driver):
                            if click_intermediary_login_button(driver):
                                StatusPrinter.print_status("Clicked 'Login' on intermediary page...", "info")
                                logger.info("Clicked 'Login' on intermediary page.")
                            else:
                                log_warning("Could not click 'Login' on intermediary page, but continuing wait.")

                        try:
                            current_url = driver.current_url
                            if "/home" in current_url or "/orders" in current_url:
                                StatusPrinter.print_status("Manual login detected!", "success")
                                logger.info(f"Manual login successful (URL: {current_url})")
                                login_successful = True
                                save_cookies_after_login(driver, manual=True)
                                break
                        except NoSuchWindowException:
                            StatusPrinter.print_status("Browser window closed during manual login.", "error")
                            logger.error("Browser window closed during manual login wait.")
                            sys.exit(1)
                        except Exception as url_err:
                            logger.warning(f"Error checking URL during manual login wait: {url_err}")

                        if InputHandler.kbhit():
                            key = InputHandler.getch()
                            if key in (b"q", "q"):
                                StatusPrinter.print_status("Exit requested during manual login.", "warning")
                                logger.info("Exit requested by user during manual login.")
                                sys.exit(0)
                            elif key in (b"p", "p"):
                                handle_keypress(key)
                        time.sleep(1)

                    if not login_successful:
                        StatusPrinter.print_status(
                            f"Manual login timed out after {manual_login_timeout} seconds.", "error"
                        )
                        logger.error("Manual login timed out.")
                        return

                except Exception as e:
                    log_error(f"Error during manual login process: {str(e)}")
                    return

            if login_successful:
                try:
                    log_info("Retrieving customer information...", transient=True)
                    driver.get("https://www.doordash.com/consumer/edit_profile/")
                    email_element = WebDriverWait(driver, 30).until(
                        lambda d: (
                            d.find_element(By.CSS_SELECTOR, "input[type='email']")
                            if d.find_element(By.CSS_SELECTOR, "input[type='email']").get_attribute("value")
                            else None
                        )
                    )
                    first_name_element = driver.find_element(By.CSS_SELECTOR, "input[data-testid='givenName_input']")
                    last_name_element = driver.find_element(By.CSS_SELECTOR, "input[data-testid='familyName_input']")

                    customer_info = {
                        "first_name": first_name_element.get_attribute("value") or "",
                        "last_name": last_name_element.get_attribute("value") or "",
                        "email": email_element.get_attribute("value") or "",
                    }

                    if all(customer_info.values()):
                        save_cookies_after_login(driver, manual=False, customer_info=customer_info)
                    else:
                        log_warning(
                            "Could not retrieve full customer info for backup naming.",
                            transient=False,
                        )
                        save_cookies_after_login(driver, manual=False, customer_info=None)

                    customer_email = customer_info.get("email", "could not get email")
                    customer_name = (
                        f"{customer_info.get('first_name', '')} {customer_info.get('last_name', '')}".strip()
                        or "could not get name"
                    )

                    app_state.customer_name = customer_name
                    app_state.customer_email = customer_email

                    clear_transient_line()
                    customer_info_panel: Panel = Panel(
                        f"\n[white]{customer_name}[/white]\n[dim]{customer_email}[/dim]\n",
                        title=f"[bold {MAIN_COLOR}]Customer Information[/]",
                        title_align="left",
                        border_style=MAIN_COLOR,
                        padding=(0, 2),
                        width=60,
                    )
                    console.print("")
                    console.print(customer_info_panel)

                except (NoSuchElementException, TimeoutException) as e:
                    log_error(f"Failed to retrieve customer information from profile page: {e}")
                    customer_email = "Error: Check Profile Page"
                    customer_name = "Error: Check Profile Page"
                    app_state.customer_name = customer_name
                    app_state.customer_email = customer_email
                except Exception as e:
                    log_error(f"Unexpected error retrieving customer info: {e}")
                    customer_email = "Error: Unknown"
                    customer_name = "Error: Unknown"
                    app_state.customer_name = customer_name
                    app_state.customer_email = customer_email

            if login_successful:
                try:
                    monitor_orders_auto(driver)
                except KeyboardInterrupt:
                    pass
                except Exception as e:
                    log_error(f"Critical error during monitoring: {e}")
            else:
                log_error("Login failed. Cannot start monitoring.")

    except Exception as e:
        log_error(f"An unexpected error occurred in main: {e}")
        logger.exception("Unhandled exception in main:")

    finally:
        session.cleanup()
        clear_transient_line()
        log_info("End of session.", transient=False)


if __name__ == "__main__":
    main()
