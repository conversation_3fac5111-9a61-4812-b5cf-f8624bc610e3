@import url('../theme/light.css');
@import url('../theme/dark.css');
@import url('dark.css');
@import url('../theme/switch.css');

body {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  min-width: 500px;
  font-size: 12px;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, sans-serif;
  color: var(--primary-text-color);
  background-color: var(--primary-surface-color);
}

body * {
  box-sizing: border-box;
  text-align: start;
}

.notransition *, .notransition *::before {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}

button::-moz-focus-inner {
  border: 0;
}

svg.icon {
  height: 1em;
  width: 1em;
  pointer-events: none;
  fill: var(--primary-text-color);
}

.container {
  padding: 8px 12px;
}

#pageTitle {
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

#pageTitle h1 {
  font-size: 16px;
  line-height: 16px;
  margin: 5px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: 0;
}

#version {
  position: absolute;
  top: 14px;
  right: 36px;
  font-size: 10px;
  color: var(--primary-border-color);
  user-select: none;
}

#main-menu-button {
  position: absolute;
  top: 6px;
  right: 6px;
  height: 30px;
  width: 30px;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  text-align: center;
  border-radius: 40px;
}
#main-menu-button svg {
  margin: 0 auto;
  display: block;
}

#main-menu-button:hover {
  background-color: var(--secondary-surface-color);
}

#main-menu-button:focus-visible {
  outline: 2px solid var(--primary-outline-color);
}

#main-menu-content {
  opacity: 0;
  position: absolute;
  top: -999px;
  right: 10px;
  border-radius: 5px;
  background-color: var(--menu-surface-color);
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.35);
  z-index: 10;
  overflow: hidden;
  user-select: none;
  transition: opacity 150ms linear;
}
#main-menu-content.visible {
  top: 35px;
  opacity: 1;
}

#main-menu-content .menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 14px;
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
  cursor: pointer;
  background: transparent;
  color: var(--primary-text-color);
  border: none;
  font-size: inherit;
  font-family: inherit;
  transition: background-color 100ms linear;
}

#main-menu-content .menu-item:hover {
  background-color: var(--menu-surface-hover-color);
}

#main-menu-content .menu-item:focus-visible {
  outline: 2px solid var(--primary-outline-color);
  outline-offset: -3px;
  border-radius: 4px;
}

#main-menu-content .menu-item .switch {
  width: 32px;
  height: 16px;
  vertical-align: middle;
}

#main-menu-content .menu-item .switch .slider:before {
  height: 14px;
  width: 14px;
  bottom: 1px;
  left: 2px;
}
#main-menu-content .menu-item .switch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}

h2 {
  margin: 0;
  padding: 0;
}

button {
  cursor: pointer;
}
button:focus {
  outline: 0;
}

#ad-container > div {
  padding: 3px 6px;
  display: flex;
  background-color: var(--ad-surface-color);
}

.ad-tag {
  font-style: italic;
  color: var(--ad-text-color);
}

.ad-link {
  flex: 1;
  padding-left: 5px;
  padding-right: 5px;
}

.ad-link a {
  color: var(--ad-link-color);
}

.ad-link a:hover {
  color: var(--primary-text-color);
}

.ad-actions {
  display: inline-block;
}

.ad-actions button {
  border: none;
  background: transparent;
  font-size: 12px;
  color: var(--ad-link-color);
}

.ad-actions button:hover {
  color: var(--primary-text-color);
  text-decoration: underline;
}
.ad-actions button:focus-visible {
  outline: 2px solid var(--primary-outline-color);
  border-radius: 4px;
}

@media (max-width: 400px) {
  #ad-container > div {
    display: block;
  }
  .ad-link {
    display: inline;
  }
  .ad-actions {
    display: block;
    text-align: right;
    margin-top: 8px;
  }
}


#searchContainer {
  display: flex;
  position: relative;
}

#cookie-container .search {
  position: absolute;
  left: 15px;
  top: 10px;
}

#cookie-container #searchField {
  padding: 8px;
  padding-left: 40px;
  transition: background-color 100ms linear;
  background-color: var(--search-surface-color);
  color: var(--primary-text-color);
}
#cookie-container li .header:focus,
#cookie-container #searchField:focus {
  outline-offset: -3px;
  border-radius: 4px;
}

#cookie-container #searchField:hover,
#cookie-container #searchField:focus {
  background-color: var(--search-surface-hover-color);
}
#cookie-container #searchField.content {
  background-color: var(--search-surface-content-color);
}

#cookie-container #searchField::placeholder {
  color: var(--secondary-text-color);
}

#cookie-container {
  min-height: 100px;
  min-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  background-color: var(--secondary-surface-color);
}

#no-cookies,
#no-permission,
#permission-impossible {
  margin-top: 2em;
  text-align: center;
  font-size: 1.2em;
}

#no-permission {
  margin: 1em auto;
}

#no-permission > div {
  text-align: center;
  padding-top: 5px;
  font-size: 1.1em;
}

#no-permission .button-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding-top: 5px;
}

#no-permission button {
  border: 1px solid var(--primary-border-color);
  border-radius: 3px;
  background-color: var(--primary-surface-color);
  color: var(--primary-text-color);
  padding: 8px 25px;
  font-size: 14px;
  transition: all 100ms linear;
}

#no-permission button:hover {
  border-radius: 4px;
  border-color: var(--primary-outline-color);
}

#no-permission button:focus-visible {
  border-radius: 4px;
  outline: 2px solid var(--primary-outline-color);
}

#cookie-container ul {
  margin: 0;
  padding: 0;
  padding-bottom: 15px;
  list-style-type: none;
}

#cookie-container li.hide {
  display: none;
}

#cookie-container .header {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  -moz-user-select: none;
  user-select: none;
  background-color: var(--primary-surface-color);
  border: 1px solid var(--primary-border-color);
}

#cookie-container li .header .btns {
  display: block;
  opacity: 0;
  position: absolute;
  right: 5px;
  top: 4px;
  transition: all 100ms ease;
}

/* Firefox display outlines in a strange way */
@-moz-document url-prefix() {
  #cookie-container li .header:focus {
    outline-offset: -3px;
    outline: 2px solid var(--primary-outline-color);
    border-radius: 3px;
  }
}

#cookie-container li .header:hover .btns {
  opacity: 1;
}

#cookie-container li .header .icon.arrow {
  -moz-transition: transform 150ms ease-in 0s;
  transition: transform 150ms ease-in 0s;
  margin-right: 10px;
  flex-shrink: 0;
}

#cookie-container li .header.active .icon.arrow {
  -moz-transform: rotate(180deg);
  transform: rotate(180deg);
}

#cookie-container li .header .header-name,
#cookie-container li .header .header-extra-info {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#cookie-container li .header .header-name {
  font-weight: 500;
}

#cookie-container li .header .header-extra-info {
  flex-shrink: 9999;
  flex-grow: 1;
  padding-left: 8px;
  padding-right: 3px;
  text-align: right;
  font-size: 0.85em;
  color: var(--secondary-text-color);
}

#cookie-container .expando {
  max-height: 0;
  display: none;
  overflow: hidden;
}

#cookie-container .expando .wrapper {
  flex: 1;
  display: flex;
}

#cookie-container textarea,
#cookie-container input[type='text'],
#cookie-container select {
  width: 100%;
  margin: 0;
  padding: 4px;
  font-family: 'Segoe UI', Tahoma, sans-serif;
  border: 1px solid var(--secondary-border-color);
  background-color: var(--primary-surface-color);
  color: var(--primary-text-color);
}
#cookie-container select option {
  background-color: var(--primary-surface-color);
}

#cookie-container textarea:hover,
#cookie-container input:not([disabled])[type='text']:hover {
  border-color: rgb(82, 130, 192);
  box-shadow: 1px 1px 5px rgba(150, 150, 150, 0.4);
}

#cookie-container textarea:disabled,
#cookie-container input[type='text']:disabled,
#cookie-container select:disabled {
  background-color: var(--secondary-surface-color);
}

.checkbox-list {
  display: flex;
  justify-content: space-between;
  margin-top: 7px;
}

.checkbox-list label,
.checkbox-list input {
  cursor: pointer;
}

.checkbox-list input {
  vertical-align: middle;
  position: relative;
  bottom: 2px;
}

#cookie-container textarea {
  height: 60px;
  resize: none;
}

#cookie-container textarea.json {
  height: 360px;
  font-family: Consolas, Monaco, monospace;
  white-space: pre;
  overflow: auto;
}

label {
  display: block;
  margin: 0;
}

.button-bar {
  display: none;
  z-index: 5;
  background-color: var(--primary-surface-color);
}

.button-bar.active {
  display: block;
}

.expando .container.form {
  flex: 1;
  padding-left: 4px;
}
.container.form label {
  font-weight: 500;
  color: var(--secondary-text-onsurface-color);
}

.action-btns {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 4px;
}

.action-btns .tooltip {
  display: none;
}

.btns button.delete {
  background-color: var(--primary-surface-color);
  border: 1px solid var(--secondary-border-color);
  height: 24px;
  outline: 0 !important;
  padding: 0 8px 0;
  transition-duration: 250ms;
  transition-property: box-shadow, border;
}

.btns button.delete::-moz-focus-inner {
  border: 0;
  outline: 0;
}

.action-btns button {
  flex: 1 1;
  border: none;
  background-color: transparent;
  cursor: pointer;
  padding: 8px;
  transition: color 75ms;
  transition-timing-function: ease-in;
}

.action-btns button,
.btns button {
  transition: all 100ms ease;
  fill: var(--primary-text-color);
}

.action-btns button:focus {
  outline: 2px solid var(--primary-outline-color);
  border-radius: 4px;
}

button.delete:hover svg,
button.delete:focus svg {
  fill: var(--primary-danger-color);
}
.action-btns button.save:hover svg,
.action-btns button.save:focus svg {
  fill: var(--primary-safe-color);
}

/* Quick workaround the rendering issue for popups */
body,
.expando,
#cookie-container {
  background-image: url('pixel.png');
}

.panel-section-footer {
  background-color: rgba(0, 0, 0, 0.06);
  border-top: 1px solid rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: row;
  height: 41px;
  margin-top: -1px;
  padding: 0;
  -moz-user-select: none;
  user-select: none;
  overflow: hidden;
}

.button-bar-top .panel-section-footer {
  border-top: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
}

.panel-section-footer-button {
  font-size: 14px;
  line-height: 14px;
  flex: 1 1;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 150ms ease;
  border: 1px solid transparent;
  background-color: var(--secondary-surface-color);
}
.panel-section-footer-button:hover,
.panel-section-footer-button:focus {
  background-color: rgba(0, 0, 0, 0.08);
  border: 1px solid var(--primary-outline-color);
}
.panel-section-footer-button.primary:hover,
.panel-section-footer-button.primary:focus {
  background-color: var(--button-primary-surface-color);
  color: var(--button-primary-text-color);
  fill: var(--button-primary-text-color);
}
.panel-section-footer-button.danger:hover,
.panel-section-footer-button.danger:focus {
  background-color: var(--button-danger-surface-color);
}

.panel-section-footer-separator {
  background-color: rgba(0, 0, 0, 0.1);
  width: 1px;
  z-index: 99;
}

.sbro {
  display: red !important;
}

.panel-section-footer-button > div {
  text-align: center;
  transition: transform 100ms ease;
}
.panel-section-footer-button .tooltip {
  text-align: center;
  color: var(--secondary-text-color);
  transition: all 200ms ease;
  opacity: 0;
}
.panel-section-footer-button:hover > div,
.panel-section-footer-button:focus > div {
  transform: translateY(-6px);
}
.panel-section-footer-button:hover .tooltip,
.panel-section-footer-button:focus .tooltip {
  color: #444;
  opacity: 1;
}

.panel-section-footer-button.primary .tooltip {
  color: #bfbfbf;
}
.panel-section-footer-button.primary:hover .tooltip,
.panel-section-footer-button.primary:focus .tooltip {
  color: #efefef;
}

#notification-container {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
  text-align: center;
  opacity: 1;
  transition: opacity 150ms ease-in 0s;
  pointer-events: none;
  padding: 0 25px;
}

.button-bar-top #notification-container {
  bottom: 15px;
}

#notification {
  position: relative;
  display: inline-block;
  padding: 10px 20px;
  padding-right: 25px;
  margin: auto;
  background-color: var(--notification-surface-color);
  border-radius: 5px;
  color: var(--notification-text-color);
  font-size: 14px;
  pointer-events: auto;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  opacity: 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  max-width: 100%;
}

.fadeInUp {
  opacity: 0;
  animation-name: fadeInUp;
}
.fadeOutDown {
  opacity: 0;
  animation-name: fadeOutDown;
}

#notification-dismiss {
  position: absolute;
  top: 5px;
  right: 2px;
  background: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
  fill: var(--notification-dismiss-color);
}

#notification-dismiss svg {
  width: 0.9em;
  height: 0.9em;
}
#notification:hover #notification-dismiss {
  fill: var(--notification-dismiss-hover-parent-color);
}
#notification:hover #notification-dismiss:hover,
#notification #notification-dismiss:focus {
  fill: var(--notification-dismiss-hover-color);
}

#notification #notification-dismiss:focus {
  outline: 2px solid var(--notification-dismiss-hover-parent-color);
  border-radius: 3px;
}

@keyframes fadeInUp {
  from {
    transform: translate3d(0, 40px, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes fadeOutDown {
  from {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }

  to {
    transform: translate3d(0, 40px, 0);
    opacity: 0;
  }
}

[data-tooltip],
[data-tooltip-left] {
  position: relative;
}

[data-tooltip]::after,
[data-tooltip-left]::after {
  position: absolute;
  top: 49%;
  padding: 3px 10px;
  min-width: 40px;
  background-color: var(--notification-surface-color);
  color: var(--notification-text-color);
  text-align: center;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  z-index: 50;
  visibility: hidden;
}
[data-tooltip]::after {
  content: attr(data-tooltip);
  left: 100%;
  margin-left: 10px;
  transform: translate(-30px, -50%);
}
[data-tooltip-left]::after {
  content: attr(data-tooltip-left);
  right: 100%;
  margin-right: 7px;
  transform: translate(20px, -50%);
}

[data-tooltip]:hover::after,
[data-tooltip-left]:hover::after,
[data-tooltip]:focus::after,
[data-tooltip-left]:focus::after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
  transform: translateY(-50%);
}

.anim-value-changed,
#cookie-container .anim-value-changed {
  animation-name: changed-value;
  animation-duration: 0.5s;
}

@keyframes changed-value {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: var(--changed-surface-color);
  }
  100% {
    background-color: transparent;
  }
}

.anim-success,
#cookie-container .anim-success {
  animation-name: success;
  animation-duration: 0.5s;
  border: 1px solid initial;
}

@keyframes success {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: var(--success-surface-color);
  }
  100% {
    background-color: transparent;
  }
}

.advanced-form {
  display: none;
}
.advanced-form.show {
  display: block;
}
.advanced-toggle {
  display: block;
  width: 100%;
  background: transparent;
  color: var(--secondary-text-color);
  border: none;
  text-align: right;
  font-size: 11px;
  text-decoration: underline;
  cursor: pointer;
  user-select: none;
}
.advanced-toggle:hover,
.advanced-toggle:focus {
  color: var(--primary-text-color);
}
.advanced-toggle:focus-visible {
  outline: 2px solid var(--primary-outline-color);
  border-radius: 4px;
}

#export-menu {
  border-radius: 5px;
  position: fixed;
  bottom: 51px;
  right: 15px;
  min-width: 100px;
  background-color: var(--menu-surface-color);
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.35);
  padding: 10px;
  padding-top: 5px;
}

#export-menu:after {
  content: '';
  position: absolute;
  top: 100%;
  right: 25px;
  width: 0;
  height: 0;
  border-top: solid 10px var(--menu-surface-color);
  border-left: solid 10px transparent;
  border-right: solid 10px transparent;
}

.button-bar-top #export-menu {
  top: 92px;
  bottom: auto;
}
.button-bar-top #export-menu:after {
  top: auto;
  bottom: 100%;
  border-top: none;
  border-bottom: solid 10px var(--menu-surface-color);
}

#export-menu h3 {
  margin-top: 0;
  margin-left: 2px;
  margin-bottom: 5px;
  font-size: 1.2em;
}

#export-menu button {
  background-color: var(--menu-surface-color);
  color: var(--primary-text-color);
  border: 1px solid var(--menu-border-color);
  border-radius: 3px;
  padding: 10px;
  font-size: 14px;
  transition: all 100ms linear;
}
#export-menu button:hover {
  border-color: var(--primary-outline-color);
  background-color: rgba(0, 0, 0, 0.03);
  box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.1);
}
