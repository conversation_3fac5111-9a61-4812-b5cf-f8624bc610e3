2025-05-13 21:18:15 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:18:15 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:18:15 [DEBUG] Checking for Python installation...
2025-05-13 21:18:15 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:18:15 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:18:15 [DEBUG] Checking for package: selenium
2025-05-13 21:18:16 [DEBUG] Package 'selenium' found.
2025-05-13 21:18:16 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:18:17 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:18:17 [DEBUG] Checking for package: requests
2025-05-13 21:18:18 [DEBUG] Package 'requests' found.
2025-05-13 21:18:18 [DEBUG] Checking for package: configparser
2025-05-13 21:18:20 [DEBUG] Package 'configparser' found.
2025-05-13 21:18:20 [INFO] All required Python packages seem to be installed.
2025-05-13 21:18:20 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:18:20 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:18:20 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:18:20 [INFO] Launching main Python script...
2025-05-13 21:18:20 [DEBUG] Full command: D:\Users\d0nbx\anaconda3\python.exe "C:\main\proxyseller\main.py"
2025-05-13 21:18:21 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-18-15.log
2025-05-13 21:18:21 [ERROR] Python script exited with code 5.
2025-05-13 21:18:21 [INFO] Launcher script execution finished. Final Exit Code: 5
2025-05-13 21:18:21 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
