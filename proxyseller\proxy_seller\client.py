"""
ProxySeller API client implementation for managing proxy services.
"""

import logging
import requests
import time
from typing import Dict, List, Optional, Any

from .exceptions import ProxySellerAPIException

logger = logging.getLogger(__name__)

class ProxySellerAPIClient:
    """
    Client for interacting with the ProxySeller API.
    
    This client provides methods to authenticate, manage proxies,
    retrieve proxy information, and perform other operations against
    the ProxySeller API.
    """
    
    BASE_URL = "https://api.proxy-seller.com/api"
    
    def __init__(self, api_key: str):
        """
        Initialize the ProxySeller API client.
        
        Args:
            api_key: API key for authentication with ProxySeller API
        """
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None, 
        data: Optional[Dict[str, Any]] = None,
        retry_count: int = 3,
        retry_delay: int = 2
    ) -> Dict[str, Any]:
        """
        Make a request to the ProxySeller API.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without base URL)
            params: Query parameters
            data: Request body data
            retry_count: Number of retry attempts
            retry_delay: Delay between retries in seconds
            
        Returns:
            API response as dictionary
            
        Raises:
            ProxySellerAPIException: If the API request fails
        """
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        
        for attempt in range(retry_count):
            try:
                if method.upper() == 'GET':
                    response = self.session.get(url, params=params)
                elif method.upper() == 'POST':
                    response = self.session.post(url, params=params, json=data)
                elif method.upper() == 'PUT':
                    response = self.session.put(url, params=params, json=data)
                elif method.upper() == 'DELETE':
                    response = self.session.delete(url, params=params, json=data)
                else:
                    raise ValueError("Unsupported HTTP method: %s" % method)
                
                response.raise_for_status()
                return response.json()
                
            except requests.RequestException as e:
                logger.error("API request failed: %s", str(e))
                
                if attempt < retry_count - 1:
                    logger.info("Retrying in %s seconds...", retry_delay)
                    time.sleep(retry_delay)
                    continue
                
                if hasattr(e, 'response') and e.response is not None:
                    status_code = e.response.status_code
                    try:
                        error_data = e.response.json()
                        error_message = error_data.get('message', str(e))
                    except ValueError:
                        error_message = e.response.text or str(e)
                else:
                    status_code = None
                    error_message = str(e)
                
                raise ProxySellerAPIException(
                    message=error_message, 
                    status_code=status_code,
                    response=e.response if hasattr(e, 'response') else None
                ) from e
    
    def get_proxies(self, proxy_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available proxies.
        
        Args:
            proxy_type: Filter by proxy type (http, socks, etc.)
            
        Returns:
            List of proxy objects
        """
        params = {}
        if proxy_type:
            params['type'] = proxy_type
            
        response = self._make_request('GET', '/proxies', params=params)
        return response.get('data', [])
    
    def get_proxy(self, proxy_id: str) -> Dict[str, Any]:
        """
        Get details of a specific proxy.
        
        Args:
            proxy_id: ID of the proxy to retrieve
            
        Returns:
            Proxy details
        """
        response = self._make_request('GET', f'/proxies/{proxy_id}')
        return response.get('data', {})
    
    def create_proxy(
        self, 
        proxy_type: str, 
        country: str, 
        quantity: int = 1,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create new proxy or proxies.
        
        Args:
            proxy_type: Type of proxy (http, socks, etc.)
            country: Country code
            quantity: Number of proxies to create
            **kwargs: Additional parameters
            
        Returns:
            Created proxy information
        """
        data = {
            'type': proxy_type,
            'country': country,
            'quantity': quantity,
            **kwargs
        }
        
        response = self._make_request('POST', '/proxies', data=data)
        return response.get('data', {})
    
    def delete_proxy(self, proxy_id: str) -> bool:
        """
        Delete a proxy.
        
        Args:
            proxy_id: ID of the proxy to delete
            
        Returns:
            True if successful
        """
        self._make_request('DELETE', f'/proxies/{proxy_id}')
        return True
    
    def get_balance(self) -> Dict[str, Any]:
        """
        Get account balance information.
        
        Returns:
            Balance information
        """
        response = self._make_request('GET', '/account/balance')
        return response.get('data', {})
    
    def verify_connection(self) -> bool:
        """
        Verify API connection and authentication.
        
        Returns:
            True if connection and authentication are valid
        """
        try:
            self._make_request('GET', '/account/info')
            return True
        except ProxySellerAPIException:
            return False
