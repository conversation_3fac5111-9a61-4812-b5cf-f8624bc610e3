@echo off
REM Launch the PowerShell ProxySeller setup script and keep the window open after execution

set SCRIPT_PATH=%~dp0start_proxy_setup.ps1

REM Use Windows PowerShell if available, fallback to pwsh (PowerShell Core) if installed
where powershell >nul 2>&1
if %errorlevel%==0 (
    powershell -NoProfile -ExecutionPolicy Bypass -File "%SCRIPT_PATH%"
    set EXITCODE=%errorlevel%
) else (
    where pwsh >nul 2>&1
    if %errorlevel%==0 (
        pwsh -NoProfile -ExecutionPolicy Bypass -File "%SCRIPT_PATH%"
        set EXITCODE=%errorlevel%
    ) else (
        echo [ERROR] PowerShell is not installed on this system.
        set EXITCODE=1
    )
)
echo.
echo ======================================
echo   PowerShell script exited with code: %EXITCODE%
echo   (If there was an error, scroll up for details.)
echo ======================================
echo Press any key to close this window...
pause >nul
exit /b %EXITCODE%
