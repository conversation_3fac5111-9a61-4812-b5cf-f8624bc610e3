#!/usr/bin/env python3
"""
ProxySeller Firefox Container Setup - Simplified Launcher
This script ensures the proper import of the Proxy-Seller API regardless of environment
"""

import argparse
import os
import sys
from pathlib import Path


def find_firefox_profile():
    """Find the Firefox profile path automatically"""
    appdata_path = os.environ.get("APPDATA")
    if not appdata_path:
        return None

    # Standard Firefox profile location on Windows
    firefox_dir = Path(appdata_path) / "Mozilla" / "Firefox"
    profiles_dir = firefox_dir / "Profiles"

    if not profiles_dir.exists():
        return None

    # Look for profiles, preferring default-release
    profiles = list(profiles_dir.glob("*.default-release"))
    if profiles:
        return str(profiles[0])

    # If no default-release, look for any profile
    profiles = list(profiles_dir.glob("*.*"))
    if profiles:
        return str(profiles[0])

    return None


def setup_proxy_containers():
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Setup Firefox containers with proxies from proxy-seller.io"
    )
    parser.add_argument("-k", "--api-key", required=True, help="Proxy-Seller API key")
    parser.add_argument(
        "-n",
        "--num-containers",
        type=int,
        default=5,
        help="Number of containers to create (default: 5)",
    )
    parser.add_argument(
        "-t",
        "--proxy-type",
        default="http",
        choices=["http", "socks5"],
        help="Type of proxy to use (default: http)",
    )
    parser.add_argument(
        "-p", "--profile-path", help="Path to Firefox profile (optional)"
    )

    args = parser.parse_args()

    # Add geckodriver to PATH
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.environ["PATH"] = os.environ["PATH"] + os.pathsep + current_dir

    # Ensure the API module can be imported
    api_dir = os.path.join(current_dir, "user-api-python")
    if api_dir not in sys.path:
        sys.path.append(api_dir)

    # Find Firefox profile if not specified
    if not args.profile_path:
        profile_path = find_firefox_profile()
        if profile_path:
            print(f"Using Firefox profile: {profile_path}")
            args.profile_path = profile_path
        else:
            print("WARNING: Could not automatically find Firefox profile")
            print(r"Available profiles in %APPDATA%\Mozilla\Firefox\Profiles:")
            profiles_dir = (
                Path(os.environ.get("APPDATA", "")) / "Mozilla" / "Firefox" / "Profiles"
            )
            if profiles_dir.exists():
                for profile in profiles_dir.iterdir():
                    if profile.is_dir():
                        print(f" - {profile.name} => {profile}")
            print(
                "\nPlease run again with -p PROFILE_PATH to specify which profile to use."
            )
            return 1

    # Import the main script as a module
    import main as proxy_setup_main

    # Create and run the setup
    setup = proxy_setup_main.ProxyContainerSetup(
        api_key=args.api_key,
        num_containers=args.num_containers,
        proxy_type=args.proxy_type,
        firefox_profile_path=args.profile_path,
    )

    success = setup.run()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(setup_proxy_containers())
