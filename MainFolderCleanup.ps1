# MainFolderCleanup.ps1
# This script cleans up the C:\main directory by moving non-essential files to C:\main_cleaned

# Define source and destination paths
$sourceDir = "C:\main"
$destDir = "C:\main_cleaned"

# Define essential folders/files that should remain in C:\main
$essentialItems = @(
    ".venv",
    "LaunchTerminal.bat",
    "launcher.ps1",
    "requirements.txt",
    "removal3.1.py",
    "removalBanned3.0.py",
    "run.py",
    "run_banned.py",
    "run_deactivated.py",
    "Deactivated.bat",
    "menu_system.py",
    "menu_system_banned.py",
    "config.json",
    "config_banned.json",
    "mainv3.code-workspace",
    "session.pkl",
    "cookies.pkl",
    "cookies_deactivated.pkl",
    "cookiesBAK",
    "logs",
    "terminal",
    "cookie_editor",
    "MainFolderCleanup.ps1",
    "CleanupMain.bat",
    "overview.md",
    "config_module.py"
    "config_management.md"
	"session_banned.pkl"
)

# Check if source directory exists
if (-not (Test-Path $sourceDir)) {
    Write-Host "Error: Source directory C:\main does not exist!" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Create destination directory if it doesn't exist
if (-not (Test-Path $destDir)) {
    try {
        New-Item -Path $destDir -ItemType Directory -Force | Out-Null
        Write-Host "Created destination directory: $destDir" -ForegroundColor Green
    }
    catch {
        Write-Host "Error creating destination directory: $_" -ForegroundColor Red
        Write-Host "Press any key to exit..."
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        exit
    }
}

# Get current date/time for the backup folder
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$cleanedDir = Join-Path -Path $destDir -ChildPath "Cleanup_$timestamp"

# Create timestamped subfolder in the destination directory
try {
    New-Item -Path $cleanedDir -ItemType Directory -Force | Out-Null
    Write-Host "Created cleanup directory: $cleanedDir" -ForegroundColor Green
}
catch {
    Write-Host "Error creating cleanup directory: $_" -ForegroundColor Red
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# Get all items in the source directory
$allItems = Get-ChildItem -Path $sourceDir -Force

# Counter for moved items
$movedCount = 0
$errorCount = 0

# Process each item
foreach ($item in $allItems) {
    $itemName = $item.Name

    # Skip if the item is in the essential list
    if ($essentialItems -contains $itemName) {
        Write-Host "Keeping essential item: $itemName" -ForegroundColor Cyan
        continue
    }

    # Define destination path
    $destPath = Join-Path -Path $cleanedDir -ChildPath $itemName

    # Move the item
    try {
        Move-Item -Path $item.FullName -Destination $destPath -Force
        Write-Host "Moved: $itemName" -ForegroundColor Green
        $movedCount++
    }
    catch {
        Write-Host "Error moving $itemName`: $_" -ForegroundColor Red
        $errorCount++
    }
}

# Summary
Write-Host "`n==== Cleanup Summary ====" -ForegroundColor Yellow
Write-Host "Source directory: $sourceDir" -ForegroundColor White
Write-Host "Destination directory: $cleanedDir" -ForegroundColor White
Write-Host "Items moved: $movedCount" -ForegroundColor Green
if ($errorCount -gt 0) {
    Write-Host "Errors encountered: $errorCount" -ForegroundColor Red
}

Write-Host "`nCleanup completed! Non-essential items have been moved to:" -ForegroundColor Yellow
Write-Host $cleanedDir -ForegroundColor White
Write-Host "`nEssential items kept in original location:" -ForegroundColor Yellow
foreach ($item in $essentialItems) {
    Write-Host " - $item" -ForegroundColor Cyan
}

# Keep console window open
Write-Host "`nPress any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
