2025-05-13 21:15:07 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:15:07 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:15:07 [DEBUG] Checking for Python installation...
2025-05-13 21:15:07 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:15:07 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:15:07 [DEBUG] Checking for package: selenium
2025-05-13 21:15:08 [DEBUG] Package 'selenium' found.
2025-05-13 21:15:08 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:15:09 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:15:09 [DEBUG] Checking for package: requests
2025-05-13 21:15:10 [DEBUG] Package 'requests' found.
2025-05-13 21:15:10 [DEBUG] Checking for package: configparser
2025-05-13 21:15:11 [DEBUG] Package 'configparser' found.
2025-05-13 21:15:11 [INFO] All required Python packages seem to be installed.
2025-05-13 21:15:11 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:15:11 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:15:11 [FATAL] A critical error occurred in the PowerShell launcher: A parameter cannot be found that matches parameter name 'CurrentConfig'.
2025-05-13 21:15:11 [DEBUG] Stack Trace: at <ScriptBlock>, C:\main\proxyseller\start_proxy_setup.ps1: line 283
2025-05-13 21:15:11 [INFO] Launcher script execution finished. Final Exit Code: 255
2025-05-13 21:15:11 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
