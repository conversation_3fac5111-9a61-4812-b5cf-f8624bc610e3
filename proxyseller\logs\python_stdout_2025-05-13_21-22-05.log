INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:22:10,703 - INFO - [__main__:load_config_and_args:2384] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 21:22:10,704 - INFO - [__main__:load_config_and_args:2386] - Config sections: []
2025-05-13 21:22:10,705 - INFO - [__main__:load_config_and_args:2387] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}

CRITICAL CONFIGURATION ERROR: Critical configuration errors: API_KEY (must be provided via config.ini or --api-key). Please update config.ini or use correct command-line arguments.

2025-05-13 21:22:10,706 - CRITICAL - [__main__:load_config_and_args:2510] - Critical configuration errors: API_KEY (must be provided via config.ini or --api-key). Please update config.ini or use correct command-line arguments.
