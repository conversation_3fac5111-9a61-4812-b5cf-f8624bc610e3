INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:30:15,497 - INFO - [__main__:load_config_and_args:2444] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 22:30:15,499 - INFO - [__main__:load_config_and_args:2446] - Config sections: []
2025-05-13 22:30:15,500 - INFO - [__main__:load_config_and_args:2447] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 22:30:15,501 - INFO - [__main__:load_config_and_args:2581] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 22:30:15,501 - INFO - [__main__:<module>:2625] - Console logging level set to: INFO
2025-05-13 22:30:15,502 - INFO - [__main__:<module>:2626] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:30:15,503 - INFO - [__main__:<module>:2627] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 22:30:15,503 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 22:30:15,503 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 22:30:15,504 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 22:30:15,505 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 22:30:15,506 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:30:15,506 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:30:15,706 - INFO - [__main__:run:1437] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-13 22:30:15,707 - INFO - [__main__:run:1443] - Starting proxy container setup process...
2025-05-13 22:30:16,602 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 22:30:16,804 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 22:30:16,806 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 22:30:16,806 - INFO - [__main__:fetch_proxies:480] - Calling proxyList(type='ipv4') API method...
2025-05-13 22:30:17,035 - INFO - [__main__:fetch_proxies:539] - Found 0 existing, active, matching USA HTTP proxies via API call and local filtering.
2025-05-13 22:30:17,036 - INFO - [__main__:fetch_proxies:550] - Attempting to create 5 new USA HTTP proxies.
2025-05-13 22:30:17,240 - ERROR - [__main__:create_usa_proxies:766] - No countries returned from referenceList('ipv4'). Check your API key/account permissions.
2025-05-13 22:30:17,240 - WARNING - [__main__:fetch_proxies:617] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-13 22:30:17,241 - WARNING - [__main__:fetch_proxies:715] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-13 22:30:17,241 - ERROR - [__main__:fetch_proxies:724] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-13 22:30:17,241 - ERROR - [__main__:run:1455] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 22:30:17,242 - ERROR - [__main__:<module>:2641] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 22:30:17,242 - ERROR - [__main__:<module>:2721] - ==================================================
2025-05-13 22:30:17,242 - ERROR - [__main__:<module>:2722] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 22:30:17,243 - ERROR - [__main__:<module>:2723] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 22:30:17,243 - ERROR - [__main__:<module>:2726] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 22:30:17,244 - ERROR - [__main__:<module>:2729] - ==================================================
2025-05-13 22:30:17,244 - INFO - [__main__:<module>:2735] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
