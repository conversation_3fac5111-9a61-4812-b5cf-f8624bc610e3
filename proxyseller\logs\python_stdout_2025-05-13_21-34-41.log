INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:34:46,721 - INFO - [__main__:load_config_and_args:2405] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 21:34:46,721 - INFO - [__main__:load_config_and_args:2407] - Config sections: []
2025-05-13 21:34:46,723 - INFO - [__main__:load_config_and_args:2408] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 21:34:46,724 - INFO - [__main__:load_config_and_args:2539] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 21:34:46,724 - INFO - [__main__:<module>:2583] - Console logging level set to: INFO
2025-05-13 21:34:46,724 - INFO - [__main__:<module>:2584] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:34:46,726 - INFO - [__main__:<module>:2585] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 21:34:46,726 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 21:34:46,726 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 21:34:46,727 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 21:34:46,727 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 21:34:46,728 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:34:46,728 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:34:46,729 - INFO - [__main__:run:1404] - Starting proxy container setup process...
2025-05-13 21:34:46,987 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 21:34:47,183 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 21:34:47,183 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 21:34:47,184 - INFO - [__main__:fetch_proxies:480] - Calling proxyList(type='ipv4') API method...
2025-05-13 21:34:47,384 - INFO - [__main__:fetch_proxies:535] - Found 0 existing, active, matching USA HTTP proxies via direct API filter.
2025-05-13 21:34:47,385 - INFO - [__main__:fetch_proxies:546] - Attempting to create 5 new USA HTTP proxies.
2025-05-13 21:34:47,577 - INFO - [__main__:create_usa_proxies:788] - Available countries from API:
2025-05-13 21:34:47,577 - INFO - [__main__:create_usa_proxies:791] - Available periods from API:
2025-05-13 21:34:47,577 - WARNING - [__main__:create_usa_proxies:796] - Could not find USA country ID from reference list. Using fallback ID 1 (often USA, but may vary by provider).
2025-05-13 21:34:47,577 - ERROR - [__main__:create_usa_proxies:1011] - Unexpected error in create_usa_proxies: name 'period_list' is not defined
2025-05-13 21:34:47,579 - WARNING - [__main__:fetch_proxies:613] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-13 21:34:47,580 - WARNING - [__main__:fetch_proxies:711] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-13 21:34:47,581 - ERROR - [__main__:fetch_proxies:720] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-13 21:34:47,581 - ERROR - [__main__:run:1416] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 21:34:47,582 - ERROR - [__main__:<module>:2599] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 21:34:47,582 - ERROR - [__main__:<module>:2679] - ==================================================
2025-05-13 21:34:47,582 - ERROR - [__main__:<module>:2680] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 21:34:47,583 - ERROR - [__main__:<module>:2681] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 21:34:47,583 - ERROR - [__main__:<module>:2684] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 21:34:47,583 - ERROR - [__main__:<module>:2687] - ==================================================
2025-05-13 21:34:47,583 - INFO - [__main__:<module>:2693] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
