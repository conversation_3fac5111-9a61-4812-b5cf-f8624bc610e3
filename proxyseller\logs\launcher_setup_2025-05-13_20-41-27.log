2025-05-13 20:41:27 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.1 ---
2025-05-13 20:41:27 [WARNING] Not running as Administrator. Python package installation (pip) might require it if not in user context or if system-wide install is attempted.
2025-05-13 20:41:27 [DEBUG] Checking for Python installation...
2025-05-13 20:41:27 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-13 20:41:27 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 20:41:27 [DEBUG] Checking for package: selenium
2025-05-13 20:41:28 [DEBUG] Package 'selenium' found.
2025-05-13 20:41:28 [DEBUG] Checking for package: webdriver_manager
2025-05-13 20:41:29 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 20:41:29 [DEBUG] Checking for package: requests
2025-05-13 20:41:30 [DEBUG] Package 'requests' found.
2025-05-13 20:41:30 [DEBUG] Checking for package: configparser
2025-05-13 20:41:31 [DEBUG] Package 'configparser' found.
2025-05-13 20:41:31 [INFO] All required Python packages seem to be installed.
2025-05-13 20:41:31 [DEBUG] Configuring script parameters...
2025-05-13 20:41:31 [INFO] Found config.ini. Loading settings from C:\main\proxyseller\config.ini
2025-05-13 20:41:31 [WARNING] Error reading or parsing config.ini: Data line '[DEFAULT]' is not in 'name=value' format. . Will proceed with defaults/prompts.
2025-05-13 20:41:31 [DEBUG] Prompting for configuration if necessary.
