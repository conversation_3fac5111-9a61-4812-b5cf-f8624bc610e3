INFO: Detailed logs will be written to: C:\Users\<USER>\proxy_setup.log
2025-05-31 13:15:58,949 - WARNING - [__main__:<module>:117] - Could not import ProxySellerAPIClient from installed package. Trying local SDK path...
2025-05-31 13:15:58,951 - INFO - [__main__:<module>:138] - Successfully imported ProxySellerAPIClient from local SDK path: C:\main\proxyseller\user-api-python
2025-05-31 13:15:58,952 - INFO - [__main__:load_config_and_args:2014] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-31 13:15:58,953 - INFO - [__main__:load_config_and_args:2110] - Effective settings: Num Containers: 3, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True
2025-05-31 13:15:58,954 - INFO - [__main__:<module>:2327] - Console logging level set to: INFO
2025-05-31 13:15:58,954 - INFO - [__main__:<module>:2328] - Detailed file logging (DEBUG level) at: C:\Users\<USER>\proxy_setup.log
2025-05-31 13:15:58,955 - INFO - [__main__:<module>:2330] - ========================================
 Starting ProxySeller Firefox Setup (Resident SDK Mode)
========================================
2025-05-31 13:15:58,955 - INFO - [__main__:__init__:373] - Initializing ProxyContainerSetup...
2025-05-31 13:15:58,956 - INFO - [__main__:__init__:388] - Attempting to determine Firefox profile path...
2025-05-31 13:15:58,957 - INFO - [__main__:_find_firefox_profile:504] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-31 13:15:58,958 - INFO - [__main__:__init__:413] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-31 13:15:59,150 - INFO - [__main__:run:1160] - Detected public IP for this script: *************
2025-05-31 13:15:59,151 - INFO - [__main__:run:1166] - Starting proxy container setup process (using SDK for resident proxies)...
2025-05-31 13:15:59,151 - INFO - [__main__:init_api_client:571] - Initializing ProxySeller API SDK client...
2025-05-31 13:15:59,720 - INFO - [__main__:init_api_client:591] - Successfully pinged Proxy-Seller API via SDK (timestamp: 1748722585).
2025-05-31 13:15:59,721 - INFO - [__main__:get_package_info_sdk:196] - Fetching resident proxy package information via SDK...
2025-05-31 13:15:59,980 - INFO - [__main__:get_package_info_sdk:201] - SDK Package Info: Active: True, Traffic Limit: 3221225472, Usage: 857970291, Left: 0
2025-05-31 13:15:59,981 - INFO - [__main__:init_api_client:605] - Successfully initialized resident proxy manager with SDK and fetched package info.
2025-05-31 13:15:59,981 - INFO - [__main__:fetch_proxies:627] - Fetching resident proxies for 3 containers using SDK...
2025-05-31 13:15:59,981 - INFO - [__main__:get_existing_lists_sdk:282] - Fetching existing resident proxy lists via SDK...
2025-05-31 13:16:05,811 - INFO - [__main__:get_existing_lists_sdk:300] - SDK: Found 25 existing resident proxy lists (direct list).
2025-05-31 13:16:05,813 - WARNING - [__main__:fetch_proxies:669] - List 'US Alabama Abbeville Brightspeed Fiber' (ID: 11783497) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Alabama", "city": "Abbeville", "isp": "Brightspeed Fiber"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,814 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Alabama Abbeville Brightspeed Fiber' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,814 - WARNING - [__main__:fetch_proxies:669] - List 'US' (ID: 11784060) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,815 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,815 - WARNING - [__main__:fetch_proxies:669] - List 'US Arizona' (ID: 11784161) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Arizona", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,815 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Arizona' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,816 - WARNING - [__main__:fetch_proxies:669] - List 'US Colorado Aurora Comcast Cable' (ID: 11786948) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Colorado", "city": "Aurora", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,817 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Colorado Aurora Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,817 - WARNING - [__main__:fetch_proxies:669] - List 'US Delaware Camden Comcast Cable' (ID: 11786965) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Delaware", "city": "Camden", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,817 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Delaware Camden Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,818 - WARNING - [__main__:fetch_proxies:669] - List 'US Idaho Boise CenturyLink' (ID: 11787009) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Idaho", "city": "Boise", "isp": "CenturyLink"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,818 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Idaho Boise CenturyLink' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,819 - WARNING - [__main__:fetch_proxies:669] - List 'US Maine Calais Pioneer Broadband' (ID: 11787038) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Maine", "city": "Calais", "isp": "Pioneer Broadband"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,819 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Maine Calais Pioneer Broadband' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,819 - WARNING - [__main__:fetch_proxies:669] - List 'US Iowa Aurora Aureon' (ID: 11787282) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Iowa", "city": "Aurora", "isp": "Aureon"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,820 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Iowa Aurora Aureon' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,820 - WARNING - [__main__:fetch_proxies:669] - List 'EG' (ID: 11865732) has 'geo' data of unexpected type: list. Content: [{"country": "EG", "region": "", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,820 - WARNING - [__main__:fetch_proxies:669] - List 'go2' (ID: 12060676) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Maryland", "city": "Gaithersburg", "isp": "Verizon Fios"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,821 - WARNING - [__main__:fetch_proxies:669] - List 'go2 kansas' (ID: 12062535) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Kansas", "city": "Garden City", "isp": "AT&T Internet"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,821 - WARNING - [__main__:fetch_proxies:669] - List 'US Kansas Garden City Cox Communications' (ID: 12062806) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Kansas", "city": "Garden City", "isp": "Cox Communications"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,822 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Kansas Garden City Cox Communications' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,822 - WARNING - [__main__:fetch_proxies:669] - List 'US Colorado Englewood Ting Fiber' (ID: 12067282) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Colorado", "city": "Englewood", "isp": "Ting Fiber"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,822 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Colorado Englewood Ting Fiber' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,823 - WARNING - [__main__:fetch_proxies:669] - List 'US Colorado Englewood CenturyLink' (ID: 12069523) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Colorado", "city": "Englewood", "isp": "CenturyLink"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,823 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Colorado Englewood CenturyLink' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,823 - WARNING - [__main__:fetch_proxies:669] - List 'US Pennsylvania Berlin Comcast Cable' (ID: 12093216) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Pennsylvania", "city": "Berlin", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,824 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Pennsylvania Berlin Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,825 - WARNING - [__main__:fetch_proxies:669] - List 'US Pennsylvania Meyersdale Comcast Cable' (ID: 12093375) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Pennsylvania", "city": "Meyersdale", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,825 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Pennsylvania Meyersdale Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,826 - WARNING - [__main__:fetch_proxies:669] - List '1' (ID: 12108673) has 'geo' data of unexpected type: list. Content: [{"country": "", "region": "", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,826 - WARNING - [__main__:fetch_proxies:669] - List 'US Arizona Camp Verde Optimum' (ID: 12109107) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Arizona", "city": "Camp Verde", "isp": "Optimum"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,827 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Arizona Camp Verde Optimum' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,827 - WARNING - [__main__:fetch_proxies:669] - List 'US Idaho Caldwell Sparklight' (ID: 12109122) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Idaho", "city": "Caldwell", "isp": "Sparklight"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,827 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Idaho Caldwell Sparklight' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,828 - WARNING - [__main__:fetch_proxies:669] - List 'US Indiana Cambridge City New Lisbon Telephone Co.' (ID: 12109134) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Indiana", "city": "Cambridge City", "isp": "New Lisbon Telephone Co."}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,828 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Indiana Cambridge City New Lisbon Telephone Co.' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,829 - WARNING - [__main__:fetch_proxies:669] - List 'US Alabama Athens AT&T Internet' (ID: 12109167) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Alabama", "city": "Athens", "isp": "AT&T Internet"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,829 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Alabama Athens AT&T Internet' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,829 - WARNING - [__main__:fetch_proxies:669] - List 'US Kentucky Albany Windstream Communications' (ID: 12109179) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Kentucky", "city": "Albany", "isp": "Windstream Communications"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,830 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Kentucky Albany Windstream Communications' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,830 - WARNING - [__main__:fetch_proxies:669] - List 'US Iowa Des Moines CenturyLink' (ID: 12109204) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Iowa", "city": "Des Moines", "isp": "CenturyLink"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,831 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Iowa Des Moines CenturyLink' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,831 - WARNING - [__main__:fetch_proxies:669] - List 'US Nevada Las Vegas AT&T Internet' (ID: 12109251) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Nevada", "city": "Las Vegas", "isp": "AT&T Internet"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,831 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Nevada Las Vegas AT&T Internet' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,832 - WARNING - [__main__:fetch_proxies:669] - List 'US Colorado Englewood Comcast Cable' (ID: 12121346) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Colorado", "city": "Englewood", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-31 13:16:05,832 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Colorado Englewood Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-31 13:16:05,833 - INFO - [__main__:fetch_proxies:700] - Found 21 US-targeted resident lists after filtering. Will attempt to use up to 3 of them.
2025-05-31 13:16:05,833 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 1/3 from list: 'US Pennsylvania Berlin Comcast Cable' (ID: 12093216)
2025-05-31 13:16:05,833 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 12093216, protocol: http via SDK...
2025-05-31 13:16:06,582 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 12093216: 2f6f96ab2680ed41:RNW...
2025-05-31 13:16:06,582 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 1: res.proxy-seller.com:10000 from list 'US Pennsylvania Berlin Comcast Cable' (Geo: Unknown, Unknown (from title))
2025-05-31 13:16:07,084 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 2/3 from list: 'US Pennsylvania Meyersdale Comcast Cable' (ID: 12093375)
2025-05-31 13:16:07,085 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 12093375, protocol: http via SDK...
2025-05-31 13:16:08,198 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 12093375: 388a97006de4ec14:RNW...
2025-05-31 13:16:08,199 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 2: res.proxy-seller.com:10000 from list 'US Pennsylvania Meyersdale Comcast Cable' (Geo: Unknown, Unknown (from title))
2025-05-31 13:16:08,700 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 3/3 from list: 'US Indiana Cambridge City New Lisbon Telephone Co.' (ID: 12109134)
2025-05-31 13:16:08,700 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 12109134, protocol: http via SDK...
2025-05-31 13:16:11,431 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 12109134: 9e145e25cb91951d:RNW...
2025-05-31 13:16:11,432 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 3: res.proxy-seller.com:10000 from list 'US Indiana Cambridge City New Lisbon Telephone Co.' (Geo: Unknown, Unknown (from title))
2025-05-31 13:16:11,932 - INFO - [__main__:fetch_proxies:774] - SDK: Successfully configured 3 resident proxies.
2025-05-31 13:16:11,933 - INFO - [__main__:create_or_update_containers:833] - Creating or updating Firefox container definitions...
2025-05-31 13:16:11,934 - INFO - [__main__:get_existing_containers:811] - Found 10 existing containers in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default\containers.json
2025-05-31 13:16:11,935 - INFO - [__main__:create_or_update_containers:891] - Found 5 existing 'ProxySeller #' containers. Max number suffix found: 5
2025-05-31 13:16:11,935 - INFO - [__main__:create_or_update_containers:953] - Writing updated container data to: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default\containers.json
2025-05-31 13:16:11,936 - INFO - [__main__:create_or_update_containers:972] - Final list for setup: Using 3 container definitions matched with 3 proxies.
2025-05-31 13:16:11,936 - INFO - [__main__:generate_foxyproxy_config:996] - Generating FoxyProxy configuration...
2025-05-31 13:16:11,937 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #01' to use proxy res.proxy-seller.com:10000 (from list 'US Pennsylvania Berlin Comcast Cable')
2025-05-31 13:16:11,938 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #02' to use proxy res.proxy-seller.com:10000 (from list 'US Pennsylvania Meyersdale Comcast Cable')
2025-05-31 13:16:11,938 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #03' to use proxy res.proxy-seller.com:10000 (from list 'US Indiana Cambridge City New Lisbon Telephone Co.')
2025-05-31 13:16:11,939 - INFO - [__main__:generate_foxyproxy_config:1146] - FoxyProxy configuration generated and saved to: C:\Users\<USER>\foxyproxy_config.json
2025-05-31 13:16:11,940 - INFO - [__main__:create_summary_file:1868] - Creating proxy-container summary file at: C:\Users\<USER>\proxy_container_summary.txt
2025-05-31 13:16:11,941 - INFO - [__main__:create_summary_file:1934] - Summary file created successfully.
2025-05-31 13:16:11,941 - INFO - [__main__:run:1214] - Attempting automated import of FoxyProxy configuration via Selenium...
2025-05-31 13:16:11,942 - INFO - [__main__:import_foxyproxy_config:1433] - Starting FoxyProxy import via Selenium...
2025-05-31 13:16:11,943 - INFO - [__main__:import_foxyproxy_config:1443] - FoxyProxy Selenium import attempt 1/2...
2025-05-31 13:16:15,534 - INFO - [__main__:import_foxyproxy_config:1467] - Initializing WebDriver for FoxyProxy options page access...
2025-05-31 13:16:20,039 - ERROR - [__main__:import_foxyproxy_config:1750] - WebDriver error during FoxyProxy import (Attempt 1/2): Message: Process unexpectedly closed with status 0

2025-05-31 13:16:20,040 - INFO - [__main__:import_foxyproxy_config:1797] - Waiting 5 seconds before retrying FoxyProxy import...
2025-05-31 13:16:25,041 - INFO - [__main__:import_foxyproxy_config:1443] - FoxyProxy Selenium import attempt 2/2...
2025-05-31 13:16:27,458 - INFO - [__main__:import_foxyproxy_config:1467] - Initializing WebDriver for FoxyProxy options page access...
2025-05-31 13:16:31,316 - INFO - [__main__:import_foxyproxy_config:1476] - Browser for FoxyProxy import started (PID: 61584).
2025-05-31 13:16:31,317 - INFO - [__main__:import_foxyproxy_config:1487] - Attempting to find FoxyProxy Internal UUID via about:debugging...
2025-05-31 13:17:23,465 - ERROR - [__main__:import_foxyproxy_config:1548] - Error finding FoxyProxy UUID on about:debugging: Message: 
Stacktrace:
RemoteError@chrome://remote/content/shared/RemoteError.sys.mjs:8:8
WebDriverError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:199:5
NoSuchElementError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:552:5
dom.find/</<@chrome://remote/content/shared/DOM.sys.mjs:136:16

2025-05-31 13:17:23,466 - ERROR - [__main__:import_foxyproxy_config:1554] - Automated FoxyProxy Internal UUID detection failed. Prompting for manual input.

============================================================
 ACTION REQUIRED: Manual FoxyProxy UUID Needed
 1. Open Firefox, go to: about:debugging#/runtime/this-firefox
 2. Find 'FoxyProxy Standard' or 'Basic'.
 3. Copy its 'Internal UUID' value.
============================================================
  Paste Internal UUID here and press Enter: 