2025-05-14 04:01:33 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-14 04:01:33 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-14 04:01:33 [DEBUG] Checking for Python installation...
2025-05-14 04:01:33 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-14 04:01:33 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-14 04:01:33 [DEBUG] Checking for package: selenium
2025-05-14 04:01:34 [DEBUG] Package 'selenium' found.
2025-05-14 04:01:34 [DEBUG] Checking for package: webdriver_manager
2025-05-14 04:01:35 [DEBUG] Package 'webdriver_manager' found.
2025-05-14 04:01:35 [DEBUG] Checking for package: requests
2025-05-14 04:01:36 [DEBUG] Package 'requests' found.
2025-05-14 04:01:36 [DEBUG] Checking for package: configparser
2025-05-14 04:01:37 [DEBUG] Package 'configparser' found.
2025-05-14 04:01:37 [INFO] All required Python packages seem to be installed.
2025-05-14 04:01:37 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-14 04:01:37 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-14 04:01:37 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-14 04:01:39 [INFO] Launching main Python script...
2025-05-14 04:01:39 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py"
2025-05-14 04:01:46 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-14_04-01-33.log
2025-05-14 04:01:46 [ERROR] Python script exited with code 1.
2025-05-14 04:01:46 [INFO] Launcher script execution finished. Final Exit Code: 1
