INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:27:04,071 - INFO - [__main__:load_config_and_args:2434] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 22:27:04,071 - INFO - [__main__:load_config_and_args:2436] - Config sections: []
2025-05-13 22:27:04,071 - INFO - [__main__:load_config_and_args:2437] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 22:27:04,073 - INFO - [__main__:load_config_and_args:2571] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 22:27:04,074 - INFO - [__main__:<module>:2615] - Console logging level set to: INFO
2025-05-13 22:27:04,074 - INFO - [__main__:<module>:2616] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:27:04,074 - INFO - [__main__:<module>:2617] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 22:27:04,076 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 22:27:04,076 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 22:27:04,077 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 22:27:04,077 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 22:27:04,077 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:27:04,079 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:27:04,080 - INFO - [__main__:run:1433] - Starting proxy container setup process...
2025-05-13 22:27:04,962 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 22:27:05,163 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 22:27:05,165 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 22:27:05,165 - INFO - [__main__:fetch_proxies:480] - Calling proxyList(type='ipv4') API method...
2025-05-13 22:27:05,385 - INFO - [__main__:fetch_proxies:539] - Found 0 existing, active, matching USA HTTP proxies via API call and local filtering.
2025-05-13 22:27:05,386 - INFO - [__main__:fetch_proxies:550] - Attempting to create 5 new USA HTTP proxies.
2025-05-13 22:27:05,592 - ERROR - [__main__:create_usa_proxies:766] - No countries returned from referenceList('ipv4'). Check your API key/account permissions.
2025-05-13 22:27:05,596 - WARNING - [__main__:fetch_proxies:617] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-13 22:27:05,596 - WARNING - [__main__:fetch_proxies:715] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-13 22:27:05,596 - ERROR - [__main__:fetch_proxies:724] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-13 22:27:05,597 - ERROR - [__main__:run:1445] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 22:27:05,597 - ERROR - [__main__:<module>:2631] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 22:27:05,597 - ERROR - [__main__:<module>:2711] - ==================================================
2025-05-13 22:27:05,598 - ERROR - [__main__:<module>:2712] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 22:27:05,599 - ERROR - [__main__:<module>:2713] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 22:27:05,599 - ERROR - [__main__:<module>:2716] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 22:27:05,600 - ERROR - [__main__:<module>:2719] - ==================================================
2025-05-13 22:27:05,600 - INFO - [__main__:<module>:2725] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
