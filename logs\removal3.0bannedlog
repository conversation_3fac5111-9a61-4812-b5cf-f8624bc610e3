
================================================================================
SESSION START: 2025-03-30 19:23:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:23:18,974 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 19:23:18,974 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:23:18,974 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:23:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 19:23:18,976 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 19:23:18,976 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:23:18,976 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:23:21 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:23:21,129 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 19:23:21,129 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:23:21,129 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:23:21 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:23:21,131 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 19:23:21,131 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:23:21,131 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:35:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:35:18,179 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 19:35:18,179 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:35:18,179 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:35:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 19:35:18,181 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 19:35:18,182 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:35:18,182 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:35:22 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:35:22,357 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 19:35:22,357 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:35:22,357 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:35:22 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:35:22,360 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 19:35:22,360 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:35:22,360 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:51:06 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:51:06,188 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 19:51:06,188 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:51:06,188 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:51:06 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 19:51:06,190 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 19:51:06,191 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:51:06,191 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:51:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:51:08,289 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 19:51:08,289 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:51:08,289 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 19:51:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 19:51:08,291 | INFO     | __main__:add_log_handler:547 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 19:51:08,291 | DEBUG    | __main__:add_log_handler:548 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 19:51:08,291 | DEBUG    | __main__:add_log_handler:549 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:31:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:31:18,689 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 20:31:18,689 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:31:18,689 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:31:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:31:18,692 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 20:31:18,692 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:31:18,692 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:31:18 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 20:31:18,730 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 20:31:18,730 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:31:18,731 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:31:20 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:31:20,262 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 20:31:20,262 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:31:20,262 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:41:05 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:41:05,103 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 20:41:05,103 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:41:05,103 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:41:05 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:41:05,106 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 20:41:05,106 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:41:05,106 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:41:05 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 20:41:05,144 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 20:41:05,144 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:41:05,144 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 20:41:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 20:41:08,593 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 20:41:08,594 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 20:41:08,594 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:05:53 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:05:53,631 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:05:53,631 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:05:53,632 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:05:53 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:05:53,634 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:05:53,634 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:05:53,634 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:05:53 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:05:53,671 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:05:53,671 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:05:53,671 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:06:04 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:06:04,910 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:06:04,910 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:06:04,910 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:23:58 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:23:58,710 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:23:58,711 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:23:58,711 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:23:58 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:23:58,713 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:23:58,713 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:23:58,713 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:23:58 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:23:58,751 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:23:58,751 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:23:58,751 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:24:00 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:24:00,329 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:24:00,329 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:24:00,329 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:26:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:26:38,411 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:26:38,411 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:26:38,411 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:26:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:26:38,413 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:26:38,413 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:26:38,413 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:26:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:26:38,452 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:26:38,452 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:26:38,452 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:26:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:26:39,879 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:26:39,880 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:26:39,880 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:30:52 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:30:52,386 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:30:52,386 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:30:52,386 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:30:52 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:30:52,389 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:30:52,389 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:30:52,389 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:30:52 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:30:52,426 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:30:52,426 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:30:52,426 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:30:55 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:30:55,768 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:30:55,768 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:30:55,768 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:40:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:40:39,454 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:40:39,454 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:40:39,454 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:40:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:40:39,456 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:40:39,456 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:40:39,457 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:40:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:40:39,496 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:40:39,496 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:40:39,496 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:40:40 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:40:40,896 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:40:40,896 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:40:40,896 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:58:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:58:39,232 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 21:58:39,232 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:58:39,233 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:58:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:58:39,235 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 21:58:39,235 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:58:39,235 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:58:39 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 21:58:39,281 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 21:58:39,281 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:58:39,281 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 21:58:40 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 21:58:40,761 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 21:58:40,761 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 21:58:40,761 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:23:27 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:23:27,386 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 22:23:27,386 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:23:27,386 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:23:27 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:23:27,389 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 22:23:27,389 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:23:27,389 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:23:27 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 22:23:27,427 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 22:23:27,427 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:23:27,427 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:23:36 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:23:36,392 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 22:23:36,392 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:23:36,392 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:39:31 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:39:31,536 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 22:39:31,536 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:39:31,536 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:39:31 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:39:31,539 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 22:39:31,539 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:39:31,539 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:39:31 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 22:39:31,580 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 22:39:31,580 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:39:31,580 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:39:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:39:38,767 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 22:39:38,767 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:39:38,767 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:55:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:55:49,355 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 22:55:49,356 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:55:49,356 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:55:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:55:49,358 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 22:55:49,358 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:55:49,358 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:55:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 22:55:49,400 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 22:55:49,400 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:55:49,400 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 22:55:50 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 22:55:50,928 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 22:55:50,928 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 22:55:50,928 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:13:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:13:24,877 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 23:13:24,877 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:13:24,877 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:13:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:13:24,879 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 23:13:24,879 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:13:24,879 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:13:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 23:13:24,920 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 23:13:24,920 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:13:24,920 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:13:35 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:13:35,334 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 23:13:35,334 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:13:35,334 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:23:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:23:49,386 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 23:23:49,386 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:23:49,386 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:23:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:23:49,388 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 23:23:49,388 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:23:49,388 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:23:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 23:23:49,425 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 23:23:49,426 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:23:49,426 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:23:52 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:23:52,238 | INFO     | __main__:add_log_handler:549 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 23:23:52,238 | DEBUG    | __main__:add_log_handler:550 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:23:52,238 | DEBUG    | __main__:add_log_handler:551 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:42:40 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:42:40,808 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 23:42:40,808 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:42:40,808 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:42:40 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:42:40,811 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 23:42:40,811 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:42:40,811 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:42:40 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 23:42:40,848 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 23:42:40,848 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:42:40,848 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:42:43 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:42:43,625 | INFO     | __main__:add_log_handler:551 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 23:42:43,625 | DEBUG    | __main__:add_log_handler:552 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:42:43,626 | DEBUG    | __main__:add_log_handler:553 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:59:09 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:59:09,818 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-30 23:59:09,818 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:59:09,818 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:59:09 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:59:09,820 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-30 23:59:09,820 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:59:09,820 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:59:09 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-30 23:59:09,860 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-30 23:59:09,860 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:59:09,860 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-30 23:59:11 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-30 23:59:11,425 | INFO     | __main__:add_log_handler:553 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-30 23:59:11,425 | DEBUG    | __main__:add_log_handler:554 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-30 23:59:11,425 | DEBUG    | __main__:add_log_handler:555 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:02:41 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:02:41,706 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-31 00:02:41,706 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:02:41,706 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:02:41 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:02:41,709 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-31 00:02:41,709 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:02:41,709 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:02:41 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-31 00:02:41,750 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-31 00:02:41,750 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:02:41,750 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:02:48 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:02:48,716 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-31 00:02:48,716 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:02:48,716 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:03:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:03:08,276 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-31 00:03:08,276 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:03:08,277 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:03:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:03:08,279 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-31 00:03:08,279 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:03:08,279 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:03:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-31 00:03:08,318 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-31 00:03:08,319 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:03:08,319 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 00:03:11 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 00:03:11,088 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-31 00:03:11,088 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 00:03:11,088 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 01:09:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 01:09:49,215 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-03-31 01:09:49,215 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:09:49,215 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 01:09:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 01:09:49,218 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-03-31 01:09:49,218 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:09:49,218 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 01:09:49 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-03-31 01:09:49,260 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-03-31 01:09:49,260 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:09:49,260 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-03-31 01:09:59 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-03-31 01:09:59,849 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-03-31 01:09:59,849 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-03-31 01:09:59,849 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:07:55 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:07:55,064 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-04-04 17:07:55,064 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:07:55,064 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:07:55 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:07:55,066 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-04-04 17:07:55,066 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:07:55,066 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:07:55 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-04-04 17:07:55,104 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-04-04 17:07:55,104 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:07:55,104 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:08:06 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:08:06,336 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-04-04 17:08:06,337 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:08:06,337 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:12:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:12:08,756 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-04-04 17:12:08,756 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:12:08,756 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:12:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:12:08,759 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-04-04 17:12:08,759 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:12:08,759 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:12:08 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-04-04 17:12:08,798 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-04-04 17:12:08,798 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:12:08,798 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 17:12:10 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 17:12:10,813 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-04-04 17:12:10,813 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 17:12:10,813 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 21:08:11 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 21:08:11,331 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-04-04 21:08:11,331 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 21:08:11,331 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 21:08:11 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 21:08:11,334 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-04-04 21:08:11,334 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 21:08:11,334 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 21:08:11 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-04-04 21:08:11,372 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-04-04 21:08:11,372 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 21:08:11,372 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-04 21:08:14 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-04 21:08:14,676 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-04-04 21:08:14,676 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-04 21:08:14,676 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:24:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:24:38,914 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-04-09 03:24:38,914 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:24:38,914 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:24:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:24:38,917 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-04-09 03:24:38,917 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:24:38,917 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:24:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-04-09 03:24:38,954 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-04-09 03:24:38,954 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:24:38,954 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:24:38 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:24:38,967 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-04-09 03:24:38,967 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:24:38,967 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:25:20 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:25:20,074 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-04-09 03:25:20,074 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:25:20,074 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:25:20 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:25:20,076 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-04-09 03:25:20,076 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:25:20,076 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:25:20 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-04-09 03:25:20,111 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-04-09 03:25:20,112 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:25:20,112 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-04-09 03:25:21 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.2
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-04-09 03:25:21,929 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-04-09 03:25:21,929 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-04-09 03:25:21,929 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-05-31 09:48:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-05-31 09:48:24,381 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-05-31 09:48:24,381 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-05-31 09:48:24,381 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-05-31 09:48:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-05-31 09:48:24,384 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-05-31 09:48:24,384 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-05-31 09:48:24,384 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-05-31 09:48:24 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-05-31 09:48:24,443 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-05-31 09:48:24,444 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-05-31 09:48:24,444 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-05-31 09:48:50 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.3
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-05-31 09:48:50,495 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-05-31 09:48:50,495 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-05-31 09:48:50,495 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:29:15 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:29:15,733 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-07-25 00:29:15,734 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:29:15,734 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:29:15 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:29:15,737 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-07-25 00:29:15,737 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:29:15,737 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:29:15 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-07-25 00:29:15,791 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-07-25 00:29:15,792 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:29:15,792 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:30:26 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:30:26,068 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-07-25 00:30:26,068 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:30:26,068 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:37:37 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:37:37,541 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-07-25 00:37:37,541 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:37:37,541 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:37:37 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:37:37,544 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-07-25 00:37:37,544 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:37:37,544 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:37:37 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-07-25 00:37:37,609 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-07-25 00:37:37,610 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:37:37,610 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 00:37:44 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 00:37:44,635 | INFO     | __main__:add_log_handler:552 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-07-25 00:37:44,635 | DEBUG    | __main__:add_log_handler:553 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 00:37:44,635 | DEBUG    | __main__:add_log_handler:554 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 02:03:07 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 02:03:07,518 | INFO     | __main__:add_log_handler:560 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 2)
2025-07-25 02:03:07,518 | DEBUG    | __main__:add_log_handler:561 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 02:03:07,519 | DEBUG    | __main__:add_log_handler:562 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 02:03:07 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 02:03:07,522 | INFO     | __main__:add_log_handler:560 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 5)
2025-07-25 02:03:07,522 | DEBUG    | __main__:add_log_handler:561 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 02:03:07,522 | DEBUG    | __main__:add_log_handler:562 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 02:03:07 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: INFO
================================================================================
2025-07-25 02:03:07,582 | INFO     | __main__:add_log_handler:560 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 8)
2025-07-25 02:03:07,583 | DEBUG    | __main__:add_log_handler:561 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 02:03:07,583 | DEBUG    | __main__:add_log_handler:562 - Full logging path: logs\removal3.0bannedlog

================================================================================
SESSION START: 2025-07-25 02:03:15 - removalBanned3.0.py
Command Arguments: --skip-dependency-check
System Information:
  - Python Version: 3.13.5
  - OS: Windows 11 (64bit)
  - Machine: AMD64
  - Processor: AMD64 Family 25 Model 97 Stepping 2, AuthenticAMD
  - User: d0nbx
  - Hostname: L3
Configuration:
  - Headless Mode: False
  - Test Mode: False
  - Max Orders Per Batch: 12
  - Cycle Size: 1
  - Log Level: OFF
================================================================================
2025-07-25 02:03:15,213 | INFO     | __main__:add_log_handler:560 - Log file initialized at: logs\removal3.0bannedlog (Sink ID: 11)
2025-07-25 02:03:15,213 | DEBUG    | __main__:add_log_handler:561 - Log rotation settings: 1MB max size, 5 backup files, zip compression
2025-07-25 02:03:15,213 | DEBUG    | __main__:add_log_handler:562 - Full logging path: logs\removal3.0bannedlog
