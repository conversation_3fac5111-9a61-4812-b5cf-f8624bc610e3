INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-14 03:51:05,920 - WARNING - [__main__:<module>:117] - Could not import ProxySellerAPIClient from installed package. Trying local SDK path...
2025-05-14 03:51:05,922 - INFO - [__main__:<module>:138] - Successfully imported ProxySellerAPIClient from local SDK path: C:\main\proxyseller\user-api-python
2025-05-14 03:51:05,923 - INFO - [__main__:load_config_and_args:2027] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 03:51:05,924 - INFO - [__main__:load_config_and_args:2123] - Effective settings: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True
2025-05-14 03:51:05,925 - INFO - [__main__:<module>:2338] - Console logging level set to: INFO
2025-05-14 03:51:05,926 - INFO - [__main__:<module>:2339] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 03:51:05,926 - INFO - [__main__:<module>:2341] - ========================================
 Starting ProxySeller Firefox Setup (Resident SDK Mode)
========================================
2025-05-14 03:51:05,926 - INFO - [__main__:__init__:373] - Initializing ProxyContainerSetup...
2025-05-14 03:51:05,927 - INFO - [__main__:__init__:388] - Attempting to determine Firefox profile path...
2025-05-14 03:51:05,928 - INFO - [__main__:_find_firefox_profile:504] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 03:51:05,929 - INFO - [__main__:__init__:413] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 03:51:06,103 - INFO - [__main__:run:1173] - Detected public IP for this script: **************
2025-05-14 03:51:06,103 - INFO - [__main__:run:1179] - Starting proxy container setup process (using SDK for resident proxies)...
2025-05-14 03:51:06,104 - INFO - [__main__:init_api_client:571] - Initializing ProxySeller API SDK client...
2025-05-14 03:51:06,962 - INFO - [__main__:init_api_client:591] - Successfully pinged Proxy-Seller API via SDK (timestamp: 1747219868).
2025-05-14 03:51:06,962 - INFO - [__main__:get_package_info_sdk:196] - Fetching resident proxy package information via SDK...
2025-05-14 03:51:07,154 - INFO - [__main__:get_package_info_sdk:201] - SDK Package Info: Active: True, Traffic Limit: 3221225472, Usage: 157459442, Left: 0
2025-05-14 03:51:07,155 - INFO - [__main__:init_api_client:605] - Successfully initialized resident proxy manager with SDK and fetched package info.
2025-05-14 03:51:07,156 - INFO - [__main__:fetch_proxies:627] - Fetching resident proxies for 5 containers using SDK...
2025-05-14 03:51:07,156 - INFO - [__main__:get_existing_lists_sdk:282] - Fetching existing resident proxy lists via SDK...
2025-05-14 03:51:09,227 - INFO - [__main__:get_existing_lists_sdk:300] - SDK: Found 8 existing resident proxy lists (direct list).
2025-05-14 03:51:09,228 - ERROR - [__main__:fetch_proxies:660] - No US-targeted resident proxy lists found among your existing lists. Please create them in your Proxy-Seller dashboard with desired US geo-targeting (state/city/ISP).
2025-05-14 03:51:09,228 - ERROR - [__main__:run:1192] - Failed to fetch or configure required resident proxies using SDK. Cannot proceed.
2025-05-14 03:51:09,229 - ERROR - [__main__:<module>:2358] - Main setup process reported failure or incomplete execution for core operations.
2025-05-14 03:51:09,229 - ERROR - [__main__:<module>:2426] - ==================================================
SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
Review logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
Any opened browser windows might be in an inconsistent state.
==================================================
2025-05-14 03:51:09,230 - INFO - [__main__:<module>:2430] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
