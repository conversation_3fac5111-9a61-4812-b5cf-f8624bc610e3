2025-05-13 21:28:41 [INFO] --- <PERSON>ript Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-13 21:28:41 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-13 21:28:41 [DEBUG] Checking for Python installation...
2025-05-13 21:28:41 [INFO] Python version 3.12 found at: D:\Users\d0nbx\anaconda3\python.exe
2025-05-13 21:28:41 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-13 21:28:41 [DEBUG] Checking for package: selenium
2025-05-13 21:28:42 [DEBUG] Package 'selenium' found.
2025-05-13 21:28:42 [DEBUG] Checking for package: webdriver_manager
2025-05-13 21:28:43 [DEBUG] Package 'webdriver_manager' found.
2025-05-13 21:28:43 [DEBUG] Checking for package: requests
2025-05-13 21:28:44 [DEBUG] Package 'requests' found.
2025-05-13 21:28:44 [DEBUG] Checking for package: configparser
2025-05-13 21:28:45 [DEBUG] Package 'configparser' found.
2025-05-13 21:28:45 [INFO] All required Python packages seem to be installed.
2025-05-13 21:28:45 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-13 21:28:45 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-13 21:28:45 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-13 21:28:45 [INFO] Launching main Python script...
2025-05-13 21:28:45 [DEBUG] Full command: D:\Users\d0nbx\anaconda3\python.exe "C:\main\proxyseller\main.py"
2025-05-13 21:28:48 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-13_21-28-41.log
2025-05-13 21:28:48 [ERROR] Python script exited with code 1.
2025-05-13 21:28:48 [INFO] Launcher script execution finished. Final Exit Code: 1
2025-05-13 21:28:48 [DEBUG] No key press detected for log viewing options (possibly non-interactive).
