INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:51:46,299 - INFO - [__main__:load_config_and_args:2467] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 22:51:46,299 - INFO - [__main__:load_config_and_args:2469] - Config sections: []
2025-05-13 22:51:46,299 - INFO - [__main__:load_config_and_args:2470] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 22:51:46,301 - INFO - [__main__:load_config_and_args:2604] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 22:51:46,302 - INFO - [__main__:<module>:2648] - Console logging level set to: INFO
2025-05-13 22:51:46,302 - INFO - [__main__:<module>:2649] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 22:51:46,303 - INFO - [__main__:<module>:2650] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 22:51:46,303 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 22:51:46,303 - INFO - [__main__:__init__:201] - Attempting to determine Firefox profile path...
2025-05-13 22:51:46,304 - INFO - [__main__:_find_firefox_profile:294] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 22:51:46,305 - INFO - [__main__:_find_firefox_profile:309] - Found default profile section in profiles.ini: Profile1
2025-05-13 22:51:46,305 - INFO - [__main__:_find_firefox_profile:357] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:51:46,305 - INFO - [__main__:__init__:230] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 22:51:46,465 - INFO - [__main__:run:1460] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-13 22:51:46,466 - INFO - [__main__:run:1466] - Starting proxy container setup process...
2025-05-13 22:51:47,318 - INFO - [__main__:init_api_client:431] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 22:51:47,534 - WARNING - [__main__:init_api_client:443] - Could not parse balance from response: 11
2025-05-13 22:51:47,534 - INFO - [__main__:fetch_proxies:467] - Fetching proxies from Proxy-Seller...
2025-05-13 22:51:47,534 - INFO - [__main__:fetch_proxies:481] - Calling proxyList(type='ipv4') API method...
2025-05-13 22:51:47,733 - WARNING - [__main__:fetch_proxies:504] - proxyList response does not contain 'status' or 'data' section: {'items': []}
2025-05-13 22:51:47,734 - ERROR - [__main__:fetch_proxies:745] - ProxySeller API Error during proxy fetch/creation: proxyList failed: Invalid response format
2025-05-13 22:51:47,736 - ERROR - [__main__:run:1478] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 22:51:47,736 - ERROR - [__main__:<module>:2664] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 22:51:47,737 - ERROR - [__main__:<module>:2744] - ==================================================
2025-05-13 22:51:47,737 - ERROR - [__main__:<module>:2745] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 22:51:47,737 - ERROR - [__main__:<module>:2746] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 22:51:47,738 - ERROR - [__main__:<module>:2749] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 22:51:47,738 - ERROR - [__main__:<module>:2752] - ==================================================
2025-05-13 22:51:47,738 - INFO - [__main__:<module>:2758] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
