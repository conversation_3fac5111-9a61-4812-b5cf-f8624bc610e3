INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-14 01:48:34,911 - INFO - [__main__:load_config_and_args:2471] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 01:48:34,916 - INFO - [__main__:load_config_and_args:2473] - Config DEFAULT section content: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-14 01:48:34,918 - INFO - [__main__:load_config_and_args:2586] - Effective settings: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-14 01:48:34,918 - INFO - [__main__:<module>:2779] - Console logging level set to: INFO
2025-05-14 01:48:34,919 - INFO - [__main__:<module>:2780] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 01:48:34,919 - INFO - [__main__:<module>:2783] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-14 01:48:34,920 - INFO - [__main__:__init__:188] - Initializing ProxyContainerSetup...
2025-05-14 01:48:34,920 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-14 01:48:34,922 - INFO - [__main__:_find_firefox_profile:323] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 01:48:34,922 - INFO - [__main__:__init__:230] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 01:48:35,111 - INFO - [__main__:run:1528] - Detected public IP for this script: **************
[DIAGNOSTIC] This script's public IP: **************
2025-05-14 01:48:35,111 - INFO - [__main__:run:1535] - Starting proxy container setup process...
2025-05-14 01:48:35,686 - INFO - [__main__:init_api_client:411] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-14 01:48:35,895 - INFO - [__main__:init_api_client:437] - Account balance: $2
2025-05-14 01:48:35,896 - INFO - [__main__:fetch_proxies:490] - Fetching proxies from Proxy-Seller...
2025-05-14 01:48:35,897 - INFO - [__main__:fetch_proxies:502] - Calling proxyList(type='ipv4') API method...
2025-05-14 01:48:36,114 - INFO - [__main__:fetch_proxies:545] - proxyList: Parsed proxies from top-level response['items'] list.
2025-05-14 01:48:36,115 - INFO - [__main__:fetch_proxies:590] - Found 0 existing, active, matching USA HTTP proxies via API call and local filtering.
2025-05-14 01:48:36,116 - INFO - [__main__:fetch_proxies:601] - Attempting to create 5 new USA HTTP proxies.
2025-05-14 01:48:36,116 - INFO - [__main__:create_usa_proxies:800] - Fetching reference list (type 'ipv4') to order proxies...
2025-05-14 01:48:36,330 - INFO - [__main__:create_usa_proxies:836] - referenceList: Parsed 'country' and/or 'period' from top-level response['items'] dict.
2025-05-14 01:48:36,332 - INFO - [__main__:create_usa_proxies:937] - Found USA country ID: 565 (Name: Proxy of US, Alpha3: USA)
2025-05-14 01:48:36,332 - INFO - [__main__:create_usa_proxies:961] - Using period ID: 1m (Name: 1 month) for 1 Month.
2025-05-14 01:48:36,333 - INFO - [__main__:create_usa_proxies:998] - Calling orderMakeIpv4 API with params: {'countryId': 565, 'periodId': '1m', 'quantity': 5, 'authorization': '', 'coupon': '', 'customTargetName': 'FirefoxContainersAuto'}
2025-05-14 01:48:37,095 - ERROR - [__main__:create_usa_proxies:1153] - Unexpected error during proxy order process: Insufficient funds. Total 2.0000. Not enough $7
2025-05-14 01:48:37,098 - ERROR - [__main__:fetch_proxies:779] - ProxySeller API Error during proxy fetch/creation: Order process failed unexpectedly: Insufficient funds. Total 2.0000. Not enough $7
2025-05-14 01:48:37,100 - ERROR - [__main__:run:1547] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-14 01:48:37,101 - ERROR - [__main__:<module>:2801] - Main setup process reported failure or incomplete execution for core operations.
2025-05-14 01:48:37,101 - ERROR - [__main__:<module>:2871] - ==================================================
SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
Review logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
Any opened browser windows might be in an inconsistent state.
==================================================
2025-05-14 01:48:37,102 - INFO - [__main__:<module>:2875] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
