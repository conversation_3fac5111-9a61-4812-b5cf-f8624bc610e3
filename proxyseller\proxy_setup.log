2025-05-14 04:05:08,126 - WARNING - [__main__:<module>:117] - Could not import ProxySellerAPIClient from installed package. Trying local SDK path...
2025-05-14 04:05:08,127 - DEBUG - [__main__:<module>:131] - Adding local SDK path to sys.path: C:\main\proxyseller\user-api-python
2025-05-14 04:05:08,128 - INFO - [__main__:<module>:138] - Successfully imported ProxySellerAPIClient from local SDK path: c:\main\proxyseller\user-api-python
2025-05-14 04:05:08,128 - INFO - [__main__:load_config_and_args:2014] - Loaded settings from config.ini at c:\main\proxyseller\config.ini
2025-05-14 04:05:08,129 - INFO - [__main__:load_config_and_args:2110] - Effective settings: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True
2025-05-14 04:05:08,129 - INFO - [__main__:<module>:2327] - Console logging level set to: INFO
2025-05-14 04:05:08,129 - INFO - [__main__:<module>:2328] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 04:05:08,129 - INFO - [__main__:<module>:2330] - ========================================
 Starting ProxySeller Firefox Setup (Resident SDK Mode)
========================================
2025-05-14 04:05:08,129 - INFO - [__main__:__init__:373] - Initializing ProxyContainerSetup...
2025-05-14 04:05:08,129 - INFO - [__main__:__init__:388] - Attempting to determine Firefox profile path...
2025-05-14 04:05:08,129 - DEBUG - [__main__:_find_firefox_profile:442] - Starting automatic Firefox profile detection.
2025-05-14 04:05:08,130 - INFO - [__main__:_find_firefox_profile:504] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 04:05:08,130 - INFO - [__main__:__init__:413] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 04:05:08,300 - INFO - [__main__:run:1160] - Detected public IP for this script: **************
2025-05-14 04:05:08,300 - INFO - [__main__:run:1166] - Starting proxy container setup process (using SDK for resident proxies)...
2025-05-14 04:05:08,300 - INFO - [__main__:init_api_client:571] - Initializing ProxySeller API SDK client...
2025-05-14 04:05:09,138 - DEBUG - [__main__:init_api_client:582] - SDK API ping response: 1747220710
2025-05-14 04:05:09,139 - INFO - [__main__:init_api_client:591] - Successfully pinged Proxy-Seller API via SDK (timestamp: 1747220710).
2025-05-14 04:05:09,139 - INFO - [__main__:get_package_info_sdk:196] - Fetching resident proxy package information via SDK...
2025-05-14 04:05:09,326 - INFO - [__main__:get_package_info_sdk:201] - SDK Package Info: Active: True, Traffic Limit: 3221225472, Usage: 157459442, Left: 0
2025-05-14 04:05:09,326 - INFO - [__main__:init_api_client:605] - Successfully initialized resident proxy manager with SDK and fetched package info.
2025-05-14 04:05:09,326 - INFO - [__main__:fetch_proxies:627] - Fetching resident proxies for 5 containers using SDK...
2025-05-14 04:05:09,326 - INFO - [__main__:get_existing_lists_sdk:282] - Fetching existing resident proxy lists via SDK...
2025-05-14 04:05:13,741 - INFO - [__main__:get_existing_lists_sdk:300] - SDK: Found 8 existing resident proxy lists (direct list).
2025-05-14 04:05:13,742 - DEBUG - [__main__:fetch_proxies:649] - All existing resident lists from SDK: [
  {
    "id": 11783497,
    "title": "US Alabama Abbeville Brightspeed Fiber",
    "login": "2fa3b3e6406b7058",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": -1,
    "geo": [
      {
        "country": "US",
        "region": "Alabama",
        "city": "Abbeville",
        "isp": "Brightspeed Fiber"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  },
  {
    "id": 11784060,
    "title": "US",
    "login": "a3c2f4780330961e",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": -1,
    "geo": [
      {
        "country": "US",
        "region": "",
        "city": "",
        "isp": ""
      }
    ],
    "export": {
      "ports": "1",
      "ext": "txt"
    }
  },
  {
    "id": 11784161,
    "title": "US Arizona",
    "login": "084391593736b8c3",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": -1,
    "geo": [
      {
        "country": "US",
        "region": "Arizona",
        "city": "",
        "isp": ""
      }
    ],
    "export": {
      "ports": "1",
      "ext": "txt"
    }
  },
  {
    "id": 11786948,
    "title": "US Colorado Aurora Comcast Cable",
    "login": "597ce835d0165be8",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": -1,
    "geo": [
      {
        "country": "US",
        "region": "Colorado",
        "city": "Aurora",
        "isp": "Comcast Cable"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  },
  {
    "id": 11786965,
    "title": "US Delaware Camden Comcast Cable",
    "login": "05d65a71c1def008",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": -1,
    "geo": [
      {
        "country": "US",
        "region": "Delaware",
        "city": "Camden",
        "isp": "Comcast Cable"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  },
  {
    "id": 11787009,
    "title": "US Idaho Boise CenturyLink",
    "login": "5a6fd34a2f3fc8a6",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": 3600,
    "geo": [
      {
        "country": "US",
        "region": "Idaho",
        "city": "Boise",
        "isp": "CenturyLink"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  },
  {
    "id": 11787038,
    "title": "US Maine Calais Pioneer Broadband",
    "login": "39e07e0ba58ba3e2",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": 3600,
    "geo": [
      {
        "country": "US",
        "region": "Maine",
        "city": "Calais",
        "isp": "Pioneer Broadband"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  },
  {
    "id": 11787282,
    "title": "US Iowa Aurora Aureon",
    "login": "af2960c293c04593",
    "password": "***MASKED_PASS***",
    "whitelist": "",
    "rotation": 3600,
    "geo": [
      {
        "country": "US",
        "region": "Iowa",
        "city": "Aurora",
        "isp": "Aureon"
      }
    ],
    "export": {
      "ports": "1",
      "ext": ""
    }
  }
]
2025-05-14 04:05:13,742 - DEBUG - [__main__:fetch_proxies:658] - List #1 - Title: 'US Alabama Abbeville Brightspeed Fiber', Raw Geo data: [{"country": "US", "region": "Alabama", "city": "Abbeville", "isp": "Brightspeed Fiber"}]
2025-05-14 04:05:13,742 - WARNING - [__main__:fetch_proxies:669] - List 'US Alabama Abbeville Brightspeed Fiber' (ID: 11783497) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Alabama", "city": "Abbeville", "isp": "Brightspeed Fiber"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,742 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Alabama Abbeville Brightspeed Fiber' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,742 - DEBUG - [__main__:fetch_proxies:658] - List #2 - Title: 'US', Raw Geo data: [{"country": "US", "region": "", "city": "", "isp": ""}]
2025-05-14 04:05:13,742 - WARNING - [__main__:fetch_proxies:669] - List 'US' (ID: 11784060) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,742 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,743 - DEBUG - [__main__:fetch_proxies:658] - List #3 - Title: 'US Arizona', Raw Geo data: [{"country": "US", "region": "Arizona", "city": "", "isp": ""}]
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:669] - List 'US Arizona' (ID: 11784161) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Arizona", "city": "", "isp": ""}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Arizona' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,743 - DEBUG - [__main__:fetch_proxies:658] - List #4 - Title: 'US Colorado Aurora Comcast Cable', Raw Geo data: [{"country": "US", "region": "Colorado", "city": "Aurora", "isp": "Comcast Cable"}]
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:669] - List 'US Colorado Aurora Comcast Cable' (ID: 11786948) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Colorado", "city": "Aurora", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Colorado Aurora Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,743 - DEBUG - [__main__:fetch_proxies:658] - List #5 - Title: 'US Delaware Camden Comcast Cable', Raw Geo data: [{"country": "US", "region": "Delaware", "city": "Camden", "isp": "Comcast Cable"}]
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:669] - List 'US Delaware Camden Comcast Cable' (ID: 11786965) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Delaware", "city": "Camden", "isp": "Comcast Cable"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Delaware Camden Comcast Cable' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,743 - DEBUG - [__main__:fetch_proxies:658] - List #6 - Title: 'US Idaho Boise CenturyLink', Raw Geo data: [{"country": "US", "region": "Idaho", "city": "Boise", "isp": "CenturyLink"}]
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:669] - List 'US Idaho Boise CenturyLink' (ID: 11787009) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Idaho", "city": "Boise", "isp": "CenturyLink"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,743 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Idaho Boise CenturyLink' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,744 - DEBUG - [__main__:fetch_proxies:658] - List #7 - Title: 'US Maine Calais Pioneer Broadband', Raw Geo data: [{"country": "US", "region": "Maine", "city": "Calais", "isp": "Pioneer Broadband"}]
2025-05-14 04:05:13,744 - WARNING - [__main__:fetch_proxies:669] - List 'US Maine Calais Pioneer Broadband' (ID: 11787038) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Maine", "city": "Calais", "isp": "Pioneer Broadband"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,744 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Maine Calais Pioneer Broadband' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,744 - DEBUG - [__main__:fetch_proxies:658] - List #8 - Title: 'US Iowa Aurora Aureon', Raw Geo data: [{"country": "US", "region": "Iowa", "city": "Aurora", "isp": "Aureon"}]
2025-05-14 04:05:13,744 - WARNING - [__main__:fetch_proxies:669] - List 'US Iowa Aurora Aureon' (ID: 11787282) has 'geo' data of unexpected type: list. Content: [{"country": "US", "region": "Iowa", "city": "Aurora", "isp": "Aureon"}]. Expected a dictionary. Cannot reliably determine US targeting for this list from this field. Please check its configuration in the Proxy-Seller dashboard.
2025-05-14 04:05:13,744 - WARNING - [__main__:fetch_proxies:676] - Fallback: List 'US Iowa Aurora Aureon' title contains 'US'/'USA'. Assuming it's a US list despite unusual geo field.
2025-05-14 04:05:13,744 - INFO - [__main__:fetch_proxies:700] - Found 8 US-targeted resident lists after filtering. Will attempt to use up to 5 of them.
2025-05-14 04:05:13,744 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 1/5 from list: 'US Idaho Boise CenturyLink' (ID: 11787009)
2025-05-14 04:05:13,744 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 11787009, protocol: http via SDK...
2025-05-14 04:05:14,488 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 11787009: 5a6fd34a2f3fc8a6:RNW...
2025-05-14 04:05:14,488 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 1: res.proxy-seller.com:10000 from list 'US Idaho Boise CenturyLink' (Geo: Unknown, Unknown (from title))
2025-05-14 04:05:14,989 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 2/5 from list: 'US Alabama Abbeville Brightspeed Fiber' (ID: 11783497)
2025-05-14 04:05:14,989 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 11783497, protocol: http via SDK...
2025-05-14 04:05:15,826 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 11783497: 2fa3b3e6406b7058:RNW...
2025-05-14 04:05:15,826 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 2: res.proxy-seller.com:10000 from list 'US Alabama Abbeville Brightspeed Fiber' (Geo: Unknown, Unknown (from title))
2025-05-14 04:05:16,327 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 3/5 from list: 'US Maine Calais Pioneer Broadband' (ID: 11787038)
2025-05-14 04:05:16,327 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 11787038, protocol: http via SDK...
2025-05-14 04:05:17,076 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 11787038: 39e07e0ba58ba3e2:RNW...
2025-05-14 04:05:17,076 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 3: res.proxy-seller.com:10000 from list 'US Maine Calais Pioneer Broadband' (Geo: Unknown, Unknown (from title))
2025-05-14 04:05:17,576 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 4/5 from list: 'US Arizona' (ID: 11784161)
2025-05-14 04:05:17,576 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 11784161, protocol: http via SDK...
2025-05-14 04:05:23,323 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 11784161: 084391593736b8c3:RNW...
2025-05-14 04:05:23,323 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 4: res.proxy-seller.com:10000 from list 'US Arizona' (Geo: Unknown, Unknown (from title))
2025-05-14 04:05:23,824 - INFO - [__main__:fetch_proxies:719] - Processing resident proxy for container 5/5 from list: 'US' (ID: 11784060)
2025-05-14 04:05:23,824 - INFO - [__main__:download_proxy_for_list_sdk:321] - Downloading proxy string for list ID: 11784060, protocol: http via SDK...
2025-05-14 04:05:28,570 - INFO - [__main__:download_proxy_for_list_sdk:342] - SDK: Downloaded proxy string for list 11784060: a3c2f4780330961e:RNW...
2025-05-14 04:05:28,570 - INFO - [__main__:fetch_proxies:755] - SDK: Configured resident proxy for container 5: res.proxy-seller.com:10000 from list 'US' (Geo: Unknown, Unknown (from title))
2025-05-14 04:05:29,071 - INFO - [__main__:fetch_proxies:774] - SDK: Successfully configured 5 resident proxies.
2025-05-14 04:05:29,071 - INFO - [__main__:create_or_update_containers:833] - Creating or updating Firefox container definitions...
2025-05-14 04:05:29,071 - DEBUG - [__main__:get_existing_containers:788] - Looking for containers.json at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default\containers.json
2025-05-14 04:05:29,073 - INFO - [__main__:get_existing_containers:811] - Found 10 existing containers in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default\containers.json
2025-05-14 04:05:29,073 - INFO - [__main__:create_or_update_containers:891] - Found 5 existing 'ProxySeller #' containers. Max number suffix found: 5
2025-05-14 04:05:29,073 - INFO - [__main__:create_or_update_containers:953] - Writing updated container data to: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default\containers.json
2025-05-14 04:05:29,073 - INFO - [__main__:create_or_update_containers:972] - Final list for setup: Using 5 container definitions matched with 5 proxies.
2025-05-14 04:05:29,073 - INFO - [__main__:generate_foxyproxy_config:996] - Generating FoxyProxy configuration...
2025-05-14 04:05:29,074 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #01' to use proxy res.proxy-seller.com:10000 (from list 'US Idaho Boise CenturyLink')
2025-05-14 04:05:29,074 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #02' to use proxy res.proxy-seller.com:10000 (from list 'US Alabama Abbeville Brightspeed Fiber')
2025-05-14 04:05:29,074 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #03' to use proxy res.proxy-seller.com:10000 (from list 'US Maine Calais Pioneer Broadband')
2025-05-14 04:05:29,074 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #04' to use proxy res.proxy-seller.com:10000 (from list 'US Arizona')
2025-05-14 04:05:29,074 - INFO - [__main__:generate_foxyproxy_config:1132] - Added FoxyProxy config mapping for Firefox Container 'ProxySeller #05' to use proxy res.proxy-seller.com:10000 (from list 'US')
2025-05-14 04:05:29,075 - INFO - [__main__:generate_foxyproxy_config:1146] - FoxyProxy configuration generated and saved to: C:\main\proxyseller\foxyproxy_config.json
2025-05-14 04:05:29,075 - INFO - [__main__:create_summary_file:1868] - Creating proxy-container summary file at: C:\main\proxyseller\proxy_container_summary.txt
2025-05-14 04:05:29,076 - INFO - [__main__:create_summary_file:1934] - Summary file created successfully.
2025-05-14 04:05:29,076 - INFO - [__main__:run:1214] - Attempting automated import of FoxyProxy configuration via Selenium...
2025-05-14 04:05:29,076 - INFO - [__main__:import_foxyproxy_config:1433] - Starting FoxyProxy import via Selenium...
2025-05-14 04:05:29,076 - INFO - [__main__:import_foxyproxy_config:1443] - FoxyProxy Selenium import attempt 1/2...
2025-05-14 04:05:29,246 - DEBUG - [__main__:import_foxyproxy_config:1452] - Initializing GeckoDriverManager for FoxyProxy import...
2025-05-14 04:05:36,280 - INFO - [__main__:import_foxyproxy_config:1467] - Initializing WebDriver for FoxyProxy options page access...
2025-05-14 04:05:40,917 - INFO - [__main__:import_foxyproxy_config:1476] - Browser for FoxyProxy import started (PID: 138172).
2025-05-14 04:05:40,917 - INFO - [__main__:import_foxyproxy_config:1487] - Attempting to find FoxyProxy Internal UUID via about:debugging...
2025-05-14 04:06:33,109 - ERROR - [__main__:import_foxyproxy_config:1548] - Error finding FoxyProxy UUID on about:debugging: Message: 
Stacktrace:
RemoteError@chrome://remote/content/shared/RemoteError.sys.mjs:8:8
WebDriverError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:199:5
NoSuchElementError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:552:5
dom.find/</<@chrome://remote/content/shared/DOM.sys.mjs:136:16

2025-05-14 04:06:33,109 - ERROR - [__main__:import_foxyproxy_config:1554] - Automated FoxyProxy Internal UUID detection failed. Prompting for manual input.
2025-05-14 04:06:33,109 - ERROR - [__main__:import_foxyproxy_config:1734] - FoxyProxy Setup Error (Attempt 1/2): FoxyProxy UUID not provided due to input error (EOF).
2025-05-14 04:06:34,110 - INFO - [__main__:import_foxyproxy_config:1782] - Closing browser window used for FoxyProxy import...
2025-05-14 04:06:36,012 - INFO - [__main__:import_foxyproxy_config:1797] - Waiting 5 seconds before retrying FoxyProxy import...
2025-05-14 04:06:41,013 - INFO - [__main__:import_foxyproxy_config:1443] - FoxyProxy Selenium import attempt 2/2...
2025-05-14 04:06:41,122 - DEBUG - [__main__:import_foxyproxy_config:1452] - Initializing GeckoDriverManager for FoxyProxy import...
2025-05-14 04:06:45,722 - INFO - [__main__:import_foxyproxy_config:1467] - Initializing WebDriver for FoxyProxy options page access...
2025-05-14 04:06:51,167 - INFO - [__main__:import_foxyproxy_config:1476] - Browser for FoxyProxy import started (PID: 115556).
2025-05-14 04:06:51,167 - INFO - [__main__:import_foxyproxy_config:1487] - Attempting to find FoxyProxy Internal UUID via about:debugging...
2025-05-14 04:07:43,403 - ERROR - [__main__:import_foxyproxy_config:1548] - Error finding FoxyProxy UUID on about:debugging: Message: 
Stacktrace:
RemoteError@chrome://remote/content/shared/RemoteError.sys.mjs:8:8
WebDriverError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:199:5
NoSuchElementError@chrome://remote/content/shared/webdriver/Errors.sys.mjs:552:5
dom.find/</<@chrome://remote/content/shared/DOM.sys.mjs:136:16

2025-05-14 04:07:43,403 - ERROR - [__main__:import_foxyproxy_config:1554] - Automated FoxyProxy Internal UUID detection failed. Prompting for manual input.
2025-05-14 04:07:43,403 - ERROR - [__main__:import_foxyproxy_config:1734] - FoxyProxy Setup Error (Attempt 2/2): FoxyProxy UUID not provided due to input error (EOF).
2025-05-14 04:07:44,404 - INFO - [__main__:import_foxyproxy_config:1782] - Closing browser window used for FoxyProxy import...
2025-05-14 04:07:46,010 - ERROR - [__main__:import_foxyproxy_config:1799] - Automated FoxyProxy import failed after 2 attempt(s).
2025-05-14 04:07:46,010 - WARNING - [__main__:run:1219] - Automated FoxyProxy import failed or timed out. Manual import will be needed: C:\main\proxyseller\foxyproxy_config.json
2025-05-14 04:07:46,010 - INFO - [__main__:run:1243] - Launching a dedicated Firefox window for each container setup...
2025-05-14 04:07:46,010 - INFO - [__main__:launch_container_windows:1261] - ==== LAUNCHING CONTAINER WINDOWS ====
2025-05-14 04:07:46,010 - INFO - [__main__:launch_container_windows:1277] - Attempting to terminate existing Firefox processes to avoid profile lock issues (best effort)...
2025-05-14 04:07:47,980 - INFO - [__main__:launch_container_windows:1297] - Firefox termination command executed. Waiting a few seconds...
2025-05-14 04:07:50,981 - INFO - [__main__:launch_container_windows:1400] - Launching up to 5 browser windows concurrently...
2025-05-14 04:07:50,981 - INFO - [__main__:launch_window_for_container_thread_func:1320] - Preparing to launch window for ProxySeller #01 (Proxy: res.proxy-seller.com:10000 from list 'US Idaho Boise CenturyLink')
2025-05-14 04:07:50,981 - INFO - [__main__:launch_window_for_container_thread_func:1320] - Preparing to launch window for ProxySeller #02 (Proxy: res.proxy-seller.com:10000 from list 'US Alabama Abbeville Brightspeed Fiber')
2025-05-14 04:07:50,982 - INFO - [__main__:launch_window_for_container_thread_func:1320] - Preparing to launch window for ProxySeller #03 (Proxy: res.proxy-seller.com:10000 from list 'US Maine Calais Pioneer Broadband')
2025-05-14 04:07:50,982 - INFO - [__main__:launch_window_for_container_thread_func:1320] - Preparing to launch window for ProxySeller #04 (Proxy: res.proxy-seller.com:10000 from list 'US Arizona')
2025-05-14 04:07:50,982 - INFO - [__main__:launch_window_for_container_thread_func:1320] - Preparing to launch window for ProxySeller #05 (Proxy: res.proxy-seller.com:10000 from list 'US')
2025-05-14 04:08:07,304 - INFO - [__main__:launch_window_for_container_thread_func:1368] - Window for ProxySeller #04 launched with instruction, DoorDash, and IPInfo tabs.
2025-05-14 04:08:07,304 - INFO - [__main__:launch_container_windows:1417] - Successfully launched window for ProxySeller #04
2025-05-14 04:08:08,308 - INFO - [__main__:launch_window_for_container_thread_func:1368] - Window for ProxySeller #01 launched with instruction, DoorDash, and IPInfo tabs.
2025-05-14 04:08:08,308 - INFO - [__main__:launch_container_windows:1417] - Successfully launched window for ProxySeller #01
2025-05-14 04:08:09,217 - INFO - [__main__:launch_window_for_container_thread_func:1368] - Window for ProxySeller #02 launched with instruction, DoorDash, and IPInfo tabs.
2025-05-14 04:08:09,217 - INFO - [__main__:launch_container_windows:1417] - Successfully launched window for ProxySeller #02
2025-05-14 04:08:09,959 - INFO - [__main__:launch_window_for_container_thread_func:1368] - Window for ProxySeller #05 launched with instruction, DoorDash, and IPInfo tabs.
2025-05-14 04:08:09,959 - INFO - [__main__:launch_container_windows:1417] - Successfully launched window for ProxySeller #05
2025-05-14 04:08:10,986 - INFO - [__main__:launch_window_for_container_thread_func:1368] - Window for ProxySeller #03 launched with instruction, DoorDash, and IPInfo tabs.
2025-05-14 04:08:10,986 - INFO - [__main__:launch_container_windows:1417] - Successfully launched window for ProxySeller #03
2025-05-14 04:08:10,987 - INFO - [__main__:launch_container_windows:1425] - All container window launch attempts have been initiated.
2025-05-14 04:08:10,987 - INFO - [__main__:launch_container_windows:1426] - ==== IF USING FIREFOX MULTI-ACCOUNT CONTAINERS EXTENSION: ====
2025-05-14 04:08:10,987 - INFO - [__main__:launch_container_windows:1427] - ==== MANUAL TAB-TO-CONTAINER ASSIGNMENT IS REQUIRED. SEE INSTRUCTION TAB IN EACH WINDOW. ====
2025-05-14 04:08:10,987 - INFO - [__main__:run:1246] - ========================================
2025-05-14 04:08:10,987 - INFO - [__main__:run:1247] - Proxy container setup process finished main steps.
2025-05-14 04:08:10,987 - INFO - [__main__:run:1248] - Summary file available at: C:\main\proxyseller\proxy_container_summary.txt
2025-05-14 04:08:10,987 - INFO - [__main__:run:1252] - FoxyProxy configuration file (for manual import if needed): C:\main\proxyseller\foxyproxy_config.json
2025-05-14 04:08:10,987 - INFO - [__main__:run:1256] - ========================================
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2344] - Main setup process reported success for core operations.
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2383] - ==================================================
PROXY AND CONTAINER SETUP PROCESS COMPLETED SUCCESSFULLY.
Firefox browser windows should remain open.
If using Firefox Multi-Account Containers, assign tabs manually (see instruction tab).
Verify proxy functionality at ipinfo.io in each window.
==================================================
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2385] - IMPORTANT: This script will now pause to keep browser windows alive.
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2388] -            Close browser windows manually when finished.
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2404] - 
EOF detected on input. Script exiting.
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2406] - Selenium-controlled browsers (if any were launched by this script) will be closed upon script termination.
2025-05-14 04:08:10,988 - INFO - [__main__:<module>:2419] - Initiating final script shutdown sequence...
