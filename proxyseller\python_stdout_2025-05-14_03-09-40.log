INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-14 03:09:50,986 - WARNING - [__main__:<module>:117] - Could not import ProxySellerAPIClient from installed package. Trying local SDK path...
2025-05-14 03:09:50,989 - INFO - [__main__:<module>:138] - Successfully imported ProxySellerAPIClient from local SDK path: C:\main\proxyseller\user-api-python
2025-05-14 03:09:50,990 - INFO - [__main__:load_config_and_args:1999] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 03:09:50,991 - INFO - [__main__:load_config_and_args:2095] - Effective settings: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True
2025-05-14 03:09:50,992 - INFO - [__main__:<module>:2310] - Console logging level set to: INFO
2025-05-14 03:09:50,992 - INFO - [__main__:<module>:2311] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 03:09:50,993 - INFO - [__main__:<module>:2313] - ========================================
 Starting ProxySeller Firefox Setup (Resident SDK Mode)
========================================
2025-05-14 03:09:50,993 - INFO - [__main__:__init__:373] - Initializing ProxyContainerSetup...
2025-05-14 03:09:50,993 - INFO - [__main__:__init__:388] - Attempting to determine Firefox profile path...
2025-05-14 03:09:50,995 - INFO - [__main__:_find_firefox_profile:504] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 03:09:50,996 - INFO - [__main__:__init__:413] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 03:09:51,189 - INFO - [__main__:run:1161] - Detected public IP for this script: **************
2025-05-14 03:09:51,190 - INFO - [__main__:run:1167] - Starting proxy container setup process (using SDK for resident proxies)...
2025-05-14 03:09:51,190 - INFO - [__main__:init_api_client:571] - Initializing ProxySeller API SDK client...
2025-05-14 03:09:52,078 - INFO - [__main__:init_api_client:591] - Successfully pinged Proxy-Seller API via SDK (timestamp: 1747217393).
2025-05-14 03:09:52,079 - INFO - [__main__:get_package_info_sdk:196] - Fetching resident proxy package information via SDK...
2025-05-14 03:09:52,276 - INFO - [__main__:get_package_info_sdk:201] - SDK Package Info: Active: True, Traffic Limit: 3221225472, Usage: 157459442, Left: 0
2025-05-14 03:09:52,277 - INFO - [__main__:init_api_client:605] - Successfully initialized resident proxy manager with SDK and fetched package info.
2025-05-14 03:09:52,277 - INFO - [__main__:fetch_proxies:627] - Fetching resident proxies for 5 containers using SDK...
2025-05-14 03:09:52,277 - INFO - [__main__:get_existing_lists_sdk:282] - Fetching existing resident proxy lists via SDK...
2025-05-14 03:09:54,759 - INFO - [__main__:get_existing_lists_sdk:300] - SDK: Found 8 existing resident proxy lists (direct list).
2025-05-14 03:09:54,760 - ERROR - [__main__:fetch_proxies:655] - No US-targeted resident proxy lists found among your existing lists. Please create them in your Proxy-Seller dashboard with desired US geo-targeting.
2025-05-14 03:09:54,761 - ERROR - [__main__:run:1180] - Failed to fetch or configure required resident proxies using SDK. Cannot proceed.
2025-05-14 03:09:54,762 - ERROR - [__main__:<module>:2330] - Main setup process reported failure or incomplete execution for core operations.
2025-05-14 03:09:54,762 - ERROR - [__main__:<module>:2398] - ==================================================
SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
Review logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
Any opened browser windows might be in an inconsistent state.
==================================================
2025-05-14 03:09:54,763 - INFO - [__main__:<module>:2402] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
