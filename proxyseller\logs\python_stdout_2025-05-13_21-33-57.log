INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:34:03,169 - INFO - [__main__:load_config_and_args:2400] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 21:34:03,170 - INFO - [__main__:load_config_and_args:2402] - Config sections: []
2025-05-13 21:34:03,171 - INFO - [__main__:load_config_and_args:2403] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 21:34:03,172 - INFO - [__main__:load_config_and_args:2534] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 21:34:03,173 - INFO - [__main__:<module>:2578] - Console logging level set to: INFO
2025-05-13 21:34:03,174 - INFO - [__main__:<module>:2579] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:34:03,174 - INFO - [__main__:<module>:2580] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 21:34:03,174 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 21:34:03,175 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 21:34:03,176 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 21:34:03,177 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 21:34:03,178 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:34:03,178 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:34:03,179 - INFO - [__main__:run:1399] - Starting proxy container setup process...
2025-05-13 21:34:03,780 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 21:34:04,004 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 21:34:04,005 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 21:34:04,005 - INFO - [__main__:fetch_proxies:480] - Calling proxyList(type='ipv4') API method...
2025-05-13 21:34:04,223 - INFO - [__main__:fetch_proxies:535] - Found 0 existing, active, matching USA HTTP proxies via direct API filter.
2025-05-13 21:34:04,223 - INFO - [__main__:fetch_proxies:546] - Attempting to create 5 new USA HTTP proxies.
2025-05-13 21:34:04,723 - INFO - [__main__:create_usa_proxies:776] - Available countries from API:
2025-05-13 21:34:04,725 - ERROR - [__main__:create_usa_proxies:1006] - Unexpected error in create_usa_proxies: name 'countries_list' is not defined
2025-05-13 21:34:04,730 - WARNING - [__main__:fetch_proxies:613] - Failed to create or retrieve details for new proxies. This might be due to API balance/permissions or provisioning delays.
2025-05-13 21:34:04,731 - WARNING - [__main__:fetch_proxies:711] - Failed to obtain the required 5 proxies. Proceeding with 0 available proxies.
2025-05-13 21:34:04,731 - ERROR - [__main__:fetch_proxies:720] - No proxies are available after fetching and creation attempts. Cannot proceed.
2025-05-13 21:34:04,732 - ERROR - [__main__:run:1411] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 21:34:04,733 - ERROR - [__main__:<module>:2594] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 21:34:04,733 - ERROR - [__main__:<module>:2674] - ==================================================
2025-05-13 21:34:04,733 - ERROR - [__main__:<module>:2675] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 21:34:04,734 - ERROR - [__main__:<module>:2676] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 21:34:04,735 - ERROR - [__main__:<module>:2679] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 21:34:04,735 - ERROR - [__main__:<module>:2682] - ==================================================
2025-05-13 21:34:04,735 - INFO - [__main__:<module>:2688] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
