2025-05-14 01:48:24 [INFO] --- Script Start: ProxySeller Firefox Container Setup Tool v3.2.1 ---
2025-05-14 01:48:24 [WARNING] Not running as Admin. Pip install might need --user or admin rights.
2025-05-14 01:48:24 [DEBUG] Checking for Python installation...
2025-05-14 01:48:24 [INFO] Python version 3.13 found at: C:\Python313\python.exe
2025-05-14 01:48:24 [DEBUG] Checking/Installing required Python packages: selenium, webdriver_manager, requests, configparser
2025-05-14 01:48:24 [DEBUG] Checking for package: selenium
2025-05-14 01:48:25 [DEBUG] Package 'selenium' found.
2025-05-14 01:48:25 [DEBUG] Checking for package: webdriver_manager
2025-05-14 01:48:26 [DEBUG] Package 'webdriver_manager' found.
2025-05-14 01:48:26 [DEBUG] Checking for package: requests
2025-05-14 01:48:27 [DEBUG] Package 'requests' found.
2025-05-14 01:48:27 [DEBUG] Checking for package: configparser
2025-05-14 01:48:28 [DEBUG] Package 'configparser' found.
2025-05-14 01:48:28 [INFO] All required Python packages seem to be installed.
2025-05-14 01:48:28 [DEBUG] Initializing default configuration parameters for launcher prompts...
2025-05-14 01:48:28 [INFO] config.ini found at C:\main\proxyseller\config.ini. Python script will read it.
2025-05-14 01:48:28 [DEBUG] Prompting user for parameters to pass as CLI overrides to Python script.
2025-05-14 01:48:33 [INFO] Launching main Python script...
2025-05-14 01:48:33 [DEBUG] Full command: C:\Python313\python.exe "C:\main\proxyseller\main.py"
2025-05-14 01:48:37 [DEBUG] Python Script Standard Error file is empty: C:\main\proxyseller\python_stderr_2025-05-14_01-48-23.log
2025-05-14 01:48:37 [ERROR] Python script exited with code 1.
2025-05-14 01:48:37 [INFO] Launcher script execution finished. Final Exit Code: 1
