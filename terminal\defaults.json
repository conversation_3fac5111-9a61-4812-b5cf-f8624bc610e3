// THIS IS AN AUTO-GENERATED FILE! Changes to this file will be ignored.
{
    "defaultProfile": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",

    // Launch Settings
    "initialCols": 120,
    "initialRows": 30,
    "launchMode": "default",
    "alwaysOnTop": false,

    // Selection
    "copyOnSelect": false,
    "copyFormatting": true,
    "trimBlockSelection": true,
    "trimPaste": true,
    "wordDelimiters": " /\\()\"'-.,:;<>~!@#$%^&*|+=[]{}~?\u2502",

    // Tab UI
    "alwaysShowTabs": true,
    "showTabsInTitlebar": true,
    "showTerminalTitleInTitlebar": true,
    "tabWidthMode": "equal",
    "tabSwitcherMode": "inOrder",
    "showAdminShield": true,

    // Miscellaneous
    "confirmCloseAllTabs": true,
    "startOnUserLogin": false,
    "theme": "dark",
    "snapToGridOnResize": true,
    "disableAnimations": false,
    "startupActions": "",
    "focusFollowMouse": false,
    "minimizeToNotificationArea": false,
    "alwaysShowNotificationIcon": false,

    "profiles":
    [
        {
            "guid": "{61c54bbd-c2c6-5271-96e7-009a87ff44bf}",
            "name": "Windows PowerShell",
            "commandline": "%SystemRoot%\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
            "icon": "ms-appx:///ProfileIcons/{61c54bbd-c2c6-5271-96e7-009a87ff44bf}.png",
            "colorScheme": "Campbell",
            "antialiasingMode": "grayscale",
            "closeOnExit": "automatic",
            "cursorShape": "bar",
            "fontFace": "Cascadia Mono",
            "fontSize": 12,
            "hidden": false,
            "historySize": 9001,
            "padding": "8, 8, 8, 8",
            "snapOnInput": true,
            "altGrAliasing": true,
            "startingDirectory": "%USERPROFILE%",
            "useAcrylic": false
        },
        {
            "guid": "{0caa0dad-35be-5f56-a8ff-afceeeaa6101}",
            "name": "Command Prompt",
            "commandline": "%SystemRoot%\\System32\\cmd.exe",
            "icon": "ms-appx:///ProfileIcons/{0caa0dad-35be-5f56-a8ff-afceeeaa6101}.png",
            "colorScheme": "Campbell",
            "antialiasingMode": "grayscale",
            "closeOnExit": "automatic",
            "cursorShape": "bar",
            "fontFace": "Cascadia Mono",
            "fontSize": 12,
            "hidden": false,
            "historySize": 9001,
            "padding": "8, 8, 8, 8",
            "snapOnInput": true,
            "altGrAliasing": true,
            "startingDirectory": "%USERPROFILE%",
            "useAcrylic": false
        }
    ],
    "schemes":
    [
        // A profile can override the following color scheme values:
        //   - "foreground"
        //   - "background"
        //   - "cursorColor"
        {
            "name": "Ottosson",
            "background": "#000000",
            "foreground": "#bebebe",
            "cursorColor": "#ffffff",
            "selectionBackground": "#92a4fd",
            "black": "#000000",
            "red": "#be2c21",
            "green": "#3fae3a",
            "yellow": "#be9a4a",
            "blue": "#204dbe",
            "purple": "#bb54be",
            "cyan": "#00a7b2",
            "white": "#bebebe",
            "brightBlack": "#808080",
            "brightRed": "#ff3e30",
            "brightGreen": "#58ea51",
            "brightYellow": "#ffc944",
            "brightBlue": "#2f6aff",
            "brightPurple": "#fc74ff",
            "brightCyan": "#00e1f0",
            "brightWhite": "#ffffff"
        },
        {
            "name": "Campbell",
            "foreground": "#CCCCCC",
            "background": "#0C0C0C",
            "cursorColor": "#FFFFFF",
            "black": "#0C0C0C",
            "red": "#C50F1F",
            "green": "#13A10E",
            "yellow": "#C19C00",
            "blue": "#0037DA",
            "purple": "#881798",
            "cyan": "#3A96DD",
            "white": "#CCCCCC",
            "brightBlack": "#767676",
            "brightRed": "#E74856",
            "brightGreen": "#16C60C",
            "brightYellow": "#F9F1A5",
            "brightBlue": "#3B78FF",
            "brightPurple": "#B4009E",
            "brightCyan": "#61D6D6",
            "brightWhite": "#F2F2F2"
        },
        {
            "name": "Campbell Powershell",
            "foreground": "#CCCCCC",
            "background": "#012456",
            "cursorColor": "#FFFFFF",
            "black": "#0C0C0C",
            "red": "#C50F1F",
            "green": "#13A10E",
            "yellow": "#C19C00",
            "blue": "#0037DA",
            "purple": "#881798",
            "cyan": "#3A96DD",
            "white": "#CCCCCC",
            "brightBlack": "#767676",
            "brightRed": "#E74856",
            "brightGreen": "#16C60C",
            "brightYellow": "#F9F1A5",
            "brightBlue": "#3B78FF",
            "brightPurple": "#B4009E",
            "brightCyan": "#61D6D6",
            "brightWhite": "#F2F2F2"
        },
        {
            "name": "Vintage",
            "foreground": "#C0C0C0",
            "background": "#000000",
            "cursorColor": "#FFFFFF",
            "black": "#000000",
            "red": "#800000",
            "green": "#008000",
            "yellow": "#808000",
            "blue": "#000080",
            "purple": "#800080",
            "cyan": "#008080",
            "white": "#C0C0C0",
            "brightBlack": "#808080",
            "brightRed": "#FF0000",
            "brightGreen": "#00FF00",
            "brightYellow": "#FFFF00",
            "brightBlue": "#0000FF",
            "brightPurple": "#FF00FF",
            "brightCyan": "#00FFFF",
            "brightWhite": "#FFFFFF"
        },
        {
            "name": "One Half Dark",
            "foreground": "#DCDFE4",
            "background": "#282C34",
            "cursorColor": "#FFFFFF",
            "black": "#282C34",
            "red": "#E06C75",
            "green": "#98C379",
            "yellow": "#E5C07B",
            "blue": "#61AFEF",
            "purple": "#C678DD",
            "cyan": "#56B6C2",
            "white": "#DCDFE4",
            "brightBlack": "#5A6374",
            "brightRed": "#E06C75",
            "brightGreen": "#98C379",
            "brightYellow": "#E5C07B",
            "brightBlue": "#61AFEF",
            "brightPurple": "#C678DD",
            "brightCyan": "#56B6C2",
            "brightWhite": "#DCDFE4"
        },
        {
            "name": "One Half Light",
            "foreground": "#383A42",
            "background": "#FAFAFA",
            "cursorColor": "#4F525D",
            "selectionBackground": "#383A42",
            "black": "#383A42",
            "red": "#E45649",
            "green": "#50A14F",
            "yellow": "#C18301",
            "blue": "#0184BC",
            "purple": "#A626A4",
            "cyan": "#0997B3",
            "white": "#FAFAFA",
            "brightBlack": "#4F525D",
            "brightRed": "#DF6C75",
            "brightGreen": "#98C379",
            "brightYellow": "#E4C07A",
            "brightBlue": "#61AFEF",
            "brightPurple": "#C577DD",
            "brightCyan": "#56B5C1",
            "brightWhite": "#FFFFFF"
        },
        {
            "name": "Solarized Dark",
            "foreground": "#839496",
            "background": "#002B36",
            "cursorColor": "#FFFFFF",
            "black": "#002B36",
            "red": "#DC322F",
            "green": "#859900",
            "yellow": "#B58900",
            "blue": "#268BD2",
            "purple": "#D33682",
            "cyan": "#2AA198",
            "white": "#EEE8D5",
            "brightBlack": "#073642",
            "brightRed": "#CB4B16",
            "brightGreen": "#586E75",
            "brightYellow": "#657B83",
            "brightBlue": "#839496",
            "brightPurple": "#6C71C4",
            "brightCyan": "#93A1A1",
            "brightWhite": "#FDF6E3"
        },
        {
            "name": "Solarized Light",
            "foreground": "#657B83",
            "background": "#FDF6E3",
            "cursorColor": "#002B36",
            "selectionBackground": "#2C4D57",
            "black": "#002B36",
            "red": "#DC322F",
            "green": "#859900",
            "yellow": "#B58900",
            "blue": "#268BD2",
            "purple": "#D33682",
            "cyan": "#2AA198",
            "white": "#EEE8D5",
            "brightBlack": "#073642",
            "brightRed": "#CB4B16",
            "brightGreen": "#586E75",
            "brightYellow": "#657B83",
            "brightBlue": "#839496",
            "brightPurple": "#6C71C4",
            "brightCyan": "#93A1A1",
            "brightWhite": "#FDF6E3"
        },
        {
            "name": "Tango Dark",
            "foreground": "#D3D7CF",
            "background": "#000000",
            "cursorColor": "#FFFFFF",
            "black": "#000000",
            "red": "#CC0000",
            "green": "#4E9A06",
            "yellow": "#C4A000",
            "blue": "#3465A4",
            "purple": "#75507B",
            "cyan": "#06989A",
            "white": "#D3D7CF",
            "brightBlack": "#555753",
            "brightRed": "#EF2929",
            "brightGreen": "#8AE234",
            "brightYellow": "#FCE94F",
            "brightBlue": "#729FCF",
            "brightPurple": "#AD7FA8",
            "brightCyan": "#34E2E2",
            "brightWhite": "#EEEEEC"
        },
        {
            "name": "Tango Light",
            "foreground": "#555753",
            "background": "#FFFFFF",
            "cursorColor": "#000000",
            "selectionBackground": "#141414",
            "black": "#000000",
            "red": "#CC0000",
            "green": "#4E9A06",
            "yellow": "#C4A000",
            "blue": "#3465A4",
            "purple": "#75507B",
            "cyan": "#06989A",
            "white": "#D3D7CF",
            "brightBlack": "#555753",
            "brightRed": "#EF2929",
            "brightGreen": "#8AE234",
            "brightYellow": "#FCE94F",
            "brightBlue": "#729FCF",
            "brightPurple": "#AD7FA8",
            "brightCyan": "#34E2E2",
            "brightWhite": "#EEEEEC"
        },
        {
            "name": "Dark+",
            "foreground": "#cccccc",
            "background": "#1e1e1e",
            "cursorColor": "#808080",
            "selectionBackground": "#ffffff",
            "black": "#000000",
            "red": "#cd3131",
            "green": "#0dbc79",
            "yellow": "#e5e510",
            "blue": "#2472c8",
            "purple": "#bc3fbc",
            "cyan": "#11a8cd",
            "white": "#e5e5e5",
            "brightBlack": "#666666",
            "brightRed": "#f14c4c",
            "brightGreen": "#23d18b",
            "brightYellow": "#f5f543",
            "brightBlue": "#3b8eea",
            "brightPurple": "#d670d6",
            "brightCyan": "#29b8db",
            "brightWhite": "#e5e5e5"
        },
        {
            "background": "#000000",
            "black": "#000000",
            "blue": "#0000AA",
            "brightBlack": "#555555",
            "brightBlue": "#5555FF",
            "brightCyan": "#55FFFF",
            "brightGreen": "#55FF55",
            "brightPurple": "#FF55FF",
            "brightRed": "#FF5555",
            "brightWhite": "#FFFFFF",
            "brightYellow": "#FFFF55",
            "cursorColor": "#00AA00",
            "cyan": "#00AAAA",
            "foreground": "#AAAAAA",
            "green": "#00AA00",
            "name": "CGA",
            "purple": "#AA00AA",
            "red": "#AA0000",
            "selectionBackground": "#FFFFFF",
            "white": "#AAAAAA",
            "yellow": "#AA5500"
        },
        {
            "background": "#000000",
            "black": "#000000",
            "blue": "#0000AA",
            "brightBlack": "#555555",
            "brightBlue": "#5555FF",
            "brightCyan": "#55FFFF",
            "brightGreen": "#55FF55",
            "brightPurple": "#FF55FF",
            "brightRed": "#FF5555",
            "brightWhite": "#FFFFFF",
            "brightYellow": "#FFFF55",
            "cursorColor": "#00AA00",
            "cyan": "#00AAAA",
            "foreground": "#AAAAAA",
            "green": "#00AA00",
            "name": "IBM 5153",
            "purple": "#AA00AA",
            "red": "#AA0000",
            "selectionBackground": "#FFFFFF",
            "white": "#AAAAAA",
            "yellow": "#C47E00"
        }
    ],
    "themes": [
        {
            "name": "light",
            "window": {
                "applicationTheme": "light"
            },
            "tab": {
                "background": "terminalBackground",
                "unfocusedBackground": "#00000000"
            },
            "tabRow": {
                "unfocusedBackground": "#FFFFFFFF"
            }
        },
        {
            "name": "dark",
            "window": {
                "applicationTheme": "dark"
            },
            "tab": {
                "background": "terminalBackground",
                "unfocusedBackground": "#00000000"
            },
            "tabRow": {
                "unfocusedBackground": "#333333FF"
            }
        },
        {
            "name": "system",
            "window": {
                "applicationTheme": "system"
            },
            "tab": {
                "background": "terminalBackground",
                "unfocusedBackground": "#00000000"
            }
        },
        {
            "name": "legacyDark",
            "tab": {
                "background": null,
                "unfocusedBackground": null
            },
            "window": {
                "applicationTheme": "dark"
            }
        },
        {
            "name": "legacyLight",
            "tab": {
                "background": null,
                "unfocusedBackground": null
            },
            "window": {
                "applicationTheme": "light"
            }
        },
        {
            "name": "legacySystem",
            "tab": {
                "background": null,
                "unfocusedBackground": null
            },
            "window": {
                "applicationTheme": "system"
            },
        }
    ],
    "actions":
    [
        // Application-level Commands
        { "command": "closeWindow", "id": "Terminal.CloseWindow" },
        { "command": "toggleFullscreen", "id": "Terminal.ToggleFullscreen" },
        { "command": "toggleFocusMode", "id": "Terminal.ToggleFocusMode" },
        { "command": "toggleAlwaysOnTop", "id": "Terminal.ToggleAlwaysOnTop" },
        { "command": "openNewTabDropdown", "id": "Terminal.OpenNewTabDropdown" },
        { "command": { "action": "openSettings", "target": "settingsUI" }, "id": "Terminal.OpenSettingsUI" },
        { "command": { "action": "openSettings", "target": "settingsFile" }, "id": "Terminal.OpenSettingsFile" },
        { "command": { "action": "openSettings", "target": "defaultsFile" }, "id": "Terminal.OpenDefaultSettingsFile" },
        { "command": { "action": "openSettings", "target": "directory" }, "id": "Terminal.OpenSettingsDirectory" },
        { "command": "find", "id": "Terminal.FindText" },
        { "command": { "action": "findMatch", "direction": "next" }, "id": "Terminal.FindNextMatch" },
        { "command": { "action": "findMatch", "direction": "prev" }, "id": "Terminal.FindPrevMatch" },
        { "command": "toggleShaderEffects", "id": "Terminal.ToggleShaderEffects" },
        { "command": "openTabColorPicker", "id": "Terminal.OpenTabColorPicker" },
        { "command": "renameTab", "id": "Terminal.RenameTab" },
        { "command": "openTabRenamer", "id": "Terminal.OpenTabRenamer" },
        { "command": "commandPalette", "id": "Terminal.ToggleCommandPalette" },
        { "command": "terminalChat", "id": "Terminal.OpenTerminalChat" },
        { "command": "identifyWindow", "id": "Terminal.IdentifyWindow" },
        { "command": "openWindowRenamer", "id": "Terminal.OpenWindowRenamer" },
        { "command": "quakeMode", "id": "Terminal.QuakeMode" },
        { "command": "openSystemMenu", "id": "Terminal.OpenSystemMenu" },
        { "command": "quit", "id": "Terminal.Quit" },
        { "command": "restoreLastClosed", "id": "Terminal.RestoreLastClosed" },
        { "command": "openAbout", "id": "Terminal.OpenAboutDialog" },
        { "command": "experimental.openTasks", "id": "Terminal.OpenTasks" },
        { "command": "quickFix", "id": "Terminal.QuickFix" },
        { "command": { "action": "showSuggestions", "source": "all"}, "id": "Terminal.Suggestions" },
        { "command": "openCWD", "id": "Terminal.OpenCWD" },

        // Tab Management
        // "command": "closeTab" is unbound by default.
        //   The closeTab command closes a tab without confirmation, even if it has multiple panes.
        { "command": "closeOtherTabs", "id": "Terminal.CloseOtherTabs" },
        { "command": "closeTabsAfter", "id": "Terminal.CloseTabsAfter" },
        { "command": { "action" : "moveTab", "direction": "forward" }, "id": "Terminal.MoveTabForward" },
        { "command": { "action" : "moveTab", "direction": "backward" }, "id": "Terminal.MoveTabBackward" },
        { "command": "newTab", "id": "Terminal.OpenNewTab" },
        { "command": "newWindow", "id": "Terminal.OpenNewWindow" },
        { "command": { "action": "newTab", "index": 0 }, "id": "Terminal.OpenNewTabProfile0" },
        { "command": { "action": "newTab", "index": 1 }, "id": "Terminal.OpenNewTabProfile1" },
        { "command": { "action": "newTab", "index": 2 }, "id": "Terminal.OpenNewTabProfile2" },
        { "command": { "action": "newTab", "index": 3 }, "id": "Terminal.OpenNewTabProfile3" },
        { "command": { "action": "newTab", "index": 4 }, "id": "Terminal.OpenNewTabProfile4" },
        { "command": { "action": "newTab", "index": 5 }, "id": "Terminal.OpenNewTabProfile5" },
        { "command": { "action": "newTab", "index": 6 }, "id": "Terminal.OpenNewTabProfile6" },
        { "command": { "action": "newTab", "index": 7 }, "id": "Terminal.OpenNewTabProfile7" },
        { "command": { "action": "newTab", "index": 8 }, "id": "Terminal.OpenNewTabProfile8" },
        { "command": "duplicateTab", "id": "Terminal.DuplicateTab" },
        { "command": "nextTab", "id": "Terminal.NextTab" },
        { "command": "prevTab", "id": "Terminal.PrevTab" },
        { "command": { "action": "switchToTab", "index": 0 }, "id": "Terminal.SwitchToTab0" },
        { "command": { "action": "switchToTab", "index": 1 }, "id": "Terminal.SwitchToTab1" },
        { "command": { "action": "switchToTab", "index": 2 }, "id": "Terminal.SwitchToTab2" },
        { "command": { "action": "switchToTab", "index": 3 }, "id": "Terminal.SwitchToTab3" },
        { "command": { "action": "switchToTab", "index": 4 }, "id": "Terminal.SwitchToTab4" },
        { "command": { "action": "switchToTab", "index": 5 }, "id": "Terminal.SwitchToTab5" },
        { "command": { "action": "switchToTab", "index": 6 }, "id": "Terminal.SwitchToTab6" },
        { "command": { "action": "switchToTab", "index": 7 }, "id": "Terminal.SwitchToTab7" },
        { "command": { "action": "switchToTab", "index": 4294967295 }, "id": "Terminal.SwitchToLastTab" },
        { "command": { "action": "moveTab", "window": "new" }, "id": "Terminal.MoveTabToNewWindow" },

        // Pane Management
        { "command": "closeOtherPanes", "id": "Terminal.CloseOtherPanes" },
        { "command": "closePane", "id": "Terminal.ClosePane" },
        { "command": { "action": "splitPane", "split": "up" }, "id": "Terminal.SplitPaneUp" },
        { "command": { "action": "splitPane", "split": "down" }, "id": "Terminal.SplitPaneDown" },
        { "command": { "action": "splitPane", "split": "left" }, "id": "Terminal.SplitPaneLeft" },
        { "command": { "action": "splitPane", "split": "right" }, "id": "Terminal.SplitPaneRight" },
        { "command": { "action": "splitPane", "splitMode": "duplicate", "split": "down" }, "id": "Terminal.DuplicatePaneDown" },
        { "command": { "action": "splitPane", "splitMode": "duplicate", "split": "right" }, "id": "Terminal.DuplicatePaneRight" },
        { "command": { "action": "splitPane", "splitMode": "duplicate", "split": "auto" }, "id": "Terminal.DuplicatePaneAuto" },
        { "command": { "action": "resizePane", "direction": "down" }, "id": "Terminal.ResizePaneDown" },
        { "command": { "action": "resizePane", "direction": "left" }, "id": "Terminal.ResizePaneLeft" },
        { "command": { "action": "resizePane", "direction": "right" }, "id": "Terminal.ResizePaneRight" },
        { "command": { "action": "resizePane", "direction": "up" }, "id": "Terminal.ResizePaneUp" },
        { "command": { "action": "moveFocus", "direction": "down" }, "id": "Terminal.MoveFocusDown" },
        { "command": { "action": "moveFocus", "direction": "left" }, "id": "Terminal.MoveFocusLeft" },
        { "command": { "action": "moveFocus", "direction": "right" }, "id": "Terminal.MoveFocusRight" },
        { "command": { "action": "moveFocus", "direction": "up" }, "id": "Terminal.MoveFocusUp" },
        { "command": { "action": "moveFocus", "direction": "previous" }, "id": "Terminal.MoveFocusPrevious" },
        { "command": { "action": "moveFocus", "direction": "previousInOrder" }, "id": "Terminal.MoveFocusPreviousInOrder" },
        { "command": { "action": "moveFocus", "direction": "nextInOrder" }, "id": "Terminal.MoveFocusNextInOrder" },
        { "command": { "action": "moveFocus", "direction": "first" }, "id": "Terminal.MoveFocusFirst" },
        { "command": { "action": "moveFocus", "direction": "parent" }, "id": "Terminal.MoveFocusParent" },
        { "command": { "action": "moveFocus", "direction": "child" }, "id": "Terminal.MoveFocusChild" },
        { "command": { "action": "swapPane", "direction": "down" }, "id": "Terminal.SwapPaneDown" },
        { "command": { "action": "swapPane", "direction": "left" }, "id": "Terminal.SwapPaneLeft" },
        { "command": { "action": "swapPane", "direction": "right" }, "id": "Terminal.SwapPaneRight" },
        { "command": { "action": "swapPane", "direction": "up" }, "id": "Terminal.SwapPaneUp" },
        { "command": { "action": "swapPane", "direction": "previous"}, "id": "Terminal.SwapPanePrevious" },
        { "command": { "action": "swapPane", "direction": "previousInOrder"}, "id": "Terminal.SwapPanePreviousInOrder" },
        { "command": { "action": "swapPane", "direction": "nextInOrder"}, "id": "Terminal.SwapPaneNextInOrder" },
        { "command": { "action": "swapPane", "direction": "first" }, "id": "Terminal.SwapPaneFirst" },
        { "command": "toggleBroadcastInput", "id": "Terminal.ToggleBroadcastInput" },
        { "command": "togglePaneZoom", "id": "Terminal.TogglePaneZoom" },
        { "command": "toggleSplitOrientation", "id": "Terminal.ToggleSplitOrientation" },
        { "command": "toggleReadOnlyMode", "id": "Terminal.ToggleReadOnlyMode" },
        { "command": "enableReadOnlyMode", "id": "Terminal.EnableReadOnlyMode" },
        { "command": "disableReadOnlyMode", "id": "Terminal.DisableReadOnlyMode" },
        { "command": { "action": "movePane", "index": 0 }, "id": "Terminal.MovePaneToTab0" },
        { "command": { "action": "movePane", "index": 1 }, "id": "Terminal.MovePaneToTab1" },
        { "command": { "action": "movePane", "index": 2 }, "id": "Terminal.MovePaneToTab2" },
        { "command": { "action": "movePane", "index": 3 }, "id": "Terminal.MovePaneToTab3" },
        { "command": { "action": "movePane", "index": 4 }, "id": "Terminal.MovePaneToTab4" },
        { "command": { "action": "movePane", "index": 5 }, "id": "Terminal.MovePaneToTab5" },
        { "command": { "action": "movePane", "index": 6 }, "id": "Terminal.MovePaneToTab6" },
        { "command": { "action": "movePane", "index": 7 }, "id": "Terminal.MovePaneToTab7" },
        { "command": { "action": "movePane", "index": 8 }, "id": "Terminal.MovePaneToTab8" },
        { "command": { "action": "movePane", "window": "new" }, "id": "Terminal.MovePaneToNewWindow" },
        { "command": "restartConnection", "id": "Terminal.RestartConnection" },
        { "command": { "action": "splitPane", "type": "snippets" }, "id": "Terminal.OpenSnippetsPane", "name": { "key": "SnippetsPaneCommandName" } },

        // Clipboard Integration
        { "command": { "action": "copy", "singleLine": false }, "id": "Terminal.CopyToClipboard" },
        { "command": "paste", "id": "Terminal.PasteFromClipboard" },
        { "command": "selectAll", "id": "Terminal.SelectAll" },
        { "command": "markMode", "id": "Terminal.ToggleMarkMode" },
        { "command": "toggleBlockSelection", "id": "Terminal.ToggleBlockSelection" },
        { "command": "switchSelectionEndpoint", "id": "Terminal.SwitchSelectionEndpoint" },
        { "command": "expandSelectionToWord", "id": "Terminal.ExpandSelectionToWord" },
        { "command": "showContextMenu", "id": "Terminal.ShowContextMenu" },

        // Web Search
        { "command": { "action": "searchWeb" }, "name": { "key": "SearchWebCommandKey" }, "id": "Terminal.SearchWeb" },

        // Scrollback
        { "command": "scrollDown", "id": "Terminal.ScrollDown" },
        { "command": "scrollDownPage", "id": "Terminal.ScrollDownPage" },
        { "command": "scrollUp", "id": "Terminal.ScrollUp" },
        { "command": "scrollUpPage", "id": "Terminal.ScrollUpPage" },
        { "command": "scrollToTop", "id": "Terminal.ScrollToTop" },
        { "command": "scrollToBottom", "id": "Terminal.ScrollToBottom" },
        { "command": { "action": "clearBuffer", "clear": "all" }, "id": "Terminal.ClearBuffer" },
        { "command": "exportBuffer", "id": "Terminal.ExportBuffer" },

        // Visual Adjustments
        { "command": { "action": "adjustFontSize", "delta": 1 }, "id": "Terminal.IncreaseFontSize" },
        { "command": { "action": "adjustFontSize", "delta": -1 }, "id": "Terminal.DecreaseFontSize" },
        { "command": "resetFontSize", "id": "Terminal.ResetFontSize" },

        // Other commands
        {
            // Select color scheme...
            "name": { "key": "SetColorSchemeParentCommandName" },
            "commands": [
                {
                    "iterateOn": "schemes",
                    "name": "${scheme.name}",
                    "command": { "action": "setColorScheme", "colorScheme": "${scheme.name}" }
                }
            ]
        },
        {
            // New tab...
            "name": { "key": "NewTabParentCommandName" },
            "commands": [
                {
                    "iterateOn": "profiles",
                    "icon": "${profile.icon}",
                    "name": "${profile.name}",
                    "command": { "action": "newTab", "profile": "${profile.name}" }
                }
            ]
        },
        {
            // Split pane...
            "name": { "key": "SplitPaneParentCommandName" },
            "commands": [
                {
                    "iterateOn": "profiles",
                    "icon": "${profile.icon}",
                    "name": "${profile.name}...",
                    "commands": [
                        {
                            "command": { "action": "splitPane", "profile": "${profile.name}", "split": "auto" }
                        },
                        {
                            "command": { "action": "splitPane", "profile": "${profile.name}", "split": "up" }
                        },
                        {
                            "command": { "action": "splitPane", "profile": "${profile.name}", "split": "down" }
                        },
                        {
                            "command": { "action": "splitPane", "profile": "${profile.name}", "split": "left" }
                        },
                        {
                            "command": { "action": "splitPane", "profile": "${profile.name}", "split": "right" }
                        }
                    ]
                }
            ]
        },
        {
            // Set opacity...
            "name": { "key": "SetOpacityParentCommandName" },
            "commands": [
                { "command": { "action": "adjustOpacity", "opacity": 0, "relative": false } },
                { "command": { "action": "adjustOpacity", "opacity": 25, "relative": false } },
                { "command": { "action": "adjustOpacity", "opacity": 50, "relative": false } },
                { "command": { "action": "adjustOpacity", "opacity": 75, "relative": false } },
                { "command": { "action": "adjustOpacity", "opacity": 100, "relative": false } }
            ]
        }
    ],
    "keybindings": [
        // Application-level Keys
        { "keys": "alt+f4", "id": "Terminal.CloseWindow" },
        { "keys": "alt+enter", "id": "Terminal.ToggleFullscreen" },
        { "keys": "f11", "id": "Terminal.ToggleFullscreen" },
        { "keys": "ctrl+shift+space", "id": "Terminal.OpenNewTabDropdown" },
        { "keys": "ctrl+,", "id": "Terminal.OpenSettingsUI" },
        { "keys": "ctrl+shift+,", "id": "Terminal.OpenSettingsFile" },
        { "keys": "ctrl+alt+,", "id": "Terminal.OpenDefaultSettingsFile" },
        { "keys": "ctrl+shift+f", "id": "Terminal.FindText" },
        { "keys": "ctrl+shift+p", "id": "Terminal.ToggleCommandPalette" },
        { "keys": "win+sc(41)", "id": "Terminal.QuakeMode" },
        { "keys": "alt+space", "id": "Terminal.OpenSystemMenu" },
        { "keys": "ctrl+shift+period", "id": "Terminal.Suggestions" },

        // Tab Management
        // "command": "closeTab" is unbound by default.
        //   The closeTab command closes a tab without confirmation, even if it has multiple panes.
        { "keys": "ctrl+shift+t", "id": "Terminal.OpenNewTab" },
        { "keys": "ctrl+shift+n", "id": "Terminal.OpenNewWindow" },
        { "keys": "ctrl+shift+1", "id": "Terminal.OpenNewTabProfile0" },
        { "keys": "ctrl+shift+2", "id": "Terminal.OpenNewTabProfile1" },
        { "keys": "ctrl+shift+3", "id": "Terminal.OpenNewTabProfile2" },
        { "keys": "ctrl+shift+4", "id": "Terminal.OpenNewTabProfile3" },
        { "keys": "ctrl+shift+5", "id": "Terminal.OpenNewTabProfile4" },
        { "keys": "ctrl+shift+6", "id": "Terminal.OpenNewTabProfile5" },
        { "keys": "ctrl+shift+7", "id": "Terminal.OpenNewTabProfile6" },
        { "keys": "ctrl+shift+8", "id": "Terminal.OpenNewTabProfile7" },
        { "keys": "ctrl+shift+9", "id": "Terminal.OpenNewTabProfile8" },
        { "keys": "ctrl+shift+d", "id": "Terminal.DuplicateTab" },
        { "keys": "ctrl+tab", "id": "Terminal.NextTab" },
        { "keys": "ctrl+shift+tab", "id": "Terminal.PrevTab" },
        { "keys": "ctrl+alt+1", "id": "Terminal.SwitchToTab0" },
        { "keys": "ctrl+alt+2", "id": "Terminal.SwitchToTab1" },
        { "keys": "ctrl+alt+3", "id": "Terminal.SwitchToTab2" },
        { "keys": "ctrl+alt+4", "id": "Terminal.SwitchToTab3" },
        { "keys": "ctrl+alt+5", "id": "Terminal.SwitchToTab4" },
        { "keys": "ctrl+alt+6", "id": "Terminal.SwitchToTab5" },
        { "keys": "ctrl+alt+7", "id": "Terminal.SwitchToTab6" },
        { "keys": "ctrl+alt+8", "id": "Terminal.SwitchToTab7" },
        { "keys": "ctrl+alt+9", "id": "Terminal.SwitchToLastTab" },

        // Pane Management
        { "keys": "ctrl+shift+w", "id": "Terminal.ClosePane" },
        { "keys": "alt+shift+-", "id": "Terminal.DuplicatePaneDown" },
        { "keys": "alt+shift+plus", "id": "Terminal.DuplicatePaneRight" },
        { "keys": "alt+shift+down", "id": "Terminal.ResizePaneDown" },
        { "keys": "alt+shift+left", "id": "Terminal.ResizePaneLeft" },
        { "keys": "alt+shift+right", "id": "Terminal.ResizePaneRight" },
        { "keys": "alt+shift+up", "id": "Terminal.ResizePaneUp" },
        { "keys": "alt+down", "id": "Terminal.MoveFocusDown" },
        { "keys": "alt+left", "id": "Terminal.MoveFocusLeft" },
        { "keys": "alt+right", "id": "Terminal.MoveFocusRight" },
        { "keys": "alt+up", "id": "Terminal.MoveFocusUp" },
        { "keys": "ctrl+alt+left", "id": "Terminal.MoveFocusPrevious" },

        // Clipboard Integration
        { "keys": "ctrl+shift+c", "id": "Terminal.CopyToClipboard" },
        { "keys": "ctrl+insert", "id": "Terminal.CopyToClipboard" },
        { "keys": "enter", "id": "Terminal.CopyToClipboard" },
        { "keys": "ctrl+shift+v", "id": "Terminal.PasteFromClipboard" },
        { "keys": "shift+insert", "id": "Terminal.PasteFromClipboard" },
        { "keys": "ctrl+shift+a", "id": "Terminal.SelectAll" },
        { "keys": "ctrl+shift+m", "id": "Terminal.ToggleMarkMode" },
        { "keys": "menu", "id": "Terminal.ShowContextMenu" },

        // Scrollback
        { "keys": "ctrl+shift+down", "id": "Terminal.ScrollDown" },
        { "keys": "ctrl+shift+pgdn", "id": "Terminal.ScrollDownPage" },
        { "keys": "ctrl+shift+up", "id": "Terminal.ScrollUp" },
        { "keys": "ctrl+shift+pgup", "id": "Terminal.ScrollUpPage" },
        { "keys": "ctrl+shift+home", "id": "Terminal.ScrollToTop" },
        { "keys": "ctrl+shift+end", "id": "Terminal.ScrollToBottom" },

        // Visual Adjustments
        { "keys": "ctrl+plus", "id": "Terminal.IncreaseFontSize" },
        { "keys": "ctrl+minus", "id": "Terminal.DecreaseFontSize" },
        { "keys": "ctrl+numpad_plus", "id": "Terminal.IncreaseFontSize" },
        { "keys": "ctrl+numpad_minus", "id": "Terminal.DecreaseFontSize" },
        { "keys": "ctrl+0", "id": "Terminal.ResetFontSize" },
        { "keys": "ctrl+numpad_0", "id": "Terminal.ResetFontSize" },
    ]
}
