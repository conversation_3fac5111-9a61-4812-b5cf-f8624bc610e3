INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-14 04:01:41,069 - WARNING - [__main__:<module>:117] - Could not import ProxySellerAPIClient from installed package. Trying local SDK path...
2025-05-14 04:01:41,071 - INFO - [__main__:<module>:138] - Successfully imported ProxySellerAPIClient from local SDK path: C:\main\proxyseller\user-api-python
2025-05-14 04:01:41,071 - INFO - [__main__:load_config_and_args:2045] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-14 04:01:41,072 - INFO - [__main__:load_config_and_args:2141] - Effective settings: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True
2025-05-14 04:01:41,073 - INFO - [__main__:<module>:2358] - Console logging level set to: INFO
2025-05-14 04:01:41,073 - INFO - [__main__:<module>:2359] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-14 04:01:41,074 - INFO - [__main__:<module>:2361] - ========================================
 Starting ProxySeller Firefox Setup (Resident SDK Mode)
========================================
2025-05-14 04:01:41,074 - INFO - [__main__:__init__:373] - Initializing ProxyContainerSetup...
2025-05-14 04:01:41,074 - INFO - [__main__:__init__:388] - Attempting to determine Firefox profile path...
2025-05-14 04:01:41,075 - INFO - [__main__:_find_firefox_profile:504] - Profile path determined from profiles.ini ('Profile1' in C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini): C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 04:01:41,076 - INFO - [__main__:__init__:413] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-14 04:01:41,235 - INFO - [__main__:run:1191] - Detected public IP for this script: **************
2025-05-14 04:01:41,236 - INFO - [__main__:run:1197] - Starting proxy container setup process (using SDK for resident proxies)...
2025-05-14 04:01:41,236 - INFO - [__main__:init_api_client:571] - Initializing ProxySeller API SDK client...
2025-05-14 04:01:42,056 - INFO - [__main__:init_api_client:591] - Successfully pinged Proxy-Seller API via SDK (timestamp: 1747220503).
2025-05-14 04:01:42,056 - INFO - [__main__:get_package_info_sdk:196] - Fetching resident proxy package information via SDK...
2025-05-14 04:01:42,254 - INFO - [__main__:get_package_info_sdk:201] - SDK Package Info: Active: True, Traffic Limit: 3221225472, Usage: 157459442, Left: 0
2025-05-14 04:01:42,255 - INFO - [__main__:init_api_client:605] - Successfully initialized resident proxy manager with SDK and fetched package info.
2025-05-14 04:01:42,255 - INFO - [__main__:fetch_proxies:627] - Fetching resident proxies for 5 containers using SDK...
2025-05-14 04:01:42,255 - INFO - [__main__:get_existing_lists_sdk:282] - Fetching existing resident proxy lists via SDK...
2025-05-14 04:01:45,253 - INFO - [__main__:get_existing_lists_sdk:300] - SDK: Found 8 existing resident proxy lists (direct list).
2025-05-14 04:01:45,254 - WARNING - [__main__:fetch_proxies:671] - List 1 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,254 - WARNING - [__main__:fetch_proxies:671] - List 2 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,255 - WARNING - [__main__:fetch_proxies:671] - List 3 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,255 - WARNING - [__main__:fetch_proxies:671] - List 4 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,255 - WARNING - [__main__:fetch_proxies:671] - List 5 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,256 - WARNING - [__main__:fetch_proxies:671] - List 6 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,256 - WARNING - [__main__:fetch_proxies:671] - List 7 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,257 - WARNING - [__main__:fetch_proxies:671] - List 8 geo data is of unexpected type: list. Skipping this list. Please fix in Proxy-Seller dashboard.
2025-05-14 04:01:45,257 - ERROR - [__main__:fetch_proxies:677] - No resident proxy lists with US or USA geo-targeting found among your existing lists. Please create or update lists in your Proxy-Seller dashboard with correct US geo-targeting (country='US' or 'USA'). Check for geo data format issues as warned above.
2025-05-14 04:01:45,257 - ERROR - [__main__:run:1210] - Failed to fetch or configure required resident proxies using SDK. Cannot proceed.
2025-05-14 04:01:45,258 - ERROR - [__main__:<module>:2378] - Main setup process reported failure or incomplete execution for core operations.
2025-05-14 04:01:45,258 - ERROR - [__main__:<module>:2446] - ==================================================
SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
Review logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
Any opened browser windows might be in an inconsistent state.
==================================================
2025-05-14 04:01:45,259 - INFO - [__main__:<module>:2450] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
