<#
    RunFile.ps1 - A helper script to run a file from its parent directory.

    If you are running a Python file (.py) that isn’t encoded in UTF-8,
    please ensure that your file includes an encoding declaration in its first lines,
    for example:

    # -*- coding: cp1252 -*-

    or convert your file to UTF-8.
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath,

    [switch]$Conda
)

# Change to the file's parent directory
$parent = Split-Path -Parent $FilePath
Set-Location $parent

# If the Conda switch is set, activate the environment "newenv"
if ($Conda) {
    conda activate newenv
}

# Determine file extension (lowercase)
$ext = [System.IO.Path]::GetExtension($FilePath).ToLower()

switch ($ext) {
    ".py" {
        # For Python scripts, call python.
        # If you get an encoding error, ensure the file has an encoding declaration or is saved as UTF-8.
        python $FilePath
        break
    }
    ".ps1" {
        # For PowerShell scripts, run the file
        & $FilePath
        break
    }
    ".bat" {
        # For Batch files, run via CMD
        cmd /c $FilePath
        break
    }
    default {
        # For any other file, try to execute directly
        & $FilePath
    }
}
