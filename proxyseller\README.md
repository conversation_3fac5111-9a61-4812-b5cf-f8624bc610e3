# ProxySeller Firefox Container Setup

This tool automates the setup of Firefox containers with unique proxies from proxy-seller.io. It integrates with the Firefox Multi-Account Containers extension and the FoxyProxy extension to provide a seamless experience.

## Prerequisites

- Firefox browser with:
  - [Multi-Account Containers extension](https://addons.mozilla.org/en-US/firefox/addon/multi-account-containers/)
  - [FoxyProxy extension](https://addons.mozilla.org/en-US/firefox/addon/foxyproxy-standard/)
- Python 3.6 or higher
- A proxy-seller.io account with API key

## Quick Start

1. Double-click on `start_proxy_setup.bat`
2. Enter your Proxy-Seller API key when prompted
3. Specify the number of containers you want to create
4. Choose your proxy type (http or socks5)
5. Follow the on-screen instructions

The script will:
- Connect to the Proxy-Seller API
- Fetch or create the proxies you need
- Set up Firefox containers
- Generate and import the FoxyProxy configuration

## Output Files

- `foxyproxy_config.json`: The FoxyProxy configuration file
- `proxy_container_summary.txt`: A human-readable summary of containers and proxies

## Manual Usage

If you prefer to run the script manually, you can use the following command:

```bash
python run_proxy_setup.py -k YOUR_API_KEY -n NUMBER_OF_CONTAINERS -t PROXY_TYPE
```

### Options

- `-k, --api-key`: Your Proxy-Seller API key (required)
- `-n, --num-containers`: Number of containers to create (default: 5)
- `-t, --proxy-type`: Type of proxy to use: http or socks5 (default: http)
- `-p, --profile-path`: Path to Firefox profile (optional)

## Troubleshooting

- **Firefox profile not found**: The script tries to find your Firefox profile automatically. If it fails, you can specify the path manually with the `-p` option.
- **Import failed**: If the automated import fails, the script will provide instructions for manual import.
- **Insufficient funds**: If you don't have enough funds in your Proxy-Seller account, the script will create as many proxies as possible.

## Files

- `start_proxy_setup.bat`: The main launcher (recommended)
- `run_proxy_setup.py`: The Python wrapper script
- `main.py`: The main script with all the functionality
- `geckodriver.exe`: Required for Firefox automation
