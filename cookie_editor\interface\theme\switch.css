.switch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 22px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.switch input:focus-visible + .slider {
  outline-style: auto;
}

.slider {
  border-radius: 22px;
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--switch-surface-neutral-color);
  -webkit-transition: .4s;
  transition: .4s;
  outline-offset: 2px;
}
.slider:before {
  border-radius: 50%;
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 3px;
  background-color: var(--switch-surface-handle-color);
  -webkit-transition: .2s;
  transition: .2s;
}

input:checked + .slider {
  background-color: var(--switch-surface-on-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--switch-surface-on-color);
}

input:checked + .slider:before {
  -webkit-transform: translateX(22px);
  -ms-transform: translateX(22px);
  transform: translateX(22px);
}