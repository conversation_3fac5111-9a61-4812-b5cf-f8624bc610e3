INFO: Detailed logs will be written to: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:22:56,885 - INFO - [__main__:load_config_and_args:2384] - Loaded settings from config.ini at C:\main\proxyseller\config.ini
2025-05-13 21:22:56,890 - INFO - [__main__:load_config_and_args:2386] - Config sections: []
2025-05-13 21:22:56,891 - INFO - [__main__:load_config_and_args:2387] - Config DEFAULT: {'API_KEY': 'ecccd2b73700761bf93684c1a431170f', 'USER_ID': '', 'NUM_CONTAINERS': '5', 'PROXY_TYPE': 'http', 'LOG_LEVEL': 'INFO'}
2025-05-13 21:22:56,893 - INFO - [__main__:load_config_and_args:2518] - Effective settings loaded: Num Containers: 5, Proxy Type: http, Log Level: INFO, Profile Path: Autodetect, Skip Import: False, API Key Set: True, User ID Set: False
2025-05-13 21:22:56,893 - INFO - [__main__:<module>:2562] - Console logging level set to: INFO
2025-05-13 21:22:56,893 - INFO - [__main__:<module>:2563] - Detailed file logging (DEBUG level) at: C:\main\proxyseller\proxy_setup.log
2025-05-13 21:22:56,895 - INFO - [__main__:<module>:2564] - ========================================
 Starting ProxySeller Firefox Setup 
========================================
2025-05-13 21:22:56,895 - INFO - [__main__:__init__:190] - Initializing ProxyContainerSetup...
2025-05-13 21:22:56,895 - INFO - [__main__:__init__:200] - Attempting to determine Firefox profile path...
2025-05-13 21:22:56,896 - INFO - [__main__:_find_firefox_profile:293] - Found profiles.ini at: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\profiles.ini
2025-05-13 21:22:56,898 - INFO - [__main__:_find_firefox_profile:308] - Found default profile section in profiles.ini: Profile1
2025-05-13 21:22:56,899 - INFO - [__main__:_find_firefox_profile:356] - Profile path determined from profiles.ini: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:22:56,899 - INFO - [__main__:__init__:229] - Using Firefox profile directory: C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\j7tsd9tu.default
2025-05-13 21:22:56,900 - INFO - [__main__:run:1383] - Starting proxy container setup process...
2025-05-13 21:22:57,798 - INFO - [__main__:init_api_client:430] - Successfully authenticated with Proxy-Seller API (ping successful).
2025-05-13 21:22:57,997 - WARNING - [__main__:init_api_client:442] - Could not parse balance from response: 11
2025-05-13 21:22:57,998 - INFO - [__main__:fetch_proxies:466] - Fetching proxies from Proxy-Seller...
2025-05-13 21:22:57,998 - INFO - [__main__:fetch_proxies:485] - Calling proxyList(type='ipv4', params={'countryCodes': ['US'], 'protocols': ['HTTP'], 'statuses': ['active']}) API method...
2025-05-13 21:22:57,999 - ERROR - [__main__:fetch_proxies:717] - Unexpected error fetching or formatting proxies: Api.proxyList() got an unexpected keyword argument 'countryCodes'
2025-05-13 21:22:58,000 - ERROR - [__main__:run:1395] - Failed to fetch or create required proxies, or no proxies are available. Cannot proceed with setup.
2025-05-13 21:22:58,001 - ERROR - [__main__:<module>:2578] - Main setup process reported failure or incomplete execution for core operations.
2025-05-13 21:22:58,002 - ERROR - [__main__:<module>:2658] - ==================================================
2025-05-13 21:22:58,002 - ERROR - [__main__:<module>:2659] - SCRIPT FINISHED WITH ERRORS (Exit Code: 1).
2025-05-13 21:22:58,003 - ERROR - [__main__:<module>:2660] - Please review the logs above and in 'C:\main\proxyseller\proxy_setup.log' for details.
2025-05-13 21:22:58,003 - ERROR - [__main__:<module>:2663] - Any browser windows opened might be in an inconsistent state or may not have launched.
2025-05-13 21:22:58,003 - ERROR - [__main__:<module>:2666] - ==================================================
2025-05-13 21:22:58,004 - INFO - [__main__:<module>:2672] - Initiating final script shutdown sequence...

Python script execution process finished. Final exit code: 1
