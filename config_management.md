# Configuration System Documentation

## Overview
This document explains our configuration management system, which enables hot-swapping of configuration values without requiring application restarts. The system is designed to be robust, with fallback mechanisms and support for multiple configuration streams.

## 1. Core Architecture

### Central Configuration Management
The system's core is implemented in `config_module.py` through the `SimpleConfigManager` class:

```python
# Main configuration managers
standard_config = SimpleConfigManager("config.json")
_banned_config = None  # Lazy loaded

def get_config(for_banned: bool = False) -> SimpleConfigManager:
    """Get the appropriate config manager."""
    global _banned_config
    if for_banned:
        if _banned_config is None:
            _banned_config = SimpleConfigManager("config_banned.json")
        return _banned_config
    return standard_config
```

Key features:
- Singleton pattern for configuration managers
- Lazy loading for banned configuration
- Automatic file monitoring and reloading
- Thread-safe operation

## 2. Configuration Streams

The system maintains two separate configuration streams:

1. **Standard Configuration** (`config.json`)
   - Used for normal operations
   - Default settings optimized for active accounts

2. **Banned Configuration** (`config_banned.json`)
   - Used for deactivated account operations
   - Specialized settings for restricted operations

### Hot-Swapping Implementation
Configuration values can be changed in real-time by:
1. Modifying the JSON configuration files
2. The system automatically detects changes and reloads
3. New values are immediately available to all components

Example of hot-swapping detection:
```python
def reload(self) -> None:
    """Load or reload the configuration file."""
    try:
        if self.config_file.exists():
            current_mtime = os.path.getmtime(self.config_file)
            if current_mtime > self.last_modified:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    self.config = self.default_config.copy()
                    self.config.update(loaded_config)
                self.last_modified = current_mtime
```

## 3. Fallback Mechanism

The system implements a multi-layer fallback strategy:

1. **Primary**: Use `config_module.py`
2. **Secondary**: Direct JSON file access
3. **Tertiary**: Default configuration values

Example implementation:
```python
def load_config() -> Dict[str, Any]:
    try:
        from config_module import standard_config
        return standard_config.config
    except Exception as e:
        console.print(f"[red]Error loading config via module: {e}[/red]")
        # Fallback to direct file loading
        try:
            if CONFIG_PATH.exists():
                with open(CONFIG_PATH, "r") as f:
                    config = json.load(f)
                    result = DEFAULT_CONFIG.copy()
                    result.update(config)
                    return result
            return DEFAULT_CONFIG.copy()
```

## 4. Default Values

Each component maintains its own default values that are merged with loaded configurations:

### Standard Configuration Defaults
```python
DEFAULT_CONFIG = {
    "TEST_MODE": True,
    "HEADLESS_MODE": False,
    "MAX_TIP_THRESHOLD": 4.0,
    "MAX_ORDERS_PER_BATCH": 30,
    "RECONNECT_TIMEOUT1": 30,
    "RECONNECT_TIMEOUT2": 60,
    "CLOSE_ALL_TIMEOUT": 60,
    "THEME": "dark",
}
```

### Banned Configuration Defaults
```python
DEFAULT_CONFIG = {
    "TEST_MODE": False,  # Default to False for deactivated account version
    "HEADLESS_MODE": True,  # Default to True for deactivated account version
    "MAX_TIP_THRESHOLD": 4.0,
    "MAX_ORDERS_PER_BATCH": 12,
    "CYCLE_SIZE": 3,
    "RECONNECT_TIMEOUT1": 30,
    "RECONNECT_TIMEOUT2": 60,
    "CLOSE_ALL_TIMEOUT": 60,
    "LOG_LEVEL": "INFO",
    "THEME": "dark",
}
```

## 5. Real-time Updates and State Management

The system handles real-time updates through:

1. **File Monitoring**: Automatic detection of configuration file changes
2. **Active Configuration Fetching**: Values are requested on-demand rather than cached
3. **State Updates**: Proper handling of state changes when configurations update

Example of state management:
```python
# Reload the config after menu system to get any updated values
saved_config = load_config()
if saved_config:
    # Store all configuration values in AppState
    app_state.test_mode = saved_config.get("TEST_MODE", app_state.test_mode)
    app_state.headless_mode = saved_config.get("HEADLESS_MODE", app_state.headless_mode)
    app_state.max_orders_per_batch = saved_config.get(
        "MAX_ORDERS_PER_BATCH", app_state.max_orders_per_batch
    )
```

## Important Notes for Developers and LLMs

1. **Configuration Updates**
   - Never cache configuration values locally
   - Always fetch values through the configuration manager
   - Use the fallback mechanism when implementing new features

2. **Hot-Swapping Considerations**
   - Configuration files can change at any time
   - Always use `get_config()` to access configuration values
   - Never modify configuration files programmatically during runtime

3. **Error Handling**
   - Implement proper error handling for configuration loading
   - Use the fallback mechanism when primary loading fails
   - Log configuration-related errors appropriately

4. **Best Practices**
   - Keep configuration files in the root directory
   - Use appropriate default values for new configuration options
   - Document any new configuration options added to the system

This configuration system ensures:
- Separation of normal and banned account configurations
- Real-time configuration updates without restarts
- Robust fallback mechanisms
- Configuration persistence across script restarts
- Thread-safe operation in multi-threaded environments
