@echo off
REM Setup script for Firefox Container Proxy Tool

REM Check Python
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in PATH. Please install it first.
    exit /b 1
)

REM Check Git
where git >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Git is not installed or not in PATH. Please install it first.
    exit /b 1
)

REM Check Selenium
python -c "import selenium" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing Selenium...
    pip install selenium
)

REM Check Firefox WebDriver (geckodriver)
where geckodriver >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Firefox WebDriver (geckodriver) is not installed or not in PATH.
    echo Please install it following the instructions at: https://github.com/mozilla/geckodriver/releases
    echo Add the geckodriver.exe location to your PATH environment variable.
    exit /b 1
)

echo Setup complete! You can now run the tool with:
echo python main.py -k YOUR_API_KEY -n 10
echo.
echo Options:
echo   -k, --api-key         Proxy-Seller API key (required)
echo   -n, --num-containers  Number of containers to create (default: 5)
echo   -t, --proxy-type      Type of proxy to use: http or socks5 (default: http)
echo   -p, --profile-path    Path to Firefox profile (optional)
