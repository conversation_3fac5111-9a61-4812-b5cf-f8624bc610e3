import json
import msvcrt
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from rich.console import Console
from rich.panel import Panel
from rich.style import Style

# Default configuration values
DEFAULT_CONFIG = {
    "TEST_MODE": False,  # Default to False for deactivated account version
    "HEADLESS_MODE": False,  # Default to True for deactivated account version
    "MAX_TIP_THRESHOLD": 4.0,
    "MAX_ORDERS_PER_BATCH": 12,  # Match Config.MAX_BATCH_SIZE in removalBanned3.0.py
    "CYCLE_SIZE": 3,  # Match Config.CYCLE_SIZE in removalBanned3.0.py
    "RECONNECT_TIMEOUT1": 30,
    "RECONNECT_TIMEOUT2": 45,
    "CLOSE_ALL_TIMEOUT": 60,
    "LOG_LEVEL": "INFO",  # Default log level
    "THEME": "dark",
    "ORDERS_PROCESSED": 10,  # New setting for number of orders to process
    "MAX_OPEN_TABS": 31,  # When this number of tabs is reached, all but main tab get closed
}

# You can remove the THEMES dictionary and keep just the default colors:

# Colors
MAIN_COLOR = "#D91400"  # Red
ACCENT_COLOR = "#FFD700"  # Gold
SUCCESS_COLOR = "#00AA00"  # Green
WARNING_COLOR = "#FFAA00"  # Yellow
INFO_COLOR = "#00AAFF"  # Cyan

# Remove the THEMES dictionary and replace with:
THEMES = {
    "default": {
        "main_color": MAIN_COLOR,
        "accent_color": ACCENT_COLOR,
        "success_color": SUCCESS_COLOR,
        "warning_color": WARNING_COLOR,
        "info_color": INFO_COLOR,
    }
}

# Help tooltips for each menu item
TOOLTIPS = {
    "TEST_MODE": "[dim italic #4A5568]\t⤑   Toggle between real and test operation.[/]",
    "HEADLESS_MODE": "[dim italic #4A5568]\t⤑   Run browser without visible window.[/]",
    "MAX_ORDERS_PER_BATCH": "[dim italic #4A5568]\t⤑   Maximum number of orders to process in one batch.[/]",
    "CYCLE_SIZE": "[dim italic #4A5568]\t⤑   Number of orders to use for opening chats.[/]",
    "MAX_TIP_THRESHOLD": "[dim italic #4A5568]\t⤑   Minimum tip amount for order to be considered eligible.[/]",
    "RECONNECT_TIMEOUT1": "[dim italic #4A5568]\t⤑   Seconds to wait before first reconnect/agent check.[/]",
    "RECONNECT_TIMEOUT2": "[dim italic #4A5568]\t⤑   Seconds to wait before second reconnect/agent check.[/]",
    "CLOSE_ALL_TIMEOUT": "[dim italic #4A5568]\t⤑   Seconds to wait before closing all order tabs.[/]",
    "LOG_LEVEL": "[dim italic #4A5568]\t⤑   Logging level selection (OFF, DEBUG, INFO, WARNING, ERROR).[/]",
    "ORDERS_PROCESSED": "[dim italic #4A5568]\t⤑   Number of orders to process in one round.[/]",
    "MAX_OPEN_TABS": "[dim italic #4A5568]\t⤑   Maximum number of browser tabs to keep open.[/]",
}

CONFIG_PATH = Path("config.json")
console = Console()


def load_config() -> Dict[str, Any]:
    """Load configuration from banned_config module or fallback to JSON file."""
    try:
        # Try to use config_module first
        from config_module import get_config

        banned_config = get_config(for_banned=True)

        # Get current values from module
        config = DEFAULT_CONFIG.copy()
        for key in config:
            config[key] = banned_config.get(key, config[key])

        return config
    except ImportError:
        # Fall back to direct file access
        try:
            config_path = Path("config_banned.json")
            if config_path.exists():
                with open(config_path, "r") as f:
                    config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    result = DEFAULT_CONFIG.copy()
                    result.update(config)
                    return result
            return DEFAULT_CONFIG.copy()
        except Exception as e:
            console.print(f"[red]Error loading config: {e}[/red]")
            return DEFAULT_CONFIG.copy()


def save_config(config: Dict[str, Any]) -> None:
    """Save configuration to banned_config module and JSON file for backup."""
    try:
        # Try to use config_module first
        from config_module import get_config

        banned_config = get_config(for_banned=True)

        # Update configuration in module
        for key, value in config.items():
            banned_config.set(key, value)

        # Also save to file as backup
        with open("config_banned.json", "w") as f:
            json.dump(config, f, indent=2)
    except ImportError:
        # Fall back to direct file access
        try:
            with open("config_banned.json", "w") as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            console.print(f"[red]Error saving config: {e}[/red]")


class MenuItem:
    def __init__(
        self,
        label: str,
        value_key: Optional[str] = None,
        action: Optional[str] = None,
        is_action: bool = False,
        section: str = "action",
        options: Optional[List[Any]] = None,
        value_format: Optional[str] = None,
        shortcut: Optional[str] = None,
    ):
        self.label = label
        self.value_key = value_key  # Key in config dict
        self.action = action  # Action to execute
        self.is_action = is_action  # True if this is an action item
        self.section = section  # Which section this item belongs to
        self.options = options  # List of possible values for toggleable items
        self.value_format = value_format  # Format string for displaying value
        self.shortcut = shortcut  # Shortcut key

    def format_value(self, value: Any) -> str:
        """Format the value for display."""
        if self.value_format:
            return self.value_format.format(value)
        if isinstance(value, bool):
            return "ON" if value else "OFF"
        return str(value)

    def get_value_color(self, value: Any, theme_colors: Dict[str, str]) -> str:
        """Get color for a value based on its type and value."""
        if isinstance(value, bool):
            return theme_colors["success_color"] if value else theme_colors["warning_color"]
        return theme_colors["info_color"]


class MenuSystem:
    def __init__(self):
        self.config = load_config()
        self.current_pos = 0
        self.editing = False
        self.pending_value = None  # For numeric entry
        self.cookies_found = has_cookies()
        self.last_render_time = 0
        self.frame_delay = 0.03  # Reduce flicker
        self.initialize_menu()
        self.apply_theme()

    def apply_theme(self):
        """Apply the current theme colors."""
        # Always use default theme
        self.theme_colors = THEMES["default"]

    def initialize_menu(self):
        """Create the menu structure."""
        self.menu_items = [
            # Upper section - action items
            MenuItem(
                "Sign into existing customer",
                action="login_manual",
                is_action=True,
                section="action",
                shortcut="1",
            ),
            MenuItem(
                "Sign in with saved cookies",
                action="login_cookies",
                is_action=True,
                section="action",
                shortcut="2",
            ),
            MenuItem("Exit", action="exit", is_action=True, section="action", shortcut="Q"),
            # Lower section - configuration items (adapted for deactivated accounts)
            MenuItem("Test Mode", "TEST_MODE", section="config"),
            MenuItem("Headless Mode", "HEADLESS_MODE", section="config"),
            MenuItem(
                "Max Open Tabs",
                "MAX_OPEN_TABS",
                options=[10, 15, 20, 25, 31, 40, 50],
                section="config",
                value_format="{0}",
            ),
            MenuItem(
                "Orders To Be Used For Chats",
                "CYCLE_SIZE",
                options=[1, 2, 3, 4, 5, 10, 15],
                section="config",
                value_format="{0}",
            ),
            MenuItem(
                "Chats Per Round",
                "ORDERS_PROCESSED",
                options=[5, 8, 10, 12, 15, 20],
                section="config",
                value_format="{0}",
            ),
            MenuItem(
                "Min Tip",
                "MAX_TIP_THRESHOLD",
                options=[3.0, 3.5, 4.0, 4.5, 5.0],
                value_format="${0}",
                section="config",
            ),
            MenuItem(
                "Log Level",
                "LOG_LEVEL",
                options=["OFF", "DEBUG", "INFO", "WARNING", "ERROR"],
                section="config",
            ),
            # Theme option removed
        ]

        # Create separate lists for each section
        self.action_items = [item for item in self.menu_items if item.section == "action"]
        self.config_items = [item for item in self.menu_items if item.section == "config"]

    def throttled_render(self):
        """Render the menu with a throttle to prevent flickering."""
        current_time = time.time()
        time_since_last_render = current_time - self.last_render_time

        if time_since_last_render >= self.frame_delay:
            self.draw_menu()
            self.last_render_time = current_time

    def format_item_line(self, is_selected, label_text, prefix, main_color):
        """Format a menu item line based on selection state."""
        if is_selected:
            label_style = f"bold {main_color}"
            line = f"[{label_style}]{prefix}{label_text}[/{label_style}]"
        else:
            label_style = "white"
            line = f"[{label_style}] {label_text}[/{label_style}]"  # Extra space for alignment
        return line, label_style

    def format_item_label(self, is_selected, item, prefix):
        """Format a menu item label based on selection and editing state."""
        if is_selected and not self.editing:
            label_text = f"{prefix}{item.label}:"
        else:
            label_text = f"{prefix} {item.label}:"  # Extra space for non-selected items
        return label_text

    def draw_menu(self):
        """Draw the menu on the screen using rich."""
        console.clear()

        # Apply current theme colors
        self.apply_theme()
        main_color = self.theme_colors["main_color"]
        info_color = self.theme_colors["info_color"]

        # Add title
        title = "DEACTIVATED ACCOUNT REMOVAL"
        subtitle = time.strftime("%Y-%m-%d %H:%M:%S")

        # Set up content for all sections
        content = []
        tooltip = None

        # Consistent arrow for all selected items
        selection_arrow = "❯  "

        # Draw action items (upper section)
        for idx, item in enumerate(self.action_items):
            is_selected = self.current_pos == idx

            # Escape any square brackets in the item label
            escaped_label = item.label.replace("[", "\\[").replace("]", "\\]")
            item_number = item.shortcut if item.shortcut else str(idx + 1)

            if is_selected:
                prefix = f"{selection_arrow}"
                # Add italic to selected item when in editing mode
                label_style = f"bold italic {main_color}" if self.editing else f"bold {main_color}"
                line = f"[{label_style}]{prefix}{item_number}. {escaped_label}[/{label_style}]"
            else:
                prefix = " "  # Same width as arrow for alignment
                # Make non-selected items bold white
                label_style = "bold white"
                # Add extra space for non-selected items to create the indentation effect
                line = f"[{label_style}] {prefix}{item_number}. {escaped_label}[/{label_style}]"

            content.append(line)

            # Set tooltip if this item is selected
            if is_selected and item.action:
                if item.action == "login_manual":
                    tooltip = "[dim italic #4A5568]\t⤑   Log in manually with a deactivated account[/]"
                elif item.action == "login_cookies":
                    tooltip = (
                        "[dim italic #4A5568]\t⤑   Log in automatically using saved deactivated account cookies[/]"
                    )
                elif item.action == "exit":
                    tooltip = "[dim italic #4A5568]\t⤑   Exit the application[/]"

        # Add config items in a visually separated format
        content.append("")  # Add space

        # Format all config items with better alignment
        label_width = max(len(item.label) for item in self.config_items) + 2  # +2 for padding

        for idx, item in enumerate(self.config_items):
            menu_idx = idx + len(self.action_items)  # Adjust index for full menu list
            is_selected = self.current_pos == menu_idx

            # Escape any square brackets in the item label
            escaped_label = item.label.replace("[", "\\[").replace("]", "\\]")

            # Use the same prefix for all items
            if is_selected:
                prefix = f"{selection_arrow}"
                # Add italic to selected item when in editing mode
                label_style = f"bold italic {main_color}" if self.editing else f"bold {main_color}"
            else:
                prefix = " "  # Same width as arrow for alignment
                # Make non-selected config items bold white
                label_style = "bold white"

            # Get the value and its formatted representation
            value = self.config.get(item.value_key, "N/A")
            formatted_value = item.format_value(value)
            # Escape any square brackets in the formatted value
            escaped_formatted_value = formatted_value.replace("[", "\\[").replace("]", "\\]")
            value_color = item.get_value_color(value, self.theme_colors)

            # For editing mode - highlight the value
            if is_selected and self.editing:
                # Show pending value if entering a number
                if self.pending_value is not None:
                    escaped_formatted_value = str(self.pending_value).replace("[", "\\[").replace("]", "\\]")
                value_style = f"bold {value_color} on #222222"
            else:
                value_style = value_color

            # Format with consistent alignment
            if is_selected:
                label_text = f"{prefix}{escaped_label}:"  # Selected item has no extra space
            else:
                label_text = f"{prefix} {escaped_label}:"  # Non-selected item has extra space

            padded_label = f"{label_text:<{label_width + 3}}"  # +3 for arrow and spacing

            # Create the line
            line = f"[{label_style}]{padded_label}[/{label_style}] [{value_style}]{escaped_formatted_value}[/{value_style}]"
            content.append(line)

            # Set tooltip if this item is selected
            if is_selected and item.value_key and item.value_key in TOOLTIPS:
                tooltip = TOOLTIPS[item.value_key]

        # Add tooltip first if available (instead of after cookie status)
        if tooltip:
            content.append("")  # Empty line
            # Keep tooltip in its original styling
            content.append(f"{tooltip}")

        # Add cookie status after tooltip
        cookie_found = has_cookies()
        content.append("")  # Empty line

        # Make "Deactivated Cookies:" bold white
        cookie_status = f"[bold white]Deactivated Cookies:[/bold white] {'[green]✓ Found[/green]' if cookie_found else '[red]✗ Not Found[/red]'}"
        content.append(cookie_status)

        # Status line - different based on mode
        content.append("")  # Empty line
        if self.editing:
            if isinstance(self.config.get(self.menu_items[self.current_pos].value_key), bool):
                status_line = f"[{info_color}]↑↓[/{info_color}]: Navigate | [{info_color}]←→[/{info_color}]: Toggle ON/OFF | [{info_color}]Enter[/{info_color}]: Confirm | [{info_color}]Esc[/{info_color}]: Cancel"
            elif self.menu_items[self.current_pos].options:
                status_line = f"[{info_color}]↑↓[/{info_color}]: Navigate | [{info_color}]←→[/{info_color}]: Change Value | [{info_color}]Enter[/{info_color}]: Confirm | [{info_color}]Esc[/{info_color}]: Cancel"
            else:
                status_line = f"Type a number | [{info_color}]Enter[/{info_color}]: Confirm | [{info_color}]Esc[/{info_color}]: Cancel"
        else:
            status_line = f"[{info_color}]↑↓[/{info_color}]: Navigate | [{info_color}]Enter[/{info_color}]: Select/Toggle | [{info_color}]1-3[/{info_color}]: Quick Select | [{info_color}]q[/{info_color}]: Quit"

        content.append(f"[dim]{status_line}[/dim]")

        # Theme indicator
        # theme = self.config.get("THEME", "default")
        # content.append(f"[{accent_color}]Current Theme: {theme}[/{accent_color}]")

        # Join all content
        panel_content = "\n".join(content)

        # Create the panel
        menu_panel = Panel(
            panel_content,
            title=f"[{main_color} bold]{title}[/{main_color} bold]",
            title_align="left",
            subtitle=f"[dim]{subtitle}[/dim]",
            subtitle_align="right",
            border_style=main_color,
            padding=(1, 2),
            width=80,
        )
        console.print(menu_panel)

    def toggle_value(self, direction: int = 1):
        """Toggle or cycle through values for the current menu item."""
        item = self.menu_items[self.current_pos]
        if not item.is_action and item.value_key:
            current_value = self.config.get(item.value_key)

            # Handle boolean toggle
            if isinstance(current_value, bool):
                self.config[item.value_key] = not current_value

            # Handle options list cycling
            elif item.options:
                try:
                    current_idx = item.options.index(current_value)
                except ValueError:
                    current_idx = 0

                # Cycle to next/prev value
                new_idx = (current_idx + direction) % len(item.options)
                self.config[item.value_key] = item.options[new_idx]

            # Save config after changing a value
            save_config(self.config)

            # If this is a log level change, immediately apply it
            if item.value_key == "LOG_LEVEL":
                # Special handling to apply log level immediately
                try:
                    # Direct approach without imports - execute Python code that calls the set_log_level
                    import subprocess

                    log_level = self.config[item.value_key]
                    python_code = f"import sys; sys.path.append('{os.path.dirname(os.path.abspath(__file__))}'); from removalBanned3 import set_log_level; set_log_level('{log_level}')"
                    subprocess.run(
                        ["python", "-c", python_code],
                        stderr=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                    )
                except Exception:
                    # If this approach fails, the main script will apply the log level on next run
                    pass

    def set_numeric_value(self, key_char):
        """Handle numeric input for editable fields."""
        # Initialize pending value if needed
        if self.pending_value is None:
            self.pending_value = ""

        # Add character to pending value
        if key_char.isdigit() or key_char == ".":
            self.pending_value += key_char

    def commit_pending_value(self):
        """Try to commit the pending numeric value to the current config item."""
        if self.pending_value is None:
            return

        item = self.menu_items[self.current_pos]
        try:
            # Convert to appropriate type
            current_value = self.config.get(item.value_key)
            if isinstance(current_value, int):
                value = int(self.pending_value)
                self.config[item.value_key] = value
            elif isinstance(current_value, float):
                value = float(self.pending_value)
                self.config[item.value_key] = value

            # Save config
            save_config(self.config)
        except ValueError:
            # Invalid number format, ignore
            pass

        # Clear pending value
        self.pending_value = None

    def navigate_sections(self, direction: int):
        """Move between items with circular navigation."""
        if direction > 0:  # Down
            self.current_pos = (self.current_pos + 1) % len(self.menu_items)
        elif direction < 0:  # Up
            self.current_pos = (self.current_pos - 1) % len(self.menu_items)

    def is_current_item_boolean(self) -> bool:
        """Check if the current menu item is a boolean value."""
        if self.current_pos >= len(self.menu_items):
            return False

        item = self.menu_items[self.current_pos]
        if not item.is_action and item.value_key:
            current_value = self.config.get(item.value_key)
            return isinstance(current_value, bool)
        return False

    def handle_key(self, key_char) -> Tuple[bool, Optional[str]]:
        """Handle keyboard input and return (continue_running, action)."""
        key = ord(key_char)

        # If editing and typing numbers
        if self.editing and not self.menu_items[self.current_pos].is_action:
            # Handle numeric entry
            if key >= 48 and key <= 57:  # 0-9
                self.set_numeric_value(chr(key))
                return True, None
            elif key == 46:  # Period for decimal
                self.set_numeric_value(".")
                return True, None

        # Special key sequences (arrow keys, etc.)
        if key in (0, 224):
            extended_key = msvcrt.getch()
            extended_key_code = ord(extended_key)

            # Handle arrow keys
            if extended_key_code == 72:  # Up arrow
                if not self.editing:
                    self.navigate_sections(-1)
            elif extended_key_code == 80:  # Down arrow
                if not self.editing:
                    self.navigate_sections(1)
            elif extended_key_code == 75 and (self.editing or self.is_current_item_boolean()):  # Left arrow
                self.toggle_value(-1)
            elif extended_key_code == 77 and (self.editing or self.is_current_item_boolean()):  # Right arrow
                self.toggle_value(1)
        else:
            # Handle regular keys
            if key == 27:  # ESC
                if self.editing:
                    self.editing = False
                    self.pending_value = None  # Clear any pending input
                else:
                    return False, "exit"
            elif key == 13:  # Enter
                item = self.menu_items[self.current_pos]

                if self.editing:
                    # Commit any pending value
                    if self.pending_value is not None:
                        self.commit_pending_value()
                    self.editing = False
                else:
                    # Handle action items
                    if item.is_action:
                        return True, item.action
                    else:
                        current_value = self.config.get(item.value_key)
                        # For boolean values, just toggle
                        if isinstance(current_value, bool):
                            self.toggle_value()
                        # For options, enter edit mode
                        elif item.options:
                            self.editing = True
                            self.pending_value = None
            elif key == 9:  # Tab - alternative to enter for editing
                item = self.menu_items[self.current_pos]
                if not item.is_action and not self.editing:
                    current_value = self.config.get(item.value_key)
                    # For boolean values, just toggle
                    if isinstance(current_value, bool):
                        self.toggle_value()
                    # For options, enter edit mode
                    elif item.options:
                        self.editing = True
                        self.pending_value = None
            elif key == 113 or key == 81:  # q or Q
                if not self.editing:
                    return False, "exit"
            # Quick select with number keys for action items
            elif key >= 49 and key <= 51 and not self.editing:  # Keys 1-3
                idx = key - 49  # Convert to 0-based index
                if idx < len(self.action_items):
                    return True, self.action_items[idx].action

        return True, None

    def run(self) -> Optional[str]:
        """Run the menu system and return the selected action."""
        # Initial render
        self.draw_menu()

        while True:
            # Check for keyboard input
            if msvcrt.kbhit():
                key_char = msvcrt.getch()
                continue_running, action = self.handle_key(key_char)

                # Only render when input is received to reduce flicker
                self.throttled_render()

                if not continue_running:
                    return None

                if action:
                    if action == "exit":
                        return None
                    return action

            # Small delay to prevent CPU hogging
            time.sleep(0.01)  # 10ms is more responsive than 50ms


def run_menu_system() -> Optional[str]:
    """Run the menu system and return the selected action."""
    menu = MenuSystem()
    return menu.run()


def has_cookies() -> bool:
    """Check if cookies_deactivated.pkl exists and is valid."""
    return os.path.exists("cookies_deactivated.pkl") and os.path.getsize("cookies_deactivated.pkl") > 0


if __name__ == "__main__":
    # For testing the menu standalone
    action = run_menu_system()
    print(f"Selected action: {action}")
